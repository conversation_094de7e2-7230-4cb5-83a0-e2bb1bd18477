# Topic Tree Builder

A Python application that builds hierarchical topic dependency trees and visualizes them as interactive graphs. The application uses SQLite for persistence and generates beautiful HTML visualizations using pyvis.

## Features

- 🌳 **Hierarchical Topic Trees**: Build dependency trees with configurable depth limits (0-5 by default)
- 💾 **SQLite Persistence**: All topics and dependencies are stored in a local SQLite database
- 🎨 **Interactive Visualization**: Generate beautiful HTML graphs with hover information and color-coded depth levels
- 🔄 **Incremental Updates**: Run multiple times to update and expand existing trees
- 📊 **Dependency Tracking**: Tracks both seen (existing) and unseen (new) dependencies
- 🛠️ **Extensible Design**: Easy to replace the stub data source with real APIs

## Installation

1. Clone or download the project files
2. Install dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage

Build a topic tree starting from "Machine Learning":

```bash
python topic_graph.py --root "Machine Learning"
```

### Advanced Options

```bash
# Specify maximum depth (default: 5)
python topic_graph.py --root "Statistics" --max-depth 3

# Custom output file
python topic_graph.py --root "Programming" --output my_graph.html

# Custom database location
python topic_graph.py --root "Mathematics" --db-path custom_topics.db

# Verbose logging
python topic_graph.py --root "Computer Science" --verbose

# Use only fallback data (skip OpenAI API)
python topic_graph.py --root "Mathematics" --fallback-only

# Clear database cache before starting (fresh data)
python topic_graph.py --root "Physics" --clear-cache
```

### Command Line Arguments

- `--root, -r`: **Required**. Root term to start building the topic tree from
- `--max-depth, -d`: Maximum depth to traverse (default: 5, meaning depths 0-5)
- `--output, -o`: Output HTML file for the graph visualization (default: graph.html)
- `--db-path`: Path to the SQLite database file (default: topics.db)
- `--verbose, -v`: Enable verbose logging
- `--fallback-only`: Use only enhanced stub data (skip OpenAI API calls)
- `--clear-cache`: Delete all existing topic entries from database before starting

## Output Files

After running the application, you'll get:

1. **`topics.db`** (or custom name): SQLite database containing all topics and dependencies
2. **`graph.html`** (or custom name): Interactive HTML visualization with both graph and data table
3. **`lib/`** directory: Supporting JavaScript libraries for the interactive visualization

## Database Schema

The SQLite database contains a single `topics` table with the following structure:

| Column | Type | Description |
|--------|------|-------------|
| `id` | INTEGER PRIMARY KEY | Auto-incrementing unique identifier |
| `depth` | INTEGER | Depth level in the tree (0 = root) |
| `term` | TEXT UNIQUE | The topic term (unique constraint) |
| `description` | TEXT | Description of the topic |
| `dependencies` | TEXT | JSON list of all prerequisite terms |

## Visualization Features

The generated HTML includes both an interactive graph and a detailed data table:

### 📊 Interactive Graph
- **Color-coded nodes** by depth level:
  - 🔴 Red: Root (depth 0)
  - 🟢 Teal: Depth 1
  - 🔵 Blue: Depth 2
  - 🟢 Green: Depth 3
  - 🟡 Yellow: Depth 4
  - 🟣 Pink: Depth 5

- **Interactive features**:
  - Hover over nodes to see descriptions
  - Drag nodes to rearrange the layout
  - Zoom and pan capabilities
  - Physics simulation for automatic layout
  - **🔍 Expandable fullscreen mode** - Click "Expand" to view graph in fullscreen
  - **🔄 Reset view** - Reset graph zoom and position
  - **⌨️ Keyboard shortcuts** - Press Escape to close expanded view

- **Edge types**:
  - Solid blue lines: Dependencies between topics in the graph
- **Performance optimizations**:
  - **Large graph handling**: Automatically disables physics and uses hierarchical layout for graphs >100 nodes
  - **Improved rendering**: Optimized for better performance with complex topic trees

### 📋 Data Table
- **Comprehensive topic listing** with all details
- **Color-coded depth indicators** matching the graph
- **Simplified dependency tracking** showing all prerequisite terms
- **Sortable and searchable** (browser-native table features)
- **Statistics summary** showing total topics, depth levels, and dependency counts

## Data Sources

The application uses a hybrid approach for fetching topic information:

### 🤖 **Primary: OpenAI API Integration**
- Uses OpenAI's `gpt-4o-mini` model (least expensive option)
- Requires `OPENAI_API_KEY` environment variable
- Provides high-quality, dynamic topic descriptions and dependencies
- Includes intelligent retry logic and rate limiting
- Automatically caches responses to minimize API costs
- **Optimized prompts** generate concise terms (4 words or fewer) for better readability

### 🛡️ **Fallback: Enhanced Stub Data**
- Comprehensive coverage of common academic topics
- Automatically used when OpenAI API is unavailable or quota exceeded
- Covers programming languages, computer science, mathematics, AI/ML, and more
- Ensures application always works regardless of API status

### ⚙️ **Configuration Options**
- `--fallback-only`: Skip OpenAI entirely and use only enhanced stub data
- Automatic fallback when API quota is exceeded or network issues occur
- Response caching for cost optimization and performance

### 🔧 **Setting Up OpenAI Integration**
1. Get an OpenAI API key from [platform.openai.com](https://platform.openai.com)
2. Set the environment variable: `export OPENAI_API_KEY="your-key-here"`
3. Run the application normally - it will automatically use OpenAI when available

### 📊 **Cost Optimization**
- **Multi-level caching** reduces redundant API calls:
  - **Database cache**: Persistent storage of previously fetched terms
  - **Memory cache**: Fast in-session access to recently used terms
- Uses the least expensive OpenAI model (gpt-4o-mini)
- **Automatic cache checking**: Never refetches terms that already exist in database
- Typical cost: <$0.01 per topic for most use cases
- Cache hit rates typically achieve 90%+ for repeated queries
- Use `--clear-cache` flag only when you want fresh data for all terms

## Examples

### Example 1: Machine Learning Tree
```bash
python topic_graph.py --root "Machine Learning"
```

This creates a tree exploring ML dependencies like Statistics, Linear Algebra, Calculus, and Programming.

### Example 2: Limited Depth Mathematics
```bash
python topic_graph.py --root "Mathematics" --max-depth 2
```

Creates a shallow tree focusing on immediate mathematical dependencies.

### Example 3: Custom Output
```bash
python topic_graph.py --root "Computer Science" --output cs_topics.html --verbose
```

Generates a computer science topic tree with detailed logging and custom output filename.

## Error Handling

The application includes comprehensive error handling for:
- Database connection issues
- Invalid command line arguments
- Missing dependencies
- Data fetching errors
- File I/O operations

## Requirements

- Python 3.8+
- pyvis (for graph visualization)
- openai (for LLM-based topic analysis)
- SQLite3 (included in Python standard library)
- OpenAI API key (optional - fallback data available)

## License

This project is provided as-is for educational and research purposes.
