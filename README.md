# Topic Tree Builder

A Python application that builds hierarchical topic dependency trees and visualizes them as interactive graphs. The application uses SQLite for persistence and generates beautiful HTML visualizations using pyvis.

## Features

- 🌳 **Hierarchical Topic Trees**: Build dependency trees with configurable depth limits (0-5 by default)
- 💾 **SQLite Persistence**: All topics and dependencies are stored in a local SQLite database
- 🎨 **Interactive Visualization**: Generate beautiful HTML graphs with hover information and color-coded depth levels
- 🔄 **Incremental Updates**: Run multiple times to update and expand existing trees
- 📊 **Dependency Tracking**: Tracks both seen (existing) and unseen (new) dependencies
- 🛠️ **Extensible Design**: Easy to replace the stub data source with real APIs

## Installation

1. Clone or download the project files
2. Install dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage

Build a topic tree starting from "Machine Learning":

```bash
python topic_graph.py --root "Machine Learning"
```

### Advanced Options

```bash
# Specify maximum depth (default: 5)
python topic_graph.py --root "Statistics" --max-depth 3

# Custom output file
python topic_graph.py --root "Programming" --output my_graph.html

# Custom database location
python topic_graph.py --root "Mathematics" --db-path custom_topics.db

# Verbose logging
python topic_graph.py --root "Computer Science" --verbose
```

### Command Line Arguments

- `--root, -r`: **Required**. Root term to start building the topic tree from
- `--max-depth, -d`: Maximum depth to traverse (default: 5, meaning depths 0-5)
- `--output, -o`: Output HTML file for the graph visualization (default: graph.html)
- `--db-path`: Path to the SQLite database file (default: topics.db)
- `--verbose, -v`: Enable verbose logging

## Output Files

After running the application, you'll get:

1. **`topics.db`** (or custom name): SQLite database containing all topics and dependencies
2. **`graph.html`** (or custom name): Interactive HTML visualization with both graph and data table
3. **`lib/`** directory: Supporting JavaScript libraries for the interactive visualization

## Database Schema

The SQLite database contains a single `topics` table with the following structure:

| Column | Type | Description |
|--------|------|-------------|
| `id` | INTEGER PRIMARY KEY | Auto-incrementing unique identifier |
| `depth` | INTEGER | Depth level in the tree (0 = root) |
| `term` | TEXT UNIQUE | The topic term (unique constraint) |
| `description` | TEXT | Description of the topic |
| `unseen_dependent_terms` | TEXT | JSON list of dependencies not yet in database |
| `seen_dependent_terms` | TEXT | JSON list of dependencies already in database |

## Visualization Features

The generated HTML includes both an interactive graph and a detailed data table:

### 📊 Interactive Graph
- **Color-coded nodes** by depth level:
  - 🔴 Red: Root (depth 0)
  - 🟢 Teal: Depth 1
  - 🔵 Blue: Depth 2
  - 🟢 Green: Depth 3
  - 🟡 Yellow: Depth 4
  - 🟣 Pink: Depth 5

- **Interactive features**:
  - Hover over nodes to see descriptions
  - Drag nodes to rearrange the layout
  - Zoom and pan capabilities
  - Physics simulation for automatic layout

- **Edge types**:
  - Solid blue lines: Dependencies that exist in the database
  - Dashed red lines: Dependencies not yet processed

### 📋 Data Table
- **Comprehensive topic listing** with all details
- **Color-coded depth indicators** matching the graph
- **Dependency tracking** showing both seen and unseen dependencies
- **Sortable and searchable** (browser-native table features)
- **Statistics summary** showing total topics, depth levels, and dependency counts

## Customizing the Data Source

The application currently uses stub data in the `fetch_term_info()` function. To integrate with real data sources:

1. Locate the `fetch_term_info()` method in `topic_graph.py`
2. Replace the stub implementation with calls to your preferred API or knowledge base
3. Ensure the function returns a tuple of `(description: str, dependent_terms: List[str])`

Example integration points:
- Wikipedia API
- Educational content APIs
- Custom knowledge bases
- Academic databases

## Examples

### Example 1: Machine Learning Tree
```bash
python topic_graph.py --root "Machine Learning"
```

This creates a tree exploring ML dependencies like Statistics, Linear Algebra, Calculus, and Programming.

### Example 2: Limited Depth Mathematics
```bash
python topic_graph.py --root "Mathematics" --max-depth 2
```

Creates a shallow tree focusing on immediate mathematical dependencies.

### Example 3: Custom Output
```bash
python topic_graph.py --root "Computer Science" --output cs_topics.html --verbose
```

Generates a computer science topic tree with detailed logging and custom output filename.

## Error Handling

The application includes comprehensive error handling for:
- Database connection issues
- Invalid command line arguments
- Missing dependencies
- Data fetching errors
- File I/O operations

## Requirements

- Python 3.6+
- pyvis (for graph visualization)
- SQLite3 (included in Python standard library)

## License

This project is provided as-is for educational and research purposes.
