
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .graph-container {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .graph-container.expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1000;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .graph-container.expanded .graph-content {
            height: calc(100vh - 120px) !important;
        }
        /* Target all pyvis network elements for proper expansion */
        .graph-container.expanded .graph-content > div,
        .graph-container.expanded #mynetworkid,
        .graph-container.expanded .card-body,
        .graph-container.expanded #vis-network,
        .graph-container.expanded canvas {
            height: 100% !important;
            width: 100% !important;
        }
        /* Ensure pyvis container divs expand properly */
        .graph-content > div,
        #mynetworkid,
        .card-body,
        #vis-network {
            height: 100%;
            width: 100%;
        }
        /* Make sure canvas fills its container but allows tooltips */
        canvas {
            display: block;
            position: relative;
        }
        /* Fix tooltip positioning */
        .vis-tooltip {
            position: absolute !important;
            z-index: 1000 !important;
            pointer-events: none !important;
        }
        .graph-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 5px;
        }
        .control-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .control-btn:hover {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .depth-filter {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.95);
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .depth-filter label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin: 0;
        }
        .depth-filter select {
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }
        .depth-filter select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .hover-display {
            position: absolute;
            top: 60px;
            left: 10px;
            z-index: 10;
            background: rgba(255,255,255,0.95);
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-size: 13px;
            min-width: 250px;
            max-width: 350px;
        }
        .hover-display h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        .hover-topic {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 4px;
        }
        .hover-depth {
            color: #666;
            font-size: 12px;
            margin-bottom: 6px;
        }
        .hover-description {
            color: #333;
            line-height: 1.4;
            font-size: 12px;
        }
        .hover-placeholder {
            color: #999;
            font-style: italic;
            font-size: 12px;
        }
        .graph-content {
            height: 500px;
            transition: height 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        /* Ensure pyvis elements fill the graph-content container */
        .graph-content > div {
            height: 100% !important;
            width: 100% !important;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        th.sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
        }
        th.sortable:hover {
            background-color: #e9ecef;
        }
        th.sortable::after {
            content: ' ↕️';
            font-size: 12px;
            opacity: 0.5;
        }
        th.sortable.sort-asc::after {
            content: ' ↑';
            opacity: 1;
        }
        th.sortable.sort-desc::after {
            content: ' ↓';
            opacity: 1;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f0f0;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="overlay" id="overlay" onclick="toggleExpand()"></div>

    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">1</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">1</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">0</div>
                <div class="stat-label">Topics with Prerequisites</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>

            <div class="graph-container" id="graphContainer">
                <div class="depth-filter">
                    <label for="depthSelect">Max Depth:</label>
                    <select id="depthSelect" onchange="filterByDepth()">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
                <div class="hover-display" id="hoverDisplay">
                    <h4>Topic Information</h4>
                    <div class="hover-placeholder">Hover over a topic to see details</div>
                </div>
                <div class="graph-controls">
                    <button class="control-btn" onclick="toggleExpand()" id="expandBtn">
                        🔍 Expand
                    </button>
                    <button class="control-btn" onclick="resetGraph()">
                        🔄 Reset View
                    </button>
                </div>
                <div class="graph-content">
                    <div id="mynetwork" class="card-body"></div>
                </div>
            </div>
        </div>

        
        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th class="sortable" onclick="sortTable(0)">ID</th>
                        <th class="sortable" onclick="sortTable(1)">Depth</th>
                        <th class="sortable" onclick="sortTable(2)">Term</th>
                        <th>Description</th>
                        <th>Prerequisites</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">1</td>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Programming</strong></td>
                    <td>A topic in the field of Programming</td>
                    <td>None</td>
                </tr>
                
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🎮 Graph Controls & Navigation</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                <div>
                    <h3 style="color: #555; margin-bottom: 10px;">🖱️ Mouse Controls</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Pan/Move:</strong> Click and drag on empty space</li>
                        <li><strong>Zoom:</strong> Mouse wheel or trackpad scroll</li>
                        <li><strong>Select Node:</strong> Click on any topic node</li>
                        <li><strong>Drag Node:</strong> Click and drag a node to reposition</li>
                        <li><strong>Multi-select:</strong> Ctrl+Click to select multiple nodes</li>
                        <li><strong>Box Select:</strong> Ctrl+Drag to select area</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">⌨️ Keyboard Shortcuts</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Escape:</strong> Close expanded view</li>
                        <li><strong>Delete:</strong> Remove selected nodes (visual only)</li>
                        <li><strong>Ctrl+A:</strong> Select all nodes</li>
                        <li><strong>Space:</strong> Fit graph to view</li>
                    </ul>
                </div>

                <div>
                    <h3 style="color: #555; margin-bottom: 10px;">🔧 Control Buttons</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>🔍 Expand:</strong> Enter fullscreen mode for detailed exploration</li>
                        <li><strong>🔄 Reset View:</strong> Fit all nodes to view and reset zoom</li>
                        <li><strong>Max Depth:</strong> Filter topics by maximum depth level</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">📌 Node Interactions</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Hover:</strong> View topic description and depth</li>
                        <li><strong>Pin/Unpin:</strong> Double-click to fix node position</li>
                        <li><strong>Physics:</strong> Nodes automatically arrange themselves</li>
                        <li><strong>Clustering:</strong> Related topics group together</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">🎯 Visual Features</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Color Coding:</strong> Each depth level has unique color</li>
                        <li><strong>Node Size:</strong> Larger nodes = shallower depth (more fundamental)</li>
                        <li><strong>Edge Direction:</strong> Arrows point from topic to prerequisites</li>
                        <li><strong>Smooth Animations:</strong> Physics-based movement and transitions</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 8px; border-left: 4px solid #007bff;">
                <h4 style="margin: 0 0 10px 0; color: #0056b3;">💡 Pro Tips</h4>
                <ul style="margin: 0; line-height: 1.6;">
                    <li><strong>Large Graphs:</strong> Use depth filter to focus on specific levels</li>
                    <li><strong>Exploration:</strong> Start with depth 1-2, then expand as needed</li>
                    <li><strong>Performance:</strong> Lower depths = faster rendering and interaction</li>
                    <li><strong>Fullscreen:</strong> Use expand mode for complex topic trees</li>
                    <li><strong>Navigation:</strong> Follow prerequisite arrows to understand learning paths</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function toggleExpand() {
            const container = document.getElementById('graphContainer');
            const overlay = document.getElementById('overlay');
            const expandBtn = document.getElementById('expandBtn');

            if (container.classList.contains('expanded')) {
                container.classList.remove('expanded');
                overlay.classList.remove('active');
                expandBtn.innerHTML = '🔍 Expand';
                document.body.style.overflow = 'auto';

                // Force resize of all network elements after collapse
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            } else {
                container.classList.add('expanded');
                overlay.classList.add('active');
                expandBtn.innerHTML = '❌ Close';
                document.body.style.overflow = 'hidden';

                // Force resize of all network elements after expansion
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            }
        }

        function resizeNetworkElements() {
            // Force resize of all pyvis network elements
            const networkElements = [
                document.getElementById('mynetworkid'),
                document.querySelector('.card-body'),
                document.querySelector('#vis-network'),
                document.querySelector('canvas')
            ];

            networkElements.forEach(element => {
                if (element) {
                    // Trigger a reflow by temporarily changing display
                    const originalDisplay = element.style.display;
                    element.style.display = 'none';
                    element.offsetHeight; // Trigger reflow
                    element.style.display = originalDisplay;
                }
            });

            // Redraw and fit the network
            if (window.network) {
                if (window.network.redraw) {
                    window.network.redraw();
                }
                if (window.network.fit) {
                    window.network.fit();
                }
                // Force canvas resize if available
                if (window.network.canvas && window.network.canvas.setSize) {
                    const container = document.querySelector('.graph-content');
                    if (container) {
                        window.network.canvas.setSize(container.offsetWidth, container.offsetHeight);
                    }
                }
            }
        }

        function resetGraph() {
            // Reset the network view and ensure proper sizing
            resizeNetworkElements();
        }

        // Close expanded view with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const container = document.getElementById('graphContainer');
                if (container.classList.contains('expanded')) {
                    toggleExpand();
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            setTimeout(() => {
                resizeNetworkElements();
            }, 100);
        });

        // Initial resize after page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                resizeNetworkElements();
                initializeDepthFilter();
                initializeHoverDisplay();
                sortTable(0); // Sort by ID by default
            }, 500);
        });

        // Store original topics data for filtering
        let allTopicsData = [[1, 0, "Programming", "A topic in the field of Programming", "[]"]];
        let currentMaxDepth = 2; // Default to depth 2

        function initializeDepthFilter() {
            const depthSelect = document.getElementById('depthSelect');
            const maxDepth = Math.max(...allTopicsData.map(t => t[1])); // Get max depth from data

            // Populate dropdown options
            for (let i = 0; i <= maxDepth; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = i;
                if (i === currentMaxDepth) {
                    option.selected = true;
                }
                depthSelect.appendChild(option);
            }

            // Apply initial filter
            filterByDepth();
        }

        function filterByDepth() {
            const depthSelect = document.getElementById('depthSelect');
            currentMaxDepth = parseInt(depthSelect.value);

            // Filter topics by selected max depth
            const filteredTopics = allTopicsData.filter(topic => topic[1] <= currentMaxDepth);

            // Rebuild the network with filtered data
            rebuildNetwork(filteredTopics);
        }

        function rebuildNetwork(filteredTopics) {
            if (!window.network) return;

            // Clear existing network
            window.network.setData({nodes: [], edges: []});

            // Color scheme for different depths
            const depthColors = {
                0: "#ff6b6b",  // Red for root
                1: "#4ecdc4",  // Teal for depth 1
                2: "#45b7d1",  // Blue for depth 2
                3: "#96ceb4",  // Green for depth 3
                4: "#feca57",  // Yellow for depth 4
                5: "#ff9ff3",  // Pink for depth 5
            };

            // Create nodes without titles (using hover display instead)
            const nodes = filteredTopics.map(topic => {
                const [id, depth, term, description] = topic;
                const color = depthColors[depth] || "#cccccc";
                return {
                    id: term,
                    label: term,
                    color: color,
                    size: 20 + (5 - depth) * 5
                };
            });

            // Create edges with arrows
            const edges = [];
            filteredTopics.forEach(topic => {
                const [id, depth, term, description, prereqsJson] = topic;
                try {
                    const prerequisites = JSON.parse(prereqsJson || '[]');
                    prerequisites.forEach(prereq => {
                        // Only add edge if prerequisite is also in filtered data
                        if (filteredTopics.some(t => t[2] === prereq)) {
                            edges.push({
                                from: term,
                                to: prereq,
                                color: "blue",
                                width: 2,
                                arrows: {
                                    to: {
                                        enabled: true,
                                        scaleFactor: 1.2
                                    }
                                }
                            });
                        }
                    });
                } catch (e) {
                    console.error('Error parsing prerequisites for', term, e);
                }
            });

            // Update network
            window.network.setData({nodes: nodes, edges: edges});
            window.network.fit();

            // Reinitialize hover events after rebuilding
            initializeHoverDisplay();
        }

        function initializeHoverDisplay() {
            // Set up hover event listeners for the network
            if (window.network) {
                window.network.on("hoverNode", function(params) {
                    const nodeId = params.node;
                    const topicData = allTopicsData.find(t => t[2] === nodeId);

                    if (topicData) {
                        const [id, depth, term, description, prereqsJson] = topicData;
                        let prerequisites = [];
                        try {
                            prerequisites = JSON.parse(prereqsJson || '[]');
                        } catch (e) {
                            prerequisites = [];
                        }

                        updateHoverDisplay(term, depth, description, prerequisites);
                    }
                });

                window.network.on("blurNode", function(params) {
                    resetHoverDisplay();
                });
            }
        }

        function updateHoverDisplay(term, depth, description, prerequisites) {
            const hoverDisplay = document.getElementById('hoverDisplay');
            const prereqsText = prerequisites.length > 0 ? prerequisites.join(', ') : 'None';

            hoverDisplay.innerHTML = `
                <h4>Topic Information</h4>
                <div class="hover-topic">${term}</div>
                <div class="hover-depth">Depth: ${depth}${depth === 0 ? ' (Root)' : ''}</div>
                <div class="hover-description">${description || 'No description available'}</div>
                <div style="margin-top: 8px; font-size: 11px; color: #666;">
                    <strong>Prerequisites:</strong> ${prereqsText}
                </div>
            `;
        }

        function resetHoverDisplay() {
            const hoverDisplay = document.getElementById('hoverDisplay');
            hoverDisplay.innerHTML = `
                <h4>Topic Information</h4>
                <div class="hover-placeholder">Hover over a topic to see details</div>
            `;
        }

        let sortDirection = {};

        function sortTable(columnIndex) {
            const table = document.querySelector('table tbody');
            const rows = Array.from(table.rows);
            const header = document.querySelectorAll('th.sortable')[columnIndex];

            // Toggle sort direction
            const currentDirection = sortDirection[columnIndex] || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            sortDirection[columnIndex] = newDirection;

            // Update header classes
            document.querySelectorAll('th.sortable').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc');
            });
            header.classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');

            // Sort rows
            rows.sort((a, b) => {
                let aVal = a.cells[columnIndex].textContent.trim();
                let bVal = b.cells[columnIndex].textContent.trim();

                // Convert to numbers for ID and Depth columns
                if (columnIndex === 0 || columnIndex === 1) {
                    aVal = parseInt(aVal) || 0;
                    bVal = parseInt(bVal) || 0;
                }

                if (aVal < bVal) return newDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return newDirection === 'asc' ? 1 : -1;
                return 0;
            });

            // Reorder table rows
            rows.forEach(row => table.appendChild(row));
        }
    </script>

    <script src="lib/bindings/utils.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>
<script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>
<script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "black"}, "id": "Programming", "label": "Programming", "shape": "dot", "size": 45}]);
                  edges = new vis.DataSet([]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 100}}, "edges": {"arrows": {"to": {"enabled": true, "scaleFactor": 1.2}}, "smooth": {"enabled": true, "type": "continuous"}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
        <script>
        // Make network globally accessible for filtering
        setTimeout(() => {
            if (typeof network !== 'undefined') {
                window.network = network;
            }
        }, 100);
        </script>
        
</body>
</html>
        