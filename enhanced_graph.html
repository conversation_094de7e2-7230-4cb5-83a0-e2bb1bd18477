
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f0f0;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">14</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">4</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">13</div>
                <div class="stat-label">Topics with Dependencies</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>
            <div id="mynetwork" class="card-body"></div>
        </div>

        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th>Depth</th>
                        <th>Term</th>
                        <th>Description</th>
                        <th>Seen Dependencies</th>
                        <th>Unseen Dependencies</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Machine Learning</strong></td>
                    <td>A field of artificial intelligence that uses statistical techniques to give computer systems the ability to learn.</td>
                    <td><span style="color: blue;">Statistics, Linear Algebra, Calculus, Programming</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Calculus</strong></td>
                    <td>The mathematical study of continuous change.</td>
                    <td><span style="color: blue;">Mathematics, Limits, Derivatives</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Linear Algebra</strong></td>
                    <td>The branch of mathematics concerning linear equations and linear functions.</td>
                    <td><span style="color: blue;">Mathematics, Vector Spaces</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Programming</strong></td>
                    <td>The process of creating a set of instructions that tell a computer how to perform a task.</td>
                    <td><span style="color: blue;">Computer Science, Algorithms</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Statistics</strong></td>
                    <td>The discipline that concerns the collection, organization, analysis, interpretation, and presentation of data.</td>
                    <td><span style="color: blue;">Probability Theory, Mathematics</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Algorithms</strong></td>
                    <td>A finite sequence of well-defined instructions for solving a problem.</td>
                    <td><span style="color: blue;">Computer Science, Mathematics</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Computer Science</strong></td>
                    <td>The study of algorithmic processes and computational systems.</td>
                    <td><span style="color: blue;">Mathematics, Logic</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Derivatives</strong></td>
                    <td>A measure of how a function changes as its input changes.</td>
                    <td><span style="color: blue;">Calculus, Limits</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Limits</strong></td>
                    <td>A fundamental concept in calculus and analysis concerning the behavior of a function near a particular input.</td>
                    <td><span style="color: blue;">Mathematics</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Mathematics</strong></td>
                    <td>The abstract science of number, quantity, and space.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Probability Theory</strong></td>
                    <td>The branch of mathematics concerned with probability.</td>
                    <td><span style="color: blue;">Mathematics, Set Theory</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Vector Spaces</strong></td>
                    <td>A collection of objects called vectors that can be added together and multiplied by numbers.</td>
                    <td><span style="color: blue;">Mathematics, Linear Algebra</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Logic</strong></td>
                    <td>The systematic study of the principles of valid inference and correct reasoning.</td>
                    <td><span style="color: blue;">Mathematics</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Set Theory</strong></td>
                    <td>The branch of mathematical logic that studies sets.</td>
                    <td><span style="color: blue;">Mathematics, Logic</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                </tbody>
            </table>
        </div>
    </div>

    <script src="lib/bindings/utils.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>
<script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>
<script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "black"}, "id": "Machine Learning", "label": "Machine Learning", "shape": "dot", "size": 45, "title": "Depth: 0\nDescription: A field of artificial intelligence that uses statistical techniques to give computer systems the ability to learn."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Calculus", "label": "Calculus", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The mathematical study of continuous change."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Linear Algebra", "label": "Linear Algebra", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The branch of mathematics concerning linear equations and linear functions."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Programming", "label": "Programming", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The process of creating a set of instructions that tell a computer how to perform a task."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Statistics", "label": "Statistics", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The discipline that concerns the collection, organization, analysis, interpretation, and presentation of data."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Algorithms", "label": "Algorithms", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: A finite sequence of well-defined instructions for solving a problem."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Computer Science", "label": "Computer Science", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: The study of algorithmic processes and computational systems."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Derivatives", "label": "Derivatives", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: A measure of how a function changes as its input changes."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Limits", "label": "Limits", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: A fundamental concept in calculus and analysis concerning the behavior of a function near a particular input."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Mathematics", "label": "Mathematics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: The abstract science of number, quantity, and space."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Probability Theory", "label": "Probability Theory", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: The branch of mathematics concerned with probability."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Vector Spaces", "label": "Vector Spaces", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: A collection of objects called vectors that can be added together and multiplied by numbers."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Logic", "label": "Logic", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: The systematic study of the principles of valid inference and correct reasoning."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Set Theory", "label": "Set Theory", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: The branch of mathematical logic that studies sets."}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Limits", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Derivatives", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Vector Spaces", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Computer Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Computer Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Computer Science", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Computer Science", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Derivatives", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Derivatives", "to": "Limits", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Spaces", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Spaces", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Logic", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 100}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
</body>
</html>
        