
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .graph-container {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .graph-container.expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1000;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .graph-container.expanded .graph-content {
            height: calc(100vh - 120px) !important;
        }
        /* Target all pyvis network elements for proper expansion */
        .graph-container.expanded .graph-content > div,
        .graph-container.expanded #mynetworkid,
        .graph-container.expanded .card-body,
        .graph-container.expanded #vis-network,
        .graph-container.expanded canvas {
            height: 100% !important;
            width: 100% !important;
        }
        /* Ensure pyvis container divs expand properly */
        .graph-content > div,
        #mynetworkid,
        .card-body,
        #vis-network {
            height: 100%;
            width: 100%;
        }
        /* Make sure canvas fills its container */
        canvas {
            display: block;
        }
        .graph-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 5px;
        }
        .control-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .control-btn:hover {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .graph-content {
            height: 500px;
            transition: height 0.3s ease;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f0f0;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="overlay" id="overlay" onclick="toggleExpand()"></div>

    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">12</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">3</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">11</div>
                <div class="stat-label">Topics with Dependencies</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>

            <div class="graph-container" id="graphContainer">
                <div class="graph-controls">
                    <button class="control-btn" onclick="toggleExpand()" id="expandBtn">
                        🔍 Expand
                    </button>
                    <button class="control-btn" onclick="resetGraph()">
                        🔄 Reset View
                    </button>
                </div>
                <div class="graph-content">
                    <div id="mynetwork" class="card-body"></div>
                </div>
            </div>
        </div>

        
        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th>Depth</th>
                        <th>Term</th>
                        <th>Description</th>
                        <th>Dependencies</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Machine Learning</strong></td>
                    <td>A field of artificial intelligence that uses statistical techniques for learning from data.</td>
                    <td>Statistics, Linear Algebra, Calculus, Programming Fundamentals</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Calculus</strong></td>
                    <td>The mathematical study of continuous change.</td>
                    <td>Mathematics, Limits, Functions</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Linear Algebra</strong></td>
                    <td>The branch of mathematics concerning linear equations and linear functions.</td>
                    <td>Mathematics, Vector Spaces</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Programming Fundamentals</strong></td>
                    <td>Basic concepts of computer programming including variables, control structures, and algorithms.</td>
                    <td>Logic, Mathematics, Computer Science</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Statistics</strong></td>
                    <td>The discipline that concerns collection, organization, analysis, and interpretation of data.</td>
                    <td>Mathematics, Probability Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Computer Science</strong></td>
                    <td>The study of algorithmic processes and computational systems.</td>
                    <td>Mathematics, Logic, Discrete Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Functions</strong></td>
                    <td>Mathematical relations that assign exactly one output to each input.</td>
                    <td>Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Limits</strong></td>
                    <td>A fundamental concept describing the behavior of functions near specific points.</td>
                    <td>Mathematics, Functions</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Logic</strong></td>
                    <td>The systematic study of the principles of valid inference and reasoning.</td>
                    <td>Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Mathematics</strong></td>
                    <td>The abstract science of number, quantity, and space.</td>
                    <td>None</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Probability Theory</strong></td>
                    <td>The branch of mathematics concerned with probability and random phenomena.</td>
                    <td>Mathematics, Set Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Vector Spaces</strong></td>
                    <td>A collection of objects called vectors that can be added and scaled.</td>
                    <td>Linear Algebra, Mathematics</td>
                </tr>
                
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function toggleExpand() {
            const container = document.getElementById('graphContainer');
            const overlay = document.getElementById('overlay');
            const expandBtn = document.getElementById('expandBtn');

            if (container.classList.contains('expanded')) {
                container.classList.remove('expanded');
                overlay.classList.remove('active');
                expandBtn.innerHTML = '🔍 Expand';
                document.body.style.overflow = 'auto';

                // Force resize of all network elements after collapse
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            } else {
                container.classList.add('expanded');
                overlay.classList.add('active');
                expandBtn.innerHTML = '❌ Close';
                document.body.style.overflow = 'hidden';

                // Force resize of all network elements after expansion
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            }
        }

        function resizeNetworkElements() {
            // Force resize of all pyvis network elements
            const networkElements = [
                document.getElementById('mynetworkid'),
                document.querySelector('.card-body'),
                document.querySelector('#vis-network'),
                document.querySelector('canvas')
            ];

            networkElements.forEach(element => {
                if (element) {
                    // Trigger a reflow by temporarily changing display
                    const originalDisplay = element.style.display;
                    element.style.display = 'none';
                    element.offsetHeight; // Trigger reflow
                    element.style.display = originalDisplay;
                }
            });

            // Redraw and fit the network
            if (window.network) {
                if (window.network.redraw) {
                    window.network.redraw();
                }
                if (window.network.fit) {
                    window.network.fit();
                }
                // Force canvas resize if available
                if (window.network.canvas && window.network.canvas.setSize) {
                    const container = document.querySelector('.graph-content');
                    if (container) {
                        window.network.canvas.setSize(container.offsetWidth, container.offsetHeight);
                    }
                }
            }
        }

        function resetGraph() {
            // Reset the network view and ensure proper sizing
            resizeNetworkElements();
        }

        // Close expanded view with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const container = document.getElementById('graphContainer');
                if (container.classList.contains('expanded')) {
                    toggleExpand();
                }
            }
        });
    </script>

    <script src="lib/bindings/utils.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>
<script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>
<script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "black"}, "id": "Machine Learning", "label": "Machine Learning", "shape": "dot", "size": 45, "title": "Depth: 0\nDescription: A field of artificial intelligence that uses statistical techniques for learning from data."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Calculus", "label": "Calculus", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The mathematical study of continuous change."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Linear Algebra", "label": "Linear Algebra", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The branch of mathematics concerning linear equations and linear functions."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Programming Fundamentals", "label": "Programming Fundamentals", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Basic concepts of computer programming including variables, control structures, and algorithms."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Statistics", "label": "Statistics", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The discipline that concerns collection, organization, analysis, and interpretation of data."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Computer Science", "label": "Computer Science", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: The study of algorithmic processes and computational systems."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Functions", "label": "Functions", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Mathematical relations that assign exactly one output to each input."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Limits", "label": "Limits", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: A fundamental concept describing the behavior of functions near specific points."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Logic", "label": "Logic", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: The systematic study of the principles of valid inference and reasoning."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Mathematics", "label": "Mathematics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: The abstract science of number, quantity, and space."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Probability Theory", "label": "Probability Theory", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: The branch of mathematics concerned with probability and random phenomena."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Vector Spaces", "label": "Vector Spaces", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: A collection of objects called vectors that can be added and scaled."}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Programming Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Limits", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Vector Spaces", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Fundamentals", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Fundamentals", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Fundamentals", "to": "Computer Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Computer Science", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Computer Science", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Spaces", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Spaces", "to": "Mathematics", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 100}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
</body>
</html>
        