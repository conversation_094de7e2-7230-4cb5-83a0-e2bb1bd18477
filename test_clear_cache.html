
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .graph-container {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .graph-container.expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1000;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .graph-container.expanded .graph-content {
            height: calc(100vh - 120px) !important;
        }
        .graph-container.expanded .graph-content > div {
            height: 100% !important;
        }
        .graph-container.expanded #mynetworkid {
            height: 100% !important;
        }
        .graph-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 5px;
        }
        .control-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .control-btn:hover {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .graph-content {
            height: 500px;
            transition: height 0.3s ease;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f0f0;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="overlay" id="overlay" onclick="toggleExpand()"></div>

    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">237</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">182</div>
                <div class="stat-label">Topics with Dependencies</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>

            <div class="graph-container" id="graphContainer">
                <div class="graph-controls">
                    <button class="control-btn" onclick="toggleExpand()" id="expandBtn">
                        🔍 Expand
                    </button>
                    <button class="control-btn" onclick="resetGraph()">
                        🔄 Reset View
                    </button>
                </div>
                <div class="graph-content">
                    <div id="mynetwork" class="card-body"></div>
                </div>
            </div>
        </div>

        
        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th>Depth</th>
                        <th>Term</th>
                        <th>Description</th>
                        <th>Seen Dependencies</th>
                        <th>Unseen Dependencies</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Deep Learning</strong></td>
                    <td>Deep Learning is a subset of machine learning that utilizes neural networks with multiple layers to model complex patterns in large datasets. It has applications in various fields such as computer vision, natural language processing, and speech recognition.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Linear Algebra, Calculus, Probability and Statistics, Machine Learning Fundamentals, Programming in Python</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Calculus</strong></td>
                    <td>Calculus is a branch of mathematics that studies continuous change, focusing on concepts such as derivatives, integrals, limits, and infinite series. It provides essential tools for analyzing dynamic systems and is foundational for advanced studies in mathematics, physics, engineering, and economics.</td>
                    <td><span style="color: blue;">Geometry, Trigonometry</span></td>
                    <td><span style="color: red;">Algebra, Functions, Limits</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Linear Algebra</strong></td>
                    <td>Linear Algebra is a branch of mathematics that deals with vector spaces, linear transformations, and systems of linear equations. It is foundational for various fields such as engineering, physics, computer science, and economics.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic Algebra, Geometry, Functions and Graphs, Trigonometry</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Machine Learning Fundamentals</strong></td>
                    <td>Machine Learning Fundamentals covers the basic principles and techniques of machine learning, including supervised and unsupervised learning, model evaluation, and algorithm selection. This topic serves as a foundation for understanding how machines can learn from data and make predictions or decisions.</td>
                    <td><span style="color: blue;">Linear Algebra, Calculus</span></td>
                    <td><span style="color: red;">Statistics, Programming Basics, Data Structures</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Probability and Statistics</strong></td>
                    <td>Probability and Statistics is a branch of mathematics that deals with the analysis of random phenomena and the interpretation of data. It provides tools for making inferences about populations based on sample data and for quantifying uncertainty.</td>
                    <td><span style="color: blue;">Basic Algebra, Functions and Graphs</span></td>
                    <td><span style="color: red;">Set Theory, Descriptive Statistics, Basic Calculus</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Programming in Python</strong></td>
                    <td>Programming in Python involves writing code in the Python programming language to create software applications, automate tasks, and analyze data. It emphasizes readability and simplicity, making it an ideal language for beginners and experienced developers alike.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of computer science concepts, Familiarity with algorithms and data structures, Knowledge of basic mathematics, Experience with using a text editor or integrated development environment (IDE), Understanding of fundamental programming concepts such as variables, control structures, and functions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Algebra</strong></td>
                    <td>Algebra is a branch of mathematics that deals with symbols and the rules for manipulating those symbols to solve equations and understand relationships between quantities. It serves as a foundational tool for higher-level mathematics and various applications in science and engineering.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Arithmetic, Basic Geometry, Number Theory, Understanding of Variables</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic Algebra</strong></td>
                    <td>Basic Algebra involves the study of mathematical symbols and the rules for manipulating these symbols to solve equations and inequalities. It serves as a foundational component for higher-level mathematics and problem-solving skills.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Arithmetic operations, Understanding of variables, Basic properties of numbers, Order of operations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic Calculus</strong></td>
                    <td>Basic Calculus is the study of the fundamental concepts of differentiation and integration, focusing on the rates of change and the accumulation of quantities. It serves as a foundation for understanding more advanced mathematical concepts and applications in various fields.</td>
                    <td><span style="color: blue;">Algebra, Functions, Graphing, Trigonometry</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic understanding of computer science concepts</strong></td>
                    <td>This topic encompasses the foundational principles of computer science, including algorithms, data structures, programming languages, and the theory of computation. It serves as an essential introduction for students to grasp how computers operate and how software is developed.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Mathematical reasoning, Basic programming skills, Understanding of binary and number systems, Familiarity with logical operators</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Data Structures</strong></td>
                    <td>Data Structures are specialized formats for organizing, processing, and storing data in a computer, enabling efficient access and modification. They form the backbone of computer algorithms and are essential for effective programming and software development.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic Programming Concepts, Algorithms, Mathematics for Computer Science, Complexity Analysis</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Descriptive Statistics</strong></td>
                    <td>Descriptive statistics involves the methods of summarizing and organizing data to provide a clear overview of its main features, typically through measures such as mean, median, mode, and standard deviation. It serves as a foundational tool for data analysis, allowing researchers to present quantitative descriptions in a manageable form.</td>
                    <td><span style="color: blue;">Understanding of Variables</span></td>
                    <td><span style="color: red;">Basic Mathematics, Data Collection Techniques, Introduction to Probability</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Experience with using a text editor or integrated development environment (IDE)</strong></td>
                    <td>This topic encompasses the skills and knowledge required to effectively utilize text editors and integrated development environments for software development, including features such as syntax highlighting, debugging, and version control integration.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of programming concepts, Familiarity with file systems and directory structures, Knowledge of programming languages syntax, Introduction to software development methodologies</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Familiarity with algorithms and data structures</strong></td>
                    <td>Familiarity with algorithms and data structures involves understanding the fundamental techniques for organizing and manipulating data efficiently, as well as the methods for solving computational problems. This knowledge is essential for software development, computer science, and data analysis.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic programming concepts, Mathematical foundations (e.g., discrete mathematics), Understanding of complexity analysis, Basic knowledge of recursion, Familiarity with basic data types</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Functions</strong></td>
                    <td>Functions are mathematical constructs that relate an input to a single output, defined by a specific rule or formula. They are foundational in various fields such as mathematics, computer science, and engineering, serving as a means to model relationships and perform calculations.</td>
                    <td><span style="color: blue;">Algebraic expressions</span></td>
                    <td><span style="color: red;">Variables, Graphing, Basic arithmetic operations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Functions and Graphs</strong></td>
                    <td>Functions and Graphs is a fundamental topic in mathematics that explores the relationship between inputs and outputs through functions, and visually represents these relationships using graphs. Understanding this topic is essential for analyzing mathematical behavior and solving real-world problems.</td>
                    <td><span style="color: blue;">Basic arithmetic</span></td>
                    <td><span style="color: red;">Algebraic expressions, Coordinate geometry, Inequalities, Linear equations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Geometry</strong></td>
                    <td>Geometry is a branch of mathematics that deals with the properties and relationships of points, lines, surfaces, and solids. It encompasses various concepts such as shapes, sizes, relative positions of figures, and the properties of space.</td>
                    <td><span style="color: blue;">Algebra</span></td>
                    <td><span style="color: red;">Basic arithmetic, Understanding of spatial reasoning, Introduction to mathematical proofs</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Knowledge of basic mathematics</strong></td>
                    <td>Knowledge of basic mathematics encompasses fundamental arithmetic operations, number sense, and the ability to solve simple mathematical problems. It serves as the foundation for more advanced mathematical concepts and applications in various fields.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of numbers and number systems, Basic arithmetic operations (addition, subtraction, multiplication, division), Concept of place value, Understanding of fractions and decimals, Basic problem-solving skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Limits</strong></td>
                    <td>Limits are a fundamental concept in calculus that describe the behavior of a function as its input approaches a particular value. They are essential for understanding continuity, derivatives, and integrals.</td>
                    <td><span style="color: blue;">Functions, Algebra, Graphing</span></td>
                    <td><span style="color: red;">Basic Trigonometry</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Programming Basics</strong></td>
                    <td>Programming Basics introduces fundamental concepts of computer programming, including syntax, data types, control structures, and basic algorithms, enabling learners to write simple programs. This foundational knowledge is essential for further study in software development and computer science.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic mathematics, Logical reasoning, Problem-solving skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Set Theory</strong></td>
                    <td>Set Theory is a branch of mathematical logic that studies sets, which are collections of objects. It serves as a foundational system for various areas of mathematics, providing a framework for understanding relations, functions, and cardinality.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic Logic, Mathematical Notation, Elementary Algebra, Functions and Relations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Statistics</strong></td>
                    <td>Statistics is the branch of mathematics that deals with the collection, analysis, interpretation, presentation, and organization of data. It provides tools for making inferences about populations based on sample data and is essential for decision-making in various fields.</td>
                    <td><span style="color: blue;">Basic Algebra</span></td>
                    <td><span style="color: red;">Probability Theory, Data Analysis, Mathematical Reasoning</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Trigonometry</strong></td>
                    <td>Trigonometry is a branch of mathematics that studies the relationships between the angles and sides of triangles, particularly right triangles. It is essential for understanding concepts in geometry, physics, engineering, and various applied sciences.</td>
                    <td><span style="color: blue;">Basic Algebra, Geometry</span></td>
                    <td><span style="color: red;">Understanding of Angles, Coordinate Systems</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Understanding of fundamental programming concepts such as variables, control structures, and functions</strong></td>
                    <td>This topic encompasses the basic building blocks of programming, including the use of variables to store data, control structures to dictate the flow of execution, and functions to encapsulate reusable code. Mastery of these concepts is essential for developing effective algorithms and writing efficient code.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic mathematical concepts, Logical reasoning skills, Introduction to computer science, Familiarity with a programming environment</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Algebraic expressions</strong></td>
                    <td>Algebraic expressions are mathematical phrases that include numbers, variables, and operation symbols, representing a value or relationship. They serve as the foundation for more complex algebraic concepts and problem-solving techniques.</td>
                    <td><span style="color: blue;">Basic arithmetic operations (addition, subtraction, multiplication, division), Order of operations (PEMDAS/BODMAS)</span></td>
                    <td><span style="color: red;">Understanding of variables and constants, Introduction to equations and inequalities</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Algorithms</strong></td>
                    <td>Algorithms are step-by-step procedures or formulas for solving problems and performing tasks, often implemented in programming to automate processes and optimize performance. They are fundamental to computer science and are used in various applications, from data processing to artificial intelligence.</td>
                    <td><span style="color: blue;">Basic programming concepts</span></td>
                    <td><span style="color: red;">Data structures, Mathematical logic, Complexity theory, Discrete mathematics</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Arithmetic</strong></td>
                    <td>Arithmetic is the branch of mathematics dealing with the properties and manipulation of numbers, including operations such as addition, subtraction, multiplication, and division. It serves as the foundation for more advanced mathematical concepts and applications.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Number recognition, Basic counting, Understanding of numerical order, Simple addition and subtraction</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Arithmetic operations</strong></td>
                    <td>Arithmetic operations refer to the basic mathematical processes of addition, subtraction, multiplication, and division, which are foundational for performing calculations and solving numerical problems. Mastery of these operations is essential for advancing in mathematics and its applications in various fields.</td>
                    <td><span style="color: blue;">Basic properties of numbers</span></td>
                    <td><span style="color: red;">Number sense, Understanding of whole numbers, Concept of equality</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic Geometry</strong></td>
                    <td>Basic Geometry is the branch of mathematics that deals with the properties and relationships of points, lines, angles, surfaces, and solids. It serves as a foundational subject that introduces students to spatial reasoning and the principles of measurement.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of basic arithmetic, Familiarity with basic algebra, Knowledge of spatial reasoning, Introduction to mathematical reasoning</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic Logic</strong></td>
                    <td>Basic Logic is the study of the principles of valid reasoning and argumentation, focusing on the structure of statements and the relationships between them. It provides foundational tools for evaluating arguments and understanding logical relationships in various fields.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of propositions, Familiarity with logical connectives, Basic comprehension of truth tables</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic Mathematics</strong></td>
                    <td>Basic Mathematics encompasses fundamental concepts such as arithmetic operations, number theory, and introductory algebra, forming the foundation for more advanced mathematical studies. It is essential for everyday problem-solving and critical thinking skills.</td>
                    <td><span style="color: blue;">Number recognition, Basic counting</span></td>
                    <td><span style="color: red;">Understanding of addition and subtraction, Familiarity with multiplication and division</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic Programming Concepts</strong></td>
                    <td>Basic Programming Concepts encompass fundamental ideas and principles that form the foundation of programming, including variables, data types, control structures, and functions. Understanding these concepts is essential for writing and comprehending code in any programming language.</td>
                    <td><span style="color: blue;">Mathematical reasoning</span></td>
                    <td><span style="color: red;">Logical thinking, Basic computer literacy</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic Trigonometry</strong></td>
                    <td>Basic Trigonometry is the study of the relationships between the angles and sides of triangles, particularly right triangles. It introduces fundamental concepts such as sine, cosine, and tangent, which are essential for solving various geometrical and real-world problems.</td>
                    <td><span style="color: blue;">Basic Algebra, Geometry, Understanding of Angles</span></td>
                    <td><span style="color: red;">Coordinate System</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic arithmetic</strong></td>
                    <td>Basic arithmetic is the branch of mathematics that deals with the fundamental operations of addition, subtraction, multiplication, and division. It serves as the foundation for more advanced mathematical concepts and everyday calculations.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of numbers, Concept of quantity, Basic number recognition, Understanding of symbols and operations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic arithmetic operations</strong></td>
                    <td>Basic arithmetic operations refer to the fundamental mathematical processes of addition, subtraction, multiplication, and division, which are essential for performing calculations and solving numerical problems. Mastery of these operations is crucial for further study in mathematics and related fields.</td>
                    <td><span style="color: blue;">Understanding of numbers, Concept of quantity</span></td>
                    <td><span style="color: red;">Basic number line usage</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic arithmetic operations (addition, subtraction, multiplication, division)</strong></td>
                    <td>Basic arithmetic operations are the foundational mathematical processes used to manipulate numbers, including addition (combining values), subtraction (finding the difference), multiplication (repeated addition), and division (distributing a value into equal parts). Mastery of these operations is essential for further mathematical learning and everyday problem-solving.</td>
                    <td><span style="color: blue;">Understanding of numbers, Concept of quantity</span></td>
                    <td><span style="color: red;">Familiarity with counting, Basic number sense</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic knowledge of recursion</strong></td>
                    <td>Recursion is a programming technique where a function calls itself to solve a problem by breaking it down into smaller, more manageable subproblems. Understanding recursion is essential for solving problems that can be defined in terms of smaller instances of the same problem.</td>
                    <td><span style="color: blue;">Basic programming concepts</span></td>
                    <td><span style="color: red;">Control structures (if statements, loops), Functions and procedures, Data structures (arrays, lists)</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic mathematical concepts</strong></td>
                    <td>Basic mathematical concepts encompass fundamental ideas such as numbers, operations, and relationships that form the foundation for more advanced mathematics. These concepts include arithmetic operations, number properties, and basic geometric principles essential for everyday problem-solving.</td>
                    <td><span style="color: blue;">Number recognition, Basic arithmetic operations (addition, subtraction, multiplication, division), Understanding of place value</span></td>
                    <td><span style="color: red;">Simple geometric shapes and their properties</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic mathematics</strong></td>
                    <td>Basic mathematics encompasses foundational concepts such as arithmetic operations, number theory, and basic geometry, which are essential for understanding more advanced mathematical topics. It serves as the building block for problem-solving and analytical skills in various fields.</td>
                    <td><span style="color: blue;">Number recognition</span></td>
                    <td><span style="color: red;">Counting, Understanding of basic arithmetic operations (addition, subtraction, multiplication, division), Simple fractions and decimals</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic problem-solving skills</strong></td>
                    <td>Basic problem-solving skills involve the ability to identify, analyze, and develop solutions for various challenges or obstacles. These skills are essential for effective decision-making and critical thinking in both academic and real-world contexts.</td>
                    <td><span style="color: blue;">Critical thinking, Analytical reasoning, Basic mathematics, Communication skills</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic programming concepts</strong></td>
                    <td>Basic programming concepts encompass fundamental principles and constructs used in programming languages, including variables, data types, control structures, and functions. Mastery of these concepts is essential for developing problem-solving skills and writing effective code.</td>
                    <td><span style="color: blue;">Familiarity with mathematical concepts, Logical reasoning skills</span></td>
                    <td><span style="color: red;">Understanding of algorithms, Basic knowledge of computer operations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic programming skills</strong></td>
                    <td>Basic programming skills encompass the foundational knowledge and abilities required to write, understand, and debug simple code in a programming language. This includes concepts such as variables, control structures, data types, and basic algorithms.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of logic and problem-solving, Familiarity with mathematical concepts, Basic knowledge of computer operation, Introduction to algorithms</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic properties of numbers</strong></td>
                    <td>The basic properties of numbers refer to fundamental characteristics that govern how numbers interact with one another, including concepts such as commutativity, associativity, distributivity, identity elements, and inverses. Understanding these properties is essential for performing arithmetic operations and solving mathematical problems effectively.</td>
                    <td><span style="color: blue;">Basic arithmetic operations (addition, subtraction, multiplication, division)</span></td>
                    <td><span style="color: red;">Understanding of numbers (natural, whole, integers, rational, and real numbers), Order of operations (PEMDAS/BODMAS), Concept of equality and inequalities</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic understanding of programming concepts</strong></td>
                    <td>This topic encompasses the foundational ideas and principles that underpin programming, including variables, control structures, data types, and algorithms. A basic understanding of these concepts is essential for anyone looking to learn how to write and understand computer programs.</td>
                    <td><span style="color: blue;">Logical reasoning, Basic mathematics</span></td>
                    <td><span style="color: red;">Understanding of computer systems, Familiarity with problem-solving techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Complexity Analysis</strong></td>
                    <td>Complexity Analysis is the study of the resources required for the execution of algorithms, primarily focusing on time and space complexity. It provides a framework for evaluating the efficiency of algorithms and understanding their scalability with respect to input size.</td>
                    <td><span style="color: blue;">Basic Programming Concepts, Data Structures</span></td>
                    <td><span style="color: red;">Algorithm Design, Mathematical Foundations, Big O Notation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Concept of place value</strong></td>
                    <td>The concept of place value is a foundational principle in mathematics that assigns a numerical value to a digit based on its position within a number. It is essential for understanding how numbers are structured and how they can be manipulated in arithmetic operations.</td>
                    <td><span style="color: blue;">Understanding of numbers</span></td>
                    <td><span style="color: red;">Basic counting skills, Knowledge of the decimal system, Familiarity with addition and subtraction</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Coordinate Systems</strong></td>
                    <td>Coordinate systems are mathematical frameworks used to uniquely determine the position of points in space through ordered pairs or triplets of numbers. They are essential in various fields such as geometry, physics, and computer graphics for representing spatial relationships and transformations.</td>
                    <td><span style="color: blue;">Basic Algebra, Geometry, Trigonometry, Functions and Graphs</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Coordinate geometry</strong></td>
                    <td>Coordinate geometry, also known as analytic geometry, is the study of geometric figures using a coordinate system, allowing for the representation and analysis of shapes and their properties through algebraic equations. It combines algebra and geometry to solve problems involving points, lines, and curves in a two-dimensional or three-dimensional space.</td>
                    <td><span style="color: blue;">Basic algebra</span></td>
                    <td><span style="color: red;">Understanding of geometric shapes, Knowledge of the Cartesian coordinate system, Basic properties of lines and angles</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Data Analysis</strong></td>
                    <td>Data analysis is the process of systematically applying statistical and logical techniques to describe, summarize, and compare data. It involves extracting meaningful insights from raw data to support decision-making and problem-solving.</td>
                    <td><span style="color: blue;">Statistics, Data Management</span></td>
                    <td><span style="color: red;">Mathematics, Basic Programming, Data Visualization</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Data Collection Techniques</strong></td>
                    <td>Data Collection Techniques encompass various methods used to gather information for research purposes, enabling researchers to obtain quantitative and qualitative data. Understanding these techniques is essential for designing effective studies and ensuring the reliability and validity of the collected data.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Research Methodology, Statistical Analysis, Sampling Methods, Ethics in Research, Data Management</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Elementary Algebra</strong></td>
                    <td>Elementary Algebra is a branch of mathematics that deals with the basic concepts of algebra, including the manipulation of variables, the solving of equations, and the understanding of functions and their properties. It serves as a foundational course for higher-level mathematics and is essential for various applications in science, engineering, and everyday problem-solving.</td>
                    <td><span style="color: blue;">Basic arithmetic operations, Order of operations</span></td>
                    <td><span style="color: red;">Understanding of numbers (integers, fractions, decimals), Introduction to variables and expressions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Familiarity with a programming environment</strong></td>
                    <td>Familiarity with a programming environment refers to the understanding and ability to effectively use tools, interfaces, and features provided by software platforms for coding, debugging, and executing programs. This knowledge is essential for developers to enhance productivity and streamline the software development process.</td>
                    <td><span style="color: blue;">Basic programming concepts, Knowledge of version control systems</span></td>
                    <td><span style="color: red;">Understanding of algorithms and data structures, Familiarity with command line interfaces, Basic troubleshooting and debugging skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Familiarity with basic data types</strong></td>
                    <td>Familiarity with basic data types involves understanding the fundamental categories of data used in programming and data analysis, such as integers, floats, strings, and booleans. This knowledge is essential for effective data manipulation and coding practices.</td>
                    <td><span style="color: blue;">Understanding of variables, Basic arithmetic operations, Introduction to programming concepts</span></td>
                    <td><span style="color: red;">Familiarity with logic and boolean expressions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Familiarity with file systems and directory structures</strong></td>
                    <td>This topic encompasses understanding how data is organized, stored, and accessed on storage devices through various file systems and their hierarchical directory structures. It is essential for effective data management and navigation within operating systems.</td>
                    <td><span style="color: blue;">Introduction to programming concepts</span></td>
                    <td><span style="color: red;">Basic understanding of computer systems, Knowledge of operating systems, Familiarity with data storage concepts</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Familiarity with logical operators</strong></td>
                    <td>Familiarity with logical operators involves understanding the basic logical constructs used in programming and mathematics, such as AND, OR, and NOT, which are essential for constructing logical expressions and making decisions in algorithms. Mastery of these operators is crucial for effective problem-solving and programming logic.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of Boolean algebra, Introduction to programming concepts, Knowledge of conditional statements, Familiarity with truth tables</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Functions and Relations</strong></td>
                    <td>Functions and Relations are fundamental concepts in mathematics that describe the relationship between sets of inputs and outputs, where a function assigns exactly one output for each input, while a relation may associate multiple outputs with a single input. Understanding these concepts is essential for exploring more advanced topics in algebra, calculus, and discrete mathematics.</td>
                    <td><span style="color: blue;">Basic algebra, Graphing</span></td>
                    <td><span style="color: red;">Sets, Ordered pairs, Domain and range</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Graphing</strong></td>
                    <td>Graphing is the visual representation of data or mathematical functions on a coordinate system, allowing for the analysis of relationships and trends. It is a fundamental skill in mathematics and science that aids in interpreting quantitative information.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of coordinates and the Cartesian plane, Basic algebraic concepts, Knowledge of functions and their properties, Ability to interpret data sets</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Inequalities</strong></td>
                    <td>Inequalities are mathematical expressions that describe the relative size or order of two values, using symbols such as <, >, ≤, and ≥. They are fundamental in various fields of mathematics, including algebra and calculus, as they help in understanding the relationships between quantities.</td>
                    <td><span style="color: blue;">Basic arithmetic, Algebraic expressions, Understanding of variables, Basic properties of numbers</span></td>
                    <td><span style="color: red;">Graphing on a number line</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Introduction to Probability</strong></td>
                    <td>Introduction to Probability covers the fundamental concepts of probability theory, including the calculation of probabilities, the understanding of random variables, and the principles of combinatorics. This topic serves as a foundation for more advanced studies in statistics and data analysis.</td>
                    <td><span style="color: blue;">Basic Algebra, Set Theory</span></td>
                    <td><span style="color: red;">Understanding of Functions, Basic Statistics</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Introduction to computer science</strong></td>
                    <td>Introduction to computer science is a foundational course that covers the fundamental principles of computing, including algorithms, data structures, programming languages, and the basics of software development. It aims to equip students with the skills necessary to understand and solve computational problems.</td>
                    <td><span style="color: blue;">Basic mathematics, Logical reasoning, Understanding of algorithms</span></td>
                    <td><span style="color: red;">Familiarity with programming concepts</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Introduction to mathematical proofs</strong></td>
                    <td>This topic introduces students to the fundamental concepts and techniques used in constructing and understanding mathematical proofs, emphasizing logical reasoning and the structure of mathematical arguments.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic algebra, Understanding of mathematical logic, Familiarity with set theory, Introduction to functions and relations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Introduction to software development methodologies</strong></td>
                    <td>This topic provides an overview of various software development methodologies, including Agile, Waterfall, and DevOps, focusing on their principles, processes, and best practices. It aims to equip learners with the foundational knowledge necessary to select and implement appropriate methodologies in software projects.</td>
                    <td><span style="color: blue;">Basic programming concepts</span></td>
                    <td><span style="color: red;">Understanding of software development life cycle (SDLC), Familiarity with project management principles, Knowledge of version control systems</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Knowledge of programming languages syntax</strong></td>
                    <td>Knowledge of programming languages syntax refers to the understanding of the rules and structure that govern how code is written in various programming languages. This includes familiarity with keywords, operators, and the organization of statements that enable effective communication with a computer.</td>
                    <td><span style="color: blue;">Familiarity with algorithms and data structures, Introduction to programming concepts</span></td>
                    <td><span style="color: red;">Basic understanding of computer science principles, Logical reasoning and problem-solving skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Linear equations</strong></td>
                    <td>Linear equations are mathematical statements that express the equality of two linear expressions, typically in the form of y = mx + b, where m represents the slope and b the y-intercept. They are foundational in algebra and are used to model relationships between variables.</td>
                    <td><span style="color: blue;">Basic arithmetic, Understanding of variables, Order of operations</span></td>
                    <td><span style="color: red;">Graphing concepts, Algebraic manipulation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Logical reasoning</strong></td>
                    <td>Logical reasoning is the process of using structured, coherent thought to analyze arguments, draw conclusions, and solve problems based on given premises. It encompasses both deductive and inductive reasoning techniques to evaluate the validity of statements and arguments.</td>
                    <td><span style="color: blue;">Basic mathematics</span></td>
                    <td><span style="color: red;">Critical thinking, Understanding of logical operators, Familiarity with argument structures</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Logical reasoning skills</strong></td>
                    <td>Logical reasoning skills involve the ability to analyze information, draw conclusions, and make decisions based on logical principles. These skills are essential for problem-solving and critical thinking in various academic and real-world contexts.</td>
                    <td><span style="color: blue;">Basic mathematics, Understanding of logical operators</span></td>
                    <td><span style="color: red;">Familiarity with syllogisms, Critical thinking skills, Introduction to argumentation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Mathematical Notation</strong></td>
                    <td>Mathematical notation is a system of symbols and signs used to represent numbers, operations, relationships, and functions in mathematics. It provides a concise and unambiguous way to communicate mathematical ideas and concepts.</td>
                    <td><span style="color: blue;">Basic Arithmetic, Set Theory</span></td>
                    <td><span style="color: red;">Algebraic Expressions, Logical Reasoning</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Mathematical Reasoning</strong></td>
                    <td>Mathematical reasoning involves the logical process of deriving conclusions from premises using mathematical concepts and principles. It encompasses various forms of reasoning, including deductive and inductive reasoning, to solve problems and prove theorems.</td>
                    <td><span style="color: blue;">Basic Arithmetic, Algebra, Set Theory</span></td>
                    <td><span style="color: red;">Logic, Proof Techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Mathematical foundations (e.g., discrete mathematics)</strong></td>
                    <td>Mathematical foundations encompass the essential concepts and principles that form the basis for various branches of mathematics, with discrete mathematics focusing on structures that are fundamentally discrete rather than continuous. This area includes topics such as logic, set theory, combinatorics, graph theory, and algorithms, which are crucial for understanding more advanced mathematical and computational theories.</td>
                    <td><span style="color: blue;">Basic arithmetic, Algebra, Set theory</span></td>
                    <td><span style="color: red;">Logic and reasoning, Functions and relations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Mathematical reasoning</strong></td>
                    <td>Mathematical reasoning is the process of using logical thinking to analyze mathematical concepts, formulate arguments, and solve problems. It encompasses various techniques, including deductive and inductive reasoning, to derive conclusions from premises or known facts.</td>
                    <td><span style="color: blue;">Basic arithmetic, Algebra, Logical reasoning</span></td>
                    <td><span style="color: red;">Set theory, Proof techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Mathematics for Computer Science</strong></td>
                    <td>Mathematics for Computer Science encompasses the mathematical concepts and techniques that are essential for understanding algorithms, data structures, and computational theory. It includes topics such as discrete mathematics, logic, and combinatorics, which form the foundation for problem-solving in computer science.</td>
                    <td><span style="color: blue;">Basic Algebra, Set Theory, Functions and Relations</span></td>
                    <td><span style="color: red;">Logic and Propositional Calculus, Basic Number Theory</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Number Theory</strong></td>
                    <td>Number Theory is a branch of pure mathematics devoted to the study of the integers and their properties, including concepts such as divisibility, prime numbers, and congruences. It serves as a foundational area for various applications in cryptography, computer science, and algebra.</td>
                    <td><span style="color: blue;">Algebra, Set Theory, Functions and Relations</span></td>
                    <td><span style="color: red;">Basic Arithmetic, Mathematical Proof Techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Order of operations</strong></td>
                    <td>The order of operations is a mathematical rule that dictates the sequence in which different operations (such as addition, subtraction, multiplication, and division) should be performed to ensure consistent results in calculations. This is often remembered by the acronym PEMDAS, which stands for Parentheses, Exponents, Multiplication and Division (from left to right), Addition and Subtraction (from left to right).</td>
                    <td><span style="color: blue;">Basic arithmetic operations</span></td>
                    <td><span style="color: red;">Understanding of parentheses, Knowledge of exponents, Order of operations rules</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Probability Theory</strong></td>
                    <td>Probability Theory is a branch of mathematics that deals with the analysis of random phenomena and the quantification of uncertainty. It provides a framework for modeling and reasoning about events and outcomes in various fields such as statistics, finance, and science.</td>
                    <td><span style="color: blue;">Basic Algebra, Set Theory, Calculus, Statistics</span></td>
                    <td><span style="color: red;">Combinatorics</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Problem-solving skills</strong></td>
                    <td>Problem-solving skills refer to the ability to identify, analyze, and resolve issues effectively and efficiently. These skills encompass critical thinking, creativity, and decision-making processes essential for navigating complex situations.</td>
                    <td><span style="color: blue;">Critical thinking, Basic mathematics</span></td>
                    <td><span style="color: red;">Analytical reasoning, Communication skills, Creativity</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Understanding of Angles</strong></td>
                    <td>Understanding of angles involves recognizing and measuring the space between two intersecting lines or surfaces, typically expressed in degrees. This topic is foundational in geometry and is essential for further studies in trigonometry, physics, and engineering.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic geometric shapes, Line segments and rays, Measurement concepts, Properties of parallel and intersecting lines</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Understanding of Variables</strong></td>
                    <td>Understanding of variables is the foundational concept in mathematics and programming that involves recognizing and manipulating symbols that represent quantities or values. It is essential for solving equations, performing calculations, and writing algorithms.</td>
                    <td><span style="color: blue;">Basic arithmetic, Algebraic expressions, Mathematical notation, Logical reasoning</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Understanding of binary and number systems</strong></td>
                    <td>This topic involves the study of various numeral systems, particularly the binary system, which is foundational for computer science and digital electronics. It encompasses the conversion between different bases and the representation of data in binary form.</td>
                    <td><span style="color: blue;">Basic arithmetic operations</span></td>
                    <td><span style="color: red;">Understanding of place value, Introduction to mathematical logic, Familiarity with decimal number system</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Understanding of complexity analysis</strong></td>
                    <td>Complexity analysis is the study of the resources required for the execution of algorithms, primarily focusing on time and space complexity. It helps in evaluating the efficiency of algorithms and understanding their scalability in relation to input size.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of algorithms, Foundational knowledge of data structures, Introduction to mathematical notation, Basic principles of computer science, Understanding of big O notation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Understanding of fractions and decimals</strong></td>
                    <td>This topic involves recognizing, comparing, and performing operations with fractions and decimals, which are essential components of number sense and mathematical literacy. Mastery of these concepts enables students to solve real-world problems involving parts of a whole and numerical representation.</td>
                    <td><span style="color: blue;">Basic number sense, Understanding of whole numbers</span></td>
                    <td><span style="color: red;">Introduction to division, Concept of ratios, Place value system</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Understanding of numbers and number systems</strong></td>
                    <td>This topic encompasses the study of numerical values, their representations, and the various systems used to categorize and manipulate these numbers, including natural numbers, integers, rational numbers, and real numbers. It lays the foundation for mathematical reasoning and problem-solving across various disciplines.</td>
                    <td><span style="color: blue;">Basic arithmetic operations (addition, subtraction, multiplication, division), Understanding of place value</span></td>
                    <td><span style="color: red;">Familiarity with fractions and decimals, Introduction to mathematical symbols and notation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Understanding of spatial reasoning</strong></td>
                    <td>Spatial reasoning refers to the ability to visualize and manipulate objects in a three-dimensional space. It is a critical skill in fields such as mathematics, engineering, and architecture, facilitating problem-solving and critical thinking.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic geometry concepts, Visual perception skills, Understanding of coordinate systems, Introduction to logical reasoning, Familiarity with shapes and their properties</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Understanding of variables</strong></td>
                    <td>Understanding of variables involves recognizing their role as symbolic representations of data that can change or vary within mathematical expressions and programming contexts. This foundational concept is crucial for problem-solving and logical reasoning in mathematics and computer science.</td>
                    <td><span style="color: blue;">Basic arithmetic, Algebraic expressions, Logical reasoning</span></td>
                    <td><span style="color: red;">Mathematical notation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Variables</strong></td>
                    <td>Variables are symbolic representations used to store data values in programming and mathematics, allowing for dynamic manipulation and computation. They serve as placeholders for information that can change during the execution of a program or within mathematical expressions.</td>
                    <td><span style="color: blue;">Basic Algebra</span></td>
                    <td><span style="color: red;">Data Types, Mathematical Operations, Control Structures</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Ability to interpret data sets</strong></td>
                    <td>The ability to interpret data sets involves analyzing and making sense of numerical and categorical data to draw meaningful conclusions, identify trends, and inform decision-making. This skill is essential in various fields, including statistics, data science, and research.</td>
                    <td><span style="color: blue;">Critical thinking skills</span></td>
                    <td><span style="color: red;">Basic statistics, Data visualization techniques, Understanding of data types (qualitative and quantitative), Fundamentals of probability</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Algebraic Expressions</strong></td>
                    <td>Algebraic expressions are mathematical phrases that include numbers, variables, and operations. They serve as the foundational building blocks for algebra, allowing for the representation of relationships and the formulation of equations.</td>
                    <td><span style="color: blue;">Basic arithmetic operations, Understanding of variables</span></td>
                    <td><span style="color: red;">Order of operations (PEMDAS), Introduction to equations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Algebraic manipulation</strong></td>
                    <td>Algebraic manipulation involves the process of rearranging and simplifying algebraic expressions using various mathematical operations and properties. It is a fundamental skill in algebra that enables the solving of equations and the understanding of mathematical relationships.</td>
                    <td><span style="color: blue;">Basic arithmetic operations, Understanding of variables and constants</span></td>
                    <td><span style="color: red;">Knowledge of algebraic expressions, Familiarity with the distributive property, Understanding of equations and inequalities</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Algorithm Design</strong></td>
                    <td>Algorithm Design is the process of defining a step-by-step procedure or formula for solving a problem or performing a task, focusing on the efficiency and effectiveness of the solution. It encompasses various strategies and techniques to create algorithms that optimize performance and resource utilization.</td>
                    <td><span style="color: blue;">Data Structures, Mathematical Foundations, Complexity Analysis</span></td>
                    <td><span style="color: red;">Discrete Mathematics</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Analytical reasoning</strong></td>
                    <td>Analytical reasoning involves the ability to evaluate information, identify patterns, and draw logical conclusions based on given data. It is a critical skill used in problem-solving and decision-making across various academic and professional fields.</td>
                    <td><span style="color: blue;">Logical reasoning, Critical thinking, Basic mathematics, Problem-solving skills</span></td>
                    <td><span style="color: red;">Data interpretation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic Arithmetic</strong></td>
                    <td>Basic Arithmetic encompasses the fundamental operations of mathematics, including addition, subtraction, multiplication, and division, which are essential for performing calculations and solving numerical problems. Mastery of these operations forms the foundation for more advanced mathematical concepts.</td>
                    <td><span style="color: blue;">Number recognition</span></td>
                    <td><span style="color: red;">Understanding of quantities, Basic number line concepts</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic Number Theory</strong></td>
                    <td>Basic Number Theory is the branch of mathematics that deals with the properties and relationships of integers, including concepts such as divisibility, prime numbers, and modular arithmetic. It serves as a foundational area of study that underpins many advanced topics in mathematics and computer science.</td>
                    <td><span style="color: blue;">Basic Algebra, Set Theory</span></td>
                    <td><span style="color: red;">Elementary Arithmetic, Understanding of Integers</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic Programming</strong></td>
                    <td>Basic programming involves the foundational concepts and techniques used to write simple computer programs, including understanding syntax, data types, control structures, and algorithms. It serves as an introduction to the principles of coding and problem-solving using programming languages.</td>
                    <td><span style="color: blue;">Understanding of algorithms, Basic mathematical concepts, Logical reasoning skills</span></td>
                    <td><span style="color: red;">Familiarity with computer operations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic Statistics</strong></td>
                    <td>Basic Statistics involves the collection, analysis, interpretation, presentation, and organization of data. It provides foundational tools for understanding data distributions, measures of central tendency, and variability.</td>
                    <td><span style="color: blue;">Arithmetic, Algebra</span></td>
                    <td><span style="color: red;">Data Representation, Basic Probability</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic algebra</strong></td>
                    <td>Basic algebra involves the study of mathematical symbols and the rules for manipulating these symbols to solve equations and understand relationships between quantities. It serves as a foundational component for more advanced mathematical concepts and applications.</td>
                    <td><span style="color: blue;">Arithmetic operations, Basic properties of numbers, Order of operations</span></td>
                    <td><span style="color: red;">Understanding of integers</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic algebraic concepts</strong></td>
                    <td>Basic algebraic concepts involve the foundational principles of algebra, including the understanding of variables, constants, expressions, and equations. Mastery of these concepts is essential for solving mathematical problems and progressing to more advanced algebraic topics.</td>
                    <td><span style="color: blue;">Understanding of numbers and number systems, Basic arithmetic operations (addition, subtraction, multiplication, division)</span></td>
                    <td><span style="color: red;">Familiarity with mathematical symbols and notation, Introduction to mathematical expressions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic comprehension of truth tables</strong></td>
                    <td>Truth tables are a mathematical table used to determine the truth values of logical expressions based on their variables. Understanding truth tables is essential for analyzing logical operations and constructing logical arguments in fields such as mathematics, computer science, and philosophy.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of logic, Familiarity with logical operators (AND, OR, NOT), Introduction to propositional logic</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic computer literacy</strong></td>
                    <td>Basic computer literacy refers to the foundational skills required to effectively use computers and related technology, including understanding hardware, software, and basic troubleshooting. It encompasses the ability to navigate operating systems, utilize applications, and engage with the internet safely and efficiently.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of basic mathematics, Familiarity with common electronic devices, Knowledge of basic keyboard and mouse functions, Awareness of digital communication methods</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic counting</strong></td>
                    <td>Basic counting is the foundational skill of enumerating objects or quantities, typically involving the recognition and use of numbers to quantify items in a set. It serves as the basis for more advanced mathematical concepts such as addition, subtraction, and number theory.</td>
                    <td><span style="color: blue;">Number recognition</span></td>
                    <td><span style="color: red;">Understanding of one-to-one correspondence, Basic understanding of sets, Introduction to numbers</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic counting skills</strong></td>
                    <td>Basic counting skills involve the ability to recognize numbers and perform simple counting tasks, which are foundational for more advanced mathematical concepts. These skills are essential for everyday activities and serve as the building blocks for arithmetic operations.</td>
                    <td><span style="color: blue;">Number recognition, Basic number sense</span></td>
                    <td><span style="color: red;">One-to-one correspondence, Understanding of quantity</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic geometric shapes</strong></td>
                    <td>Basic geometric shapes are fundamental figures in geometry, including circles, triangles, squares, and rectangles, that serve as the building blocks for more complex geometric concepts. Understanding these shapes involves recognizing their properties, such as sides, angles, and symmetry.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of points and lines, Basic concepts of measurement, Introduction to angles, Familiarity with spatial reasoning</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic geometry concepts</strong></td>
                    <td>Basic geometry concepts encompass fundamental ideas related to shapes, sizes, and the properties of space, including points, lines, angles, and surfaces. These concepts serve as the foundation for more advanced studies in geometry and related fields.</td>
                    <td><span style="color: blue;">Familiarity with shapes and their properties</span></td>
                    <td><span style="color: red;">Understanding of points and lines, Basic arithmetic skills, Introduction to measurement</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic knowledge of computer operation</strong></td>
                    <td>Basic knowledge of computer operation involves understanding the fundamental functions and components of a computer system, including hardware, software, and user interfaces. This knowledge enables individuals to effectively interact with computers for various tasks such as file management, internet browsing, and application usage.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of basic electronic concepts, Familiarity with operating systems, Knowledge of file management principles, Basic understanding of software applications</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic knowledge of computer operations</strong></td>
                    <td>Basic knowledge of computer operations encompasses the fundamental skills and understanding required to effectively use a computer, including navigating the operating system, managing files, and utilizing essential software applications. This foundational knowledge is crucial for engaging with more advanced computing concepts and technologies.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of computer hardware components, Familiarity with operating systems, Basic knowledge of file management, Introduction to software applications, Basic troubleshooting skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic number line usage</strong></td>
                    <td>Basic number line usage involves understanding how to represent numbers on a linear scale, allowing for the visualization of numerical relationships, such as greater than, less than, and the concept of zero. It serves as a foundational tool for arithmetic operations and helps in grasping more complex mathematical concepts.</td>
                    <td><span style="color: blue;">Understanding of whole numbers, Basic counting skills</span></td>
                    <td><span style="color: red;">Concept of zero, Introduction to positive and negative numbers</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic number recognition</strong></td>
                    <td>Basic number recognition involves the ability to identify and understand numbers, including their symbols and quantities, which is foundational for early mathematical skills. This skill is crucial for developing further numeracy and arithmetic abilities in children.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of counting, Familiarity with number symbols, Basic understanding of quantity, Recognition of shapes and patterns</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic number sense</strong></td>
                    <td>Basic number sense refers to the foundational understanding of numbers and their relationships, including the ability to recognize, compare, and manipulate numbers in various contexts. It is essential for developing mathematical reasoning and problem-solving skills.</td>
                    <td><span style="color: blue;">Number recognition</span></td>
                    <td><span style="color: red;">Counting skills, Understanding of quantity, Basic addition and subtraction</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic principles of computer science</strong></td>
                    <td>Basic principles of computer science encompass foundational concepts such as algorithms, data structures, and programming logic that form the basis for understanding how computers operate and solve problems. This topic serves as an introduction to computational thinking and the systematic approach to problem-solving using technology.</td>
                    <td><span style="color: blue;">Mathematical reasoning, Basic programming skills</span></td>
                    <td><span style="color: red;">Understanding of logic, Familiarity with computer hardware, Introduction to software development</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic properties of lines and angles</strong></td>
                    <td>This topic covers the fundamental characteristics and relationships of lines and angles, including concepts such as parallel lines, intersecting lines, complementary and supplementary angles, and the properties of angles formed by transversals. Understanding these properties is essential for solving geometric problems and proofs.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of points and lines, Basic knowledge of geometric shapes, Familiarity with the concept of measurement, Introduction to angles and their types</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic troubleshooting and debugging skills</strong></td>
                    <td>Basic troubleshooting and debugging skills involve the ability to identify, diagnose, and resolve issues in software or hardware systems. These skills are essential for maintaining system functionality and improving user experience.</td>
                    <td><span style="color: blue;">Basic programming concepts</span></td>
                    <td><span style="color: red;">Fundamentals of computer science, Understanding of operating systems, Knowledge of software development life cycle, Basic logical reasoning and problem-solving skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic understanding of Boolean algebra</strong></td>
                    <td>Boolean algebra is a branch of mathematics that deals with variables that have two possible values, typically represented as true and false. It is fundamental in computer science, digital logic design, and set theory, providing the basis for logical reasoning and operations.</td>
                    <td><span style="color: blue;">Basic arithmetic, Set theory</span></td>
                    <td><span style="color: red;">Introduction to logic, Understanding of binary numbers</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic understanding of algorithms</strong></td>
                    <td>A basic understanding of algorithms involves grasping the fundamental principles of problem-solving through step-by-step procedures, including how to analyze their efficiency and effectiveness. This foundational knowledge is essential for further study in computer science and programming.</td>
                    <td><span style="color: blue;">Mathematical reasoning, Basic programming concepts, Logical thinking</span></td>
                    <td><span style="color: red;">Understanding of data structures</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic understanding of computer science principles</strong></td>
                    <td>This topic encompasses the foundational concepts and theories that underpin computer science, including algorithms, data structures, and the principles of programming. It provides learners with the essential knowledge needed to engage in further study or practical application of computer science.</td>
                    <td><span style="color: blue;">Basic mathematics, Logical reasoning</span></td>
                    <td><span style="color: red;">Introduction to programming, Understanding of binary systems</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic understanding of computer systems</strong></td>
                    <td>This topic encompasses the foundational concepts of how computer systems operate, including hardware components, software interactions, and basic data processing. It serves as an essential introduction for anyone looking to delve into more advanced computing subjects.</td>
                    <td><span style="color: blue;">Basic mathematics, Understanding of binary and number systems</span></td>
                    <td><span style="color: red;">Introduction to programming, Familiarity with operating systems concepts</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Big O Notation</strong></td>
                    <td>Big O Notation is a mathematical concept used to describe the upper bound of an algorithm's running time or space requirements in relation to the size of the input data. It provides a high-level understanding of the algorithm's efficiency and scalability.</td>
                    <td><span style="color: blue;">Basic understanding of algorithms, Introduction to mathematical notation</span></td>
                    <td><span style="color: red;">Fundamentals of data structures, Basic principles of computational complexity</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Combinatorics</strong></td>
                    <td>Combinatorics is a branch of mathematics focused on counting, arrangement, and combination of objects. It plays a crucial role in various fields such as computer science, probability, and optimization.</td>
                    <td><span style="color: blue;">Basic Algebra, Set Theory</span></td>
                    <td><span style="color: red;">Elementary Probability, Mathematical Logic</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Communication skills</strong></td>
                    <td>Communication skills refer to the ability to convey information effectively and efficiently, encompassing verbal, non-verbal, and written forms of communication. Mastery of these skills is essential for personal and professional success, as they facilitate understanding and collaboration among individuals.</td>
                    <td><span style="color: blue;">Critical thinking</span></td>
                    <td><span style="color: red;">Language proficiency, Active listening, Emotional intelligence, Basic interpersonal skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Complexity theory</strong></td>
                    <td>Complexity theory is a branch of theoretical computer science that studies the resources required for solving computational problems, particularly focusing on the classification of problems based on their inherent difficulty and the efficiency of algorithms that solve them.</td>
                    <td><span style="color: blue;">Discrete mathematics, Algorithms, Data structures, Mathematical logic</span></td>
                    <td><span style="color: red;">Computational theory</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Concept of equality</strong></td>
                    <td>The concept of equality refers to the principle that all individuals should have the same rights, opportunities, and treatment under the law, irrespective of their background or characteristics. It encompasses various dimensions, including social, political, and economic equality, and is fundamental to democratic societies.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Human rights, Social justice, Political theory, Ethics, Philosophy of law</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Concept of equality and inequalities</strong></td>
                    <td>The concept of equality and inequalities involves understanding the relationships between quantities, where equality denotes that two expressions are equivalent, while inequalities express the relative size or order of values. This topic is fundamental in various fields, including mathematics, economics, and social sciences, as it helps in analyzing and comparing different scenarios.</td>
                    <td><span style="color: blue;">Basic arithmetic operations</span></td>
                    <td><span style="color: red;">Understanding of numbers and their properties, Introduction to algebraic expressions, Graphing on a coordinate plane, Basic set theory</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Concept of quantity</strong></td>
                    <td>The concept of quantity refers to the understanding and measurement of amounts, sizes, or magnitudes of objects or phenomena, which is foundational in mathematics and the sciences. It encompasses the ability to compare, classify, and manipulate numerical values and their representations.</td>
                    <td><span style="color: blue;">Number sense, Basic arithmetic operations, Set theory</span></td>
                    <td><span style="color: red;">Understanding of measurement, Comparative analysis</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Concept of ratios</strong></td>
                    <td>The concept of ratios involves comparing two or more quantities to express their relative sizes or proportions. Ratios are fundamental in mathematics and are used in various fields such as finance, science, and statistics to analyze relationships between different entities.</td>
                    <td><span style="color: blue;">Basic arithmetic operations, Number sense</span></td>
                    <td><span style="color: red;">Understanding of fractions, Concept of proportions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Control Structures</strong></td>
                    <td>Control structures are fundamental programming constructs that dictate the flow of execution in a program, allowing for decision-making, iteration, and branching based on specified conditions. They are essential for creating dynamic and responsive algorithms in software development.</td>
                    <td><span style="color: blue;">Basic programming concepts</span></td>
                    <td><span style="color: red;">Data types and variables, Logical operators, Conditional statements, Loops and iteration</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Control structures (if statements, loops)</strong></td>
                    <td>Control structures are essential programming constructs that enable the execution of specific code blocks based on certain conditions (if statements) or the repetition of code blocks (loops). They are fundamental for implementing logic and flow in programming languages.</td>
                    <td><span style="color: blue;">Basic programming concepts</span></td>
                    <td><span style="color: red;">Data types, Boolean logic, Syntax of a programming language, Basic input/output operations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Coordinate System</strong></td>
                    <td>A coordinate system is a mathematical framework that uses numbers to uniquely determine the position of points or objects in space, typically defined by a set of axes and a reference point. It is essential for graphing functions, solving geometric problems, and understanding spatial relationships in mathematics and physics.</td>
                    <td><span style="color: blue;">Basic Algebra, Geometry, Understanding of Functions</span></td>
                    <td><span style="color: red;">Graphing Techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Counting</strong></td>
                    <td>Counting is the process of determining the quantity of items in a set, typically by assigning a unique number to each item in a sequential manner. It serves as a foundational concept in mathematics, enabling further exploration of arithmetic and number theory.</td>
                    <td><span style="color: blue;">Number recognition</span></td>
                    <td><span style="color: red;">Basic numeracy, Understanding of one-to-one correspondence, Ordinal numbers, Set theory basics</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Creativity</strong></td>
                    <td>Creativity is the ability to generate novel and valuable ideas or solutions, often involving the integration of diverse concepts and perspectives. It encompasses various cognitive processes and is essential in fields such as art, science, and problem-solving.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Critical Thinking, Problem Solving, Cognitive Flexibility, Emotional Intelligence, Basic Knowledge of Artistic Principles</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Critical thinking</strong></td>
                    <td>Critical thinking is the ability to analyze, evaluate, and synthesize information in order to make reasoned judgments and decisions. It involves questioning assumptions, identifying biases, and applying logical reasoning to solve problems.</td>
                    <td><span style="color: blue;">Logical reasoning</span></td>
                    <td><span style="color: red;">Argument analysis, Basic research skills, Problem-solving techniques, Understanding of cognitive biases</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Critical thinking skills</strong></td>
                    <td>Critical thinking skills involve the ability to analyze information, evaluate evidence, and construct reasoned arguments to make informed decisions. These skills are essential for problem-solving and effective communication in both academic and real-world contexts.</td>
                    <td><span style="color: blue;">Logical reasoning</span></td>
                    <td><span style="color: red;">Basic argumentation, Analytical skills, Information literacy, Problem-solving techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Data Management</strong></td>
                    <td>Data Management encompasses the practices, processes, and technologies used to collect, store, organize, and maintain data throughout its lifecycle, ensuring its accuracy, accessibility, and security. It is essential for effective decision-making and operational efficiency in various fields.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of data structures, Fundamentals of database systems, Knowledge of data modeling concepts, Introduction to data analysis techniques, Familiarity with data privacy and security principles</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Data Types</strong></td>
                    <td>Data types are classifications that specify which type of value a variable can hold, determining the operations that can be performed on that data. Understanding data types is essential for effective programming and data manipulation in computer science.</td>
                    <td><span style="color: blue;">Variables, Basic Programming Concepts, Control Structures</span></td>
                    <td><span style="color: red;">Memory Management</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Data Visualization</strong></td>
                    <td>Data Visualization is the graphical representation of information and data, utilizing visual elements like charts, graphs, and maps to communicate complex data insights clearly and effectively. It enables users to identify patterns, trends, and outliers in data through visual context.</td>
                    <td><span style="color: blue;">Basic Statistics, Data Analysis, Mathematics</span></td>
                    <td><span style="color: red;">Information Design, Computer Literacy</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Data structures</strong></td>
                    <td>Data structures are specialized formats for organizing, managing, and storing data in a computer so that it can be accessed and modified efficiently. They are fundamental to computer science and programming, enabling the development of efficient algorithms and applications.</td>
                    <td><span style="color: blue;">Basic programming concepts, Algorithms</span></td>
                    <td><span style="color: red;">Mathematics (discrete mathematics), Complexity analysis</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Data structures (arrays, lists)</strong></td>
                    <td>Data structures such as arrays and lists are fundamental components in computer science that allow for the organization and storage of data in a systematic way, enabling efficient access and manipulation. Understanding these structures is crucial for developing algorithms and solving computational problems.</td>
                    <td><span style="color: blue;">Basic programming concepts, Functions and procedures</span></td>
                    <td><span style="color: red;">Variables and data types, Control structures (loops and conditionals), Algorithmic thinking</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Discrete mathematics</strong></td>
                    <td>Discrete mathematics is the study of mathematical structures that are fundamentally discrete rather than continuous. It includes topics such as logic, set theory, combinatorics, graph theory, and algorithms, which are essential for computer science and related fields.</td>
                    <td><span style="color: blue;">Basic algebra, Logic, Set theory, Functions and relations</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Domain and range</strong></td>
                    <td>Domain and range are fundamental concepts in mathematics that describe the set of possible input values (domain) and the set of possible output values (range) of a function. Understanding these concepts is essential for analyzing the behavior of functions and their graphical representations.</td>
                    <td><span style="color: blue;">Functions, Sets, Graphing, Algebraic expressions</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Ethics in Research</strong></td>
                    <td>Ethics in Research refers to the moral principles and guidelines that govern the conduct of research, ensuring integrity, accountability, and respect for participants. It encompasses issues such as informed consent, confidentiality, and the responsible use of data.</td>
                    <td><span style="color: blue;">Research Methodology, Data Management</span></td>
                    <td><span style="color: red;">Basic Principles of Ethics, Informed Consent, Human Rights in Research</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with addition and subtraction</strong></td>
                    <td>Familiarity with addition and subtraction refers to the understanding and ability to perform basic arithmetic operations involving the combination and separation of quantities. This foundational skill is essential for further mathematical learning and everyday problem-solving.</td>
                    <td><span style="color: blue;">Basic counting skills</span></td>
                    <td><span style="color: red;">Understanding of numbers and numerals, Recognition of mathematical symbols, Concept of quantity and comparison</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with argument structures</strong></td>
                    <td>Familiarity with argument structures involves understanding the components and organization of arguments, including premises, conclusions, and the relationships between them. This knowledge is essential for critical thinking, effective communication, and engaging in logical discourse.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of logic, Knowledge of critical thinking principles, Familiarity with rhetorical devices, Introduction to philosophy, Understanding of language and semantics</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with basic algebra</strong></td>
                    <td>Familiarity with basic algebra involves understanding fundamental concepts such as variables, constants, and mathematical operations, which are essential for solving equations and manipulating algebraic expressions. It serves as a foundational skill necessary for more advanced mathematical topics and real-world problem-solving.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of arithmetic operations, Knowledge of integers and rational numbers, Basic understanding of mathematical notation, Ability to perform order of operations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with command line interfaces</strong></td>
                    <td>Familiarity with command line interfaces (CLIs) involves understanding how to interact with a computer's operating system through text-based commands, enabling users to perform tasks efficiently without a graphical user interface. This skill is essential for software development, system administration, and automation tasks.</td>
                    <td><span style="color: blue;">Basic computer literacy</span></td>
                    <td><span style="color: red;">Understanding of operating system concepts, Knowledge of file system structure, Familiarity with text editors, Basic programming concepts (optional but beneficial)</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with counting</strong></td>
                    <td>Familiarity with counting refers to the understanding and ability to enumerate objects, recognize numerical sequences, and apply basic counting principles in various contexts. This foundational skill is essential for developing more advanced mathematical concepts and problem-solving abilities.</td>
                    <td><span style="color: blue;">Number recognition</span></td>
                    <td><span style="color: red;">Understanding of one-to-one correspondence, Basic addition and subtraction, Ordinal numbers, Set theory basics</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with data storage concepts</strong></td>
                    <td>Familiarity with data storage concepts involves understanding the various methods and technologies used to store, retrieve, and manage data in digital systems. This knowledge is essential for effective data management and utilization in computing environments.</td>
                    <td><span style="color: blue;">Basic understanding of computer systems</span></td>
                    <td><span style="color: red;">Knowledge of data types and structures, Familiarity with file systems, Introduction to databases, Understanding of data retrieval methods</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with decimal number system</strong></td>
                    <td>The decimal number system, also known as base-10, is the standard system for denoting integer and non-integer numbers. It is based on ten symbols (0-9) and is widely used in everyday counting and arithmetic operations.</td>
                    <td><span style="color: blue;">Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)</span></td>
                    <td><span style="color: red;">Knowledge of place value concepts, Familiarity with whole numbers and integers, Introduction to number systems</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with fractions and decimals</strong></td>
                    <td>Familiarity with fractions and decimals involves understanding the representation and manipulation of numbers that are not whole, including their equivalences and operations. This foundational knowledge is essential for more advanced mathematical concepts and real-world applications.</td>
                    <td><span style="color: blue;">Understanding of whole numbers</span></td>
                    <td><span style="color: red;">Basic addition and subtraction skills, Concept of division, Number line comprehension, Understanding of ratios</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with logic and boolean expressions</strong></td>
                    <td>Familiarity with logic and boolean expressions involves understanding the principles of logical reasoning and the use of boolean algebra to manipulate true/false values. This knowledge is essential for fields such as computer science, mathematics, and philosophy, where logical operations are fundamental.</td>
                    <td><span style="color: blue;">Basic mathematics, Set theory, Basic programming concepts</span></td>
                    <td><span style="color: red;">Propositional logic, Truth tables</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with logical connectives</strong></td>
                    <td>Familiarity with logical connectives involves understanding the basic operators used in formal logic, such as conjunction, disjunction, negation, implication, and equivalence, which are essential for constructing and interpreting logical statements. Mastery of these connectives is crucial for engaging in logical reasoning and problem-solving in mathematics, computer science, and philosophy.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of propositional logic, Knowledge of truth tables, Familiarity with logical statements, Introduction to set theory</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with mathematical concepts</strong></td>
                    <td>Familiarity with mathematical concepts refers to the understanding and recognition of fundamental mathematical ideas, principles, and operations that form the foundation for more advanced mathematical reasoning and problem-solving. This familiarity is essential for engaging with various fields of study that rely on mathematical applications.</td>
                    <td><span style="color: blue;">Basic arithmetic operations (addition, subtraction, multiplication, division)</span></td>
                    <td><span style="color: red;">Understanding of numbers (whole numbers, fractions, decimals), Introduction to algebra (variables, expressions, equations), Basic geometry concepts (shapes, area, perimeter), Understanding of mathematical notation and terminology</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with multiplication and division</strong></td>
                    <td>Familiarity with multiplication and division involves understanding the concepts of repeated addition and equal grouping, which are foundational operations in arithmetic. Mastery of these operations is essential for solving more complex mathematical problems and real-world applications.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of addition, Understanding of subtraction, Knowledge of number sense, Ability to recognize patterns in numbers</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with problem-solving techniques</strong></td>
                    <td>Familiarity with problem-solving techniques involves understanding various methodologies and strategies used to identify, analyze, and resolve issues effectively. This knowledge enables individuals to approach challenges systematically and enhances critical thinking skills.</td>
                    <td><span style="color: blue;">Critical thinking, Basic mathematics, Analytical reasoning</span></td>
                    <td><span style="color: red;">Research methods, Decision-making processes</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with programming concepts</strong></td>
                    <td>Familiarity with programming concepts refers to the understanding of fundamental principles and terminology used in programming, such as variables, control structures, data types, and algorithms. This foundational knowledge is essential for anyone looking to learn programming languages and develop software applications.</td>
                    <td><span style="color: blue;">Basic mathematics, Logical reasoning, Understanding of computer systems, Basic problem-solving skills</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with project management principles</strong></td>
                    <td>Familiarity with project management principles involves understanding the foundational concepts and methodologies that guide the planning, execution, and completion of projects. This knowledge is essential for effectively managing resources, timelines, and stakeholder expectations in various organizational contexts.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of organizational behavior, Fundamentals of time management, Introduction to resource allocation, Basic communication skills, Understanding of team dynamics</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with set theory</strong></td>
                    <td>Familiarity with set theory involves understanding the fundamental concepts of sets, including operations such as union, intersection, and difference, as well as the properties and relations of sets. It serves as a foundational aspect of mathematics and logic, essential for advanced studies in various fields such as mathematics, computer science, and statistics.</td>
                    <td><span style="color: blue;">Basic arithmetic</span></td>
                    <td><span style="color: red;">Understanding of numbers and operations, Introduction to logic, Familiarity with mathematical notation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with shapes and their properties</strong></td>
                    <td>This topic involves understanding various geometric shapes, including their characteristics, classifications, and the relationships between them. It is foundational for further studies in geometry and spatial reasoning.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of geometry, Knowledge of points, lines, and angles, Familiarity with two-dimensional and three-dimensional figures, Introduction to measurement concepts</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with syllogisms</strong></td>
                    <td>Familiarity with syllogisms involves understanding the structure of deductive reasoning where a conclusion is drawn from two premises. It is a foundational aspect of logic that helps in evaluating arguments and reasoning processes.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of logic, Knowledge of premises and conclusions, Familiarity with deductive reasoning, Understanding of logical validity and soundness</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Familiarity with truth tables</strong></td>
                    <td>Familiarity with truth tables involves understanding the systematic representation of logical expressions and their corresponding truth values. This foundational concept is essential in fields such as mathematics, computer science, and philosophy for analyzing logical propositions and circuits.</td>
                    <td><span style="color: blue;">Set theory</span></td>
                    <td><span style="color: red;">Basic logic, Propositional logic, Logical operators (AND, OR, NOT), Boolean algebra</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Foundational knowledge of data structures</strong></td>
                    <td>Foundational knowledge of data structures involves understanding the basic ways of organizing and storing data in a computer, which is essential for efficient data manipulation and retrieval. This knowledge serves as a critical building block for more advanced topics in computer science and software development.</td>
                    <td><span style="color: blue;">Basic programming concepts, Understanding of algorithms, Mathematical reasoning</span></td>
                    <td><span style="color: red;">Familiarity with computational complexity, Basic knowledge of computer memory architecture</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Functions and procedures</strong></td>
                    <td>Functions and procedures are fundamental programming constructs that allow for the encapsulation of code into reusable blocks, enabling modular programming and improving code organization. They facilitate the execution of specific tasks and can take inputs (parameters) and return outputs (results).</td>
                    <td><span style="color: blue;">Basic programming concepts, Control structures (if statements, loops)</span></td>
                    <td><span style="color: red;">Data types and variables, Scope and lifetime of variables</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Functions and relations</strong></td>
                    <td>Functions and relations are fundamental concepts in mathematics that describe the relationship between sets of inputs and outputs. A function assigns exactly one output to each input, while a relation can associate multiple outputs with a single input.</td>
                    <td><span style="color: blue;">Sets, Ordered pairs, Domain and range, Basic algebra</span></td>
                    <td><span style="color: red;">Graphing techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Graphing concepts</strong></td>
                    <td>Graphing concepts involve the representation of mathematical relationships and data visually using graphs, which can include lines, bars, and other shapes. Understanding these concepts is essential for interpreting data and solving mathematical problems effectively.</td>
                    <td><span style="color: blue;">Basic arithmetic, Functions and relations, Algebraic expressions</span></td>
                    <td><span style="color: red;">Coordinate systems, Data interpretation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Graphing on a number line</strong></td>
                    <td>Graphing on a number line involves representing numbers as points on a straight line, where the position of each point corresponds to its value. This visual representation helps in understanding numerical relationships and performing operations such as addition and subtraction.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of numbers and their values, Basic knowledge of the number line concept, Familiarity with positive and negative integers, Ability to perform basic arithmetic operations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to algorithms</strong></td>
                    <td>Introduction to algorithms is a foundational course that covers the design, analysis, and implementation of algorithms, emphasizing their efficiency and effectiveness in solving computational problems. It serves as a critical building block for further studies in computer science and software engineering.</td>
                    <td><span style="color: blue;">Basic programming skills</span></td>
                    <td><span style="color: red;">Understanding of data structures, Mathematical foundations (discrete mathematics), Problem-solving techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to argumentation</strong></td>
                    <td>Introduction to argumentation is a foundational course that explores the principles and techniques of constructing, analyzing, and evaluating arguments. It emphasizes critical thinking skills and the role of reasoning in persuasive communication.</td>
                    <td><span style="color: blue;">Critical thinking</span></td>
                    <td><span style="color: red;">Basic logic, Understanding of rhetoric, Reading comprehension, Writing skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to division</strong></td>
                    <td>Introduction to division is a foundational mathematical concept that involves understanding how to split a quantity into equal parts or groups. It serves as a critical building block for more advanced arithmetic operations and problem-solving skills.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Understanding of addition, Understanding of subtraction, Knowledge of multiplication, Familiarity with numbers and counting</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to equations and inequalities</strong></td>
                    <td>This topic introduces the fundamental concepts of equations and inequalities, including their definitions, properties, and methods for solving them. It serves as a foundation for understanding more complex algebraic concepts and real-world applications.</td>
                    <td><span style="color: blue;">Basic arithmetic operations, Understanding of variables and constants</span></td>
                    <td><span style="color: red;">Knowledge of mathematical expressions, Familiarity with the concept of equality</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to functions and relations</strong></td>
                    <td>This topic introduces the concepts of functions and relations, focusing on their definitions, properties, and graphical representations. It provides foundational knowledge essential for understanding more complex mathematical concepts and their applications.</td>
                    <td><span style="color: blue;">Basic arithmetic operations</span></td>
                    <td><span style="color: red;">Understanding of ordered pairs, Knowledge of coordinate systems, Introduction to sets and set notation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to logical reasoning</strong></td>
                    <td>Introduction to logical reasoning is a foundational course that explores the principles of valid reasoning, argument structure, and critical thinking skills necessary for evaluating arguments and making sound conclusions. It serves as a basis for understanding more complex logical concepts and applications in various fields.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of mathematics, Familiarity with reading comprehension, Introduction to critical thinking, Basic knowledge of formal and informal arguments</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to mathematical logic</strong></td>
                    <td>Introduction to mathematical logic is a foundational course that explores the principles of formal reasoning, including the structure of arguments, the nature of mathematical proofs, and the formulation of logical statements. It serves as a gateway to understanding more advanced topics in mathematics and computer science.</td>
                    <td><span style="color: blue;">Basic algebra, Set theory</span></td>
                    <td><span style="color: red;">Propositional logic, Quantifiers and predicates</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to mathematical notation</strong></td>
                    <td>This topic covers the basic symbols and conventions used in mathematics to represent numbers, operations, and relationships. Understanding mathematical notation is essential for interpreting and communicating mathematical ideas effectively.</td>
                    <td><span style="color: blue;">Basic arithmetic operations, Understanding of numbers (integers, fractions, decimals)</span></td>
                    <td><span style="color: red;">Familiarity with variables and expressions, Basic logic and reasoning skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to mathematical reasoning</strong></td>
                    <td>This topic covers the foundational principles of logical thinking and argumentation in mathematics, emphasizing the development of proofs and the understanding of mathematical concepts through rigorous reasoning. It serves as an essential framework for students to approach mathematical problems systematically and critically.</td>
                    <td><span style="color: blue;">Basic algebra, Familiarity with logical operators, Basic problem-solving skills</span></td>
                    <td><span style="color: red;">Understanding of set theory, Introduction to functions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to mathematical symbols and notation</strong></td>
                    <td>This topic covers the basic symbols and notation used in mathematics, including operators, variables, and functions, which are essential for communicating mathematical ideas clearly and effectively. Understanding these symbols is foundational for further study in mathematics and related fields.</td>
                    <td><span style="color: blue;">Basic arithmetic operations</span></td>
                    <td><span style="color: red;">Understanding of numbers (natural, whole, integers, rational, and real), Introduction to algebraic concepts, Familiarity with basic geometric shapes and properties</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to programming concepts</strong></td>
                    <td>This topic covers the foundational principles of programming, including basic syntax, data types, control structures, and algorithms, aimed at beginners to develop problem-solving skills through coding. It serves as a gateway to more advanced programming and software development topics.</td>
                    <td><span style="color: blue;">Basic mathematics, Logical reasoning</span></td>
                    <td><span style="color: red;">Understanding of computer fundamentals, Familiarity with operating systems</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Introduction to variables and expressions</strong></td>
                    <td>This topic introduces the concept of variables as symbols that represent numbers or values in mathematical expressions, and explores how these expressions can be manipulated and evaluated. It lays the foundation for understanding algebraic concepts and problem-solving techniques.</td>
                    <td><span style="color: blue;">Introduction to mathematical notation, Basic problem-solving skills</span></td>
                    <td><span style="color: red;">Understanding of basic arithmetic operations, Familiarity with numbers and their properties</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Knowledge of conditional statements</strong></td>
                    <td>Knowledge of conditional statements involves understanding how to use 'if', 'else if', and 'else' constructs to control the flow of execution in programming and logic. This foundational concept is essential for making decisions based on specific conditions in algorithms and code.</td>
                    <td><span style="color: blue;">Basic programming concepts</span></td>
                    <td><span style="color: red;">Logical operators, Boolean algebra, Control structures</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Knowledge of exponents</strong></td>
                    <td>Knowledge of exponents involves understanding the mathematical notation and rules for expressing numbers raised to a power, which indicates repeated multiplication. This concept is foundational for various mathematical operations and applications in algebra, science, and engineering.</td>
                    <td><span style="color: blue;">Basic arithmetic operations, Order of operations</span></td>
                    <td><span style="color: red;">Understanding of multiplication, Introduction to integers</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Knowledge of functions and their properties</strong></td>
                    <td>This topic encompasses the understanding of mathematical functions, including their definitions, types, and characteristics such as domain, range, continuity, and behavior under transformations. Mastery of functions is essential for advanced studies in calculus, algebra, and various applications in science and engineering.</td>
                    <td><span style="color: blue;">Basic arithmetic operations</span></td>
                    <td><span style="color: red;">Algebraic expressions and equations, Graphing techniques, Concept of variables and constants, Understanding of relations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Knowledge of operating systems</strong></td>
                    <td>Knowledge of operating systems encompasses understanding the software that manages computer hardware and software resources, as well as providing common services for computer programs. This knowledge is essential for grasping how applications interact with the hardware and how system resources are allocated and managed.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic computer architecture, Fundamentals of programming, Data structures and algorithms, Computer networks, Introduction to software engineering</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Knowledge of spatial reasoning</strong></td>
                    <td>Knowledge of spatial reasoning refers to the ability to visualize and manipulate objects in a three-dimensional space, which is essential for problem-solving in fields such as mathematics, engineering, and architecture. This cognitive skill involves understanding spatial relationships and dimensions, enabling individuals to navigate and interact with their environment effectively.</td>
                    <td><span style="color: blue;">Basic algebra</span></td>
                    <td><span style="color: red;">Basic geometry, Understanding of shapes and their properties, Familiarity with coordinate systems, Introduction to visual perception</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Knowledge of the Cartesian coordinate system</strong></td>
                    <td>The Cartesian coordinate system is a mathematical framework that uses two perpendicular axes (x and y) to define the position of points in a two-dimensional space. It serves as a foundational concept in geometry, algebra, and various applications in science and engineering.</td>
                    <td><span style="color: blue;">Basic arithmetic operations</span></td>
                    <td><span style="color: red;">Understanding of numbers and number lines, Introduction to geometry, Concept of points and lines, Basic algebraic expressions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Knowledge of the decimal system</strong></td>
                    <td>The decimal system is a base-10 numeral system that uses ten digits (0-9) to represent numbers, facilitating arithmetic operations and numerical representation. Understanding this system is essential for performing calculations and interpreting numerical data in various fields.</td>
                    <td><span style="color: blue;">Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)</span></td>
                    <td><span style="color: red;">Familiarity with number recognition and counting, Knowledge of place value concepts, Basic understanding of fractions and decimals</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Knowledge of version control systems</strong></td>
                    <td>Knowledge of version control systems involves understanding the tools and practices used to manage changes to source code over time, facilitating collaboration among developers and maintaining a history of project modifications. This knowledge is essential for software development, enabling teams to track progress, revert changes, and manage multiple versions of code efficiently.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of software development concepts, Familiarity with programming languages, Knowledge of file systems and directory structures, Understanding of collaborative workflows in software projects</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Line segments and rays</strong></td>
                    <td>Line segments and rays are fundamental concepts in geometry that represent parts of a line. A line segment has two endpoints, while a ray has one endpoint and extends infinitely in one direction.</td>
                    <td><span style="color: blue;">Basic geometric shapes</span></td>
                    <td><span style="color: red;">Points, Lines, Coordinate system</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Logic</strong></td>
                    <td>Logic is the study of reasoning, argumentation, and the principles of valid inference. It encompasses the analysis of propositions, the structure of arguments, and the evaluation of truth values.</td>
                    <td><span style="color: blue;">Basic Mathematics, Set Theory</span></td>
                    <td><span style="color: red;">Critical Thinking, Philosophy</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Logic and Propositional Calculus</strong></td>
                    <td>Logic and Propositional Calculus is a branch of mathematical logic that deals with the formalization of logical statements and their relationships through symbolic representation and rules of inference. It provides the foundational framework for understanding logical reasoning, validity, and the structure of arguments.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of logical reasoning, Familiarity with mathematical notation, Introduction to set theory, Basic algebra skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Logic and reasoning</strong></td>
                    <td>Logic and reasoning encompass the systematic study of valid inference, argument structure, and the principles of correct reasoning. This topic is essential for developing critical thinking skills and understanding how to construct and evaluate arguments effectively.</td>
                    <td><span style="color: blue;">Basic mathematics, Understanding of propositions</span></td>
                    <td><span style="color: red;">Familiarity with deductive and inductive reasoning, Knowledge of logical operators, Introduction to critical thinking</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Logical Reasoning</strong></td>
                    <td>Logical reasoning is the process of using structured thinking to analyze arguments, identify fallacies, and derive conclusions based on premises. It encompasses both deductive and inductive reasoning techniques to evaluate the validity of statements and arguments.</td>
                    <td><span style="color: blue;">Basic Mathematics, Set Theory</span></td>
                    <td><span style="color: red;">Critical Thinking, Propositional Logic</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Logical reasoning and problem-solving skills</strong></td>
                    <td>Logical reasoning and problem-solving skills involve the ability to analyze information, identify patterns, and apply logical principles to derive solutions to complex problems. These skills are essential for effective decision-making and critical thinking in various academic and real-world contexts.</td>
                    <td><span style="color: blue;">Basic mathematics, Critical thinking, Understanding of logical operators</span></td>
                    <td><span style="color: red;">Familiarity with deductive and inductive reasoning, Problem identification techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Logical thinking</strong></td>
                    <td>Logical thinking is the ability to reason systematically and draw valid conclusions based on premises and evidence. It involves the application of formal logic principles to solve problems and make decisions effectively.</td>
                    <td><span style="color: blue;">Critical thinking, Basic mathematics, Understanding of logical operators, Problem-solving skills</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Mathematical Foundations</strong></td>
                    <td>Mathematical Foundations encompasses the basic concepts and principles that underlie mathematics, including logic, set theory, and the structure of mathematical reasoning. It serves as the groundwork for more advanced mathematical studies and applications.</td>
                    <td><span style="color: blue;">Basic Arithmetic, Algebra, Logic, Set Theory, Functions</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Mathematical Operations</strong></td>
                    <td>Mathematical operations refer to the basic processes of arithmetic, including addition, subtraction, multiplication, and division, which are foundational for performing calculations and solving mathematical problems. These operations form the building blocks for more complex mathematical concepts and applications.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Number Recognition, Basic Counting, Understanding of Place Value, Introduction to Fractions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Mathematical Proof Techniques</strong></td>
                    <td>Mathematical proof techniques are systematic methods used to establish the validity of mathematical statements through logical reasoning and argumentation. These techniques include direct proof, proof by contradiction, proof by induction, and others, serving as foundational tools in mathematics.</td>
                    <td><span style="color: blue;">Basic Algebra, Set Theory, Logical Reasoning, Functions and Relations</span></td>
                    <td><span style="color: red;">Elementary Number Theory</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Mathematical logic</strong></td>
                    <td>Mathematical logic is a subfield of mathematics that deals with formal systems, symbolic reasoning, and the principles of valid inference. It encompasses the study of propositional and predicate logic, as well as the foundations of mathematics and computability theory.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic set theory, Propositional logic, Predicate logic, Elementary proof techniques, Introduction to formal languages</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Mathematical notation</strong></td>
                    <td>Mathematical notation is a system of symbols and signs used to represent numbers, operations, and relationships in mathematics, enabling precise communication of mathematical ideas. It serves as a universal language that facilitates the expression of complex concepts in a concise manner.</td>
                    <td><span style="color: blue;">Basic arithmetic, Algebraic expressions, Set theory, Logic and reasoning, Functions and relations</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Mathematics</strong></td>
                    <td>Mathematics is the abstract science of number, quantity, and space, either as abstract concepts (pure mathematics), or as applied to other disciplines such as physics and engineering (applied mathematics). It encompasses various fields including arithmetic, algebra, geometry, and calculus, providing tools for problem-solving and logical reasoning.</td>
                    <td><span style="color: blue;">Arithmetic, Basic Algebra, Geometry, Logical Reasoning</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Measurement concepts</strong></td>
                    <td>Measurement concepts encompass the principles and methods used to quantify physical properties, enabling the comparison and analysis of various phenomena. This topic includes understanding units of measurement, precision, accuracy, and the significance of measurement in scientific inquiry.</td>
                    <td><span style="color: blue;">Basic arithmetic</span></td>
                    <td><span style="color: red;">Understanding of units and conversions, Introduction to scientific methods, Fundamental concepts of physical properties</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Number recognition</strong></td>
                    <td>Number recognition is the ability to identify and understand numerical symbols and their corresponding values. This foundational skill is essential for early mathematics learning and is often developed in preschool and kindergarten settings.</td>
                    <td><span style="color: blue;">Basic counting skills</span></td>
                    <td><span style="color: red;">Understanding of quantity, Familiarity with numerical symbols, Visual discrimination skills</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Number sense</strong></td>
                    <td>Number sense refers to the intuitive understanding of numbers, their relationships, and the ability to perform mental calculations and estimations. It encompasses skills such as recognizing quantities, understanding numerical patterns, and applying mathematical reasoning in various contexts.</td>
                    <td><span style="color: blue;">Basic counting skills, Understanding of whole numbers</span></td>
                    <td><span style="color: red;">Familiarity with number lines, Concept of quantity and measurement, Basic addition and subtraction</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Order of operations (PEMDAS/BODMAS)</strong></td>
                    <td>The order of operations is a mathematical rule that dictates the sequence in which operations should be performed to accurately evaluate expressions. It is commonly remembered using the acronyms PEMDAS (Parentheses, Exponents, Multiplication and Division, Addition and Subtraction) or BODMAS (Brackets, Orders, Division and Multiplication, Addition and Subtraction).</td>
                    <td><span style="color: blue;">Basic arithmetic operations (addition, subtraction, multiplication, division)</span></td>
                    <td><span style="color: red;">Understanding of numerical expressions, Knowledge of mathematical symbols and notation, Familiarity with parentheses and their use in grouping terms</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Order of operations rules</strong></td>
                    <td>The order of operations rules are a set of guidelines used to determine the sequence in which mathematical operations should be performed to accurately evaluate expressions. This ensures consistency and correctness in calculations involving multiple operations such as addition, subtraction, multiplication, and division.</td>
                    <td><span style="color: blue;">Basic arithmetic operations, Understanding of parentheses</span></td>
                    <td><span style="color: red;">Knowledge of mathematical symbols, Introduction to algebraic expressions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Ordered pairs</strong></td>
                    <td>An ordered pair is a pair of elements where the order in which they are listed is significant, typically represented as (a, b). This concept is foundational in mathematics, particularly in coordinate systems and relations.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">sets, elements, coordinates, relations, functions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Place value system</strong></td>
                    <td>The place value system is a numerical system that assigns a value to a digit based on its position within a number, allowing for the representation of large numbers and the execution of arithmetic operations. It is foundational for understanding how numbers are structured and manipulated in mathematics.</td>
                    <td><span style="color: blue;">Understanding of numbers, Basic arithmetic operations</span></td>
                    <td><span style="color: red;">Concept of zero, Number representation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Proof Techniques</strong></td>
                    <td>Proof techniques are methods used in mathematics and computer science to establish the validity of statements or theorems. Common techniques include direct proof, proof by contradiction, proof by induction, and proof by contrapositive.</td>
                    <td><span style="color: blue;">Logical reasoning, Set theory, Basic algebra, Functions and relations, Mathematical notation</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Proof techniques</strong></td>
                    <td>Proof techniques are systematic methods used to establish the validity of mathematical statements through logical reasoning. Common techniques include direct proof, proof by contradiction, proof by induction, and contrapositive proof.</td>
                    <td><span style="color: blue;">Mathematical logic, Set theory, Basic algebra, Functions and relations</span></td>
                    <td><span style="color: red;">Quantifiers</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Properties of parallel and intersecting lines</strong></td>
                    <td>This topic explores the characteristics and relationships between parallel lines, which never meet, and intersecting lines, which cross at a point. Understanding these properties is essential for solving geometric problems and proving theorems related to angles and shapes.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic understanding of points, lines, and planes, Knowledge of angles and their measurements, Familiarity with geometric terminology, Introduction to the concepts of congruence and similarity</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Research Methodology</strong></td>
                    <td>Research Methodology refers to the systematic approach and techniques used to conduct research, encompassing the principles, procedures, and strategies for collecting and analyzing data to answer specific research questions. It provides a framework for researchers to ensure the validity and reliability of their findings.</td>
                    <td><span style="color: blue;">Basic Statistics, Data Collection Techniques, Ethics in Research</span></td>
                    <td><span style="color: red;">Research Design, Critical Thinking</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Sampling Methods</strong></td>
                    <td>Sampling methods are techniques used to select a subset of individuals or observations from a larger population to make inferences about that population. These methods are crucial in statistics for ensuring that samples accurately represent the population being studied.</td>
                    <td><span style="color: blue;">Basic Statistics, Probability Theory, Data Collection Techniques, Descriptive Statistics</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Set theory</strong></td>
                    <td>Set theory is a branch of mathematical logic that studies sets, which are collections of objects. It provides the foundational framework for various areas of mathematics, including functions, relations, and cardinality.</td>
                    <td><span style="color: blue;">Mathematical notation</span></td>
                    <td><span style="color: red;">Basic logic, Elementary algebra, Understanding of functions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Sets</strong></td>
                    <td>Sets are fundamental mathematical objects that represent collections of distinct elements, often used to define relationships and operations in mathematics. They serve as the foundation for various branches of mathematics, including algebra, calculus, and discrete mathematics.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic counting principles, Understanding of numbers and operations, Introduction to logic, Familiarity with mathematical notation</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Simple addition and subtraction</strong></td>
                    <td>Simple addition and subtraction are fundamental arithmetic operations that involve combining quantities (addition) or determining the difference between quantities (subtraction). Mastery of these concepts is essential for further mathematical learning and everyday problem-solving.</td>
                    <td><span style="color: blue;">Understanding of numbers, Basic counting skills, Concept of quantity</span></td>
                    <td><span style="color: red;">Recognition of mathematical symbols</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Simple fractions and decimals</strong></td>
                    <td>Simple fractions and decimals are fundamental mathematical concepts that represent parts of a whole. Understanding these concepts is essential for performing basic arithmetic operations and for grasping more complex mathematical ideas.</td>
                    <td><span style="color: blue;">Understanding of whole numbers, Place value system</span></td>
                    <td><span style="color: red;">Basic addition and subtraction, Concept of division</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Simple geometric shapes and their properties</strong></td>
                    <td>This topic explores basic geometric shapes such as circles, triangles, squares, and rectangles, focusing on their defining characteristics, relationships, and the principles that govern their properties. Understanding these shapes is fundamental for further studies in geometry and spatial reasoning.</td>
                    <td><span style="color: blue;">Basic arithmetic</span></td>
                    <td><span style="color: red;">Understanding of points and lines, Introduction to angles, Basic measurement concepts</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Statistical Analysis</strong></td>
                    <td>Statistical analysis involves the collection, examination, interpretation, and presentation of data to uncover patterns and inform decision-making. It employs various statistical methods to summarize data and draw conclusions based on empirical evidence.</td>
                    <td><span style="color: blue;">Basic Mathematics, Probability Theory, Descriptive Statistics, Data Visualization</span></td>
                    <td><span style="color: red;">Inferential Statistics</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of Functions</strong></td>
                    <td>Understanding of functions involves comprehending the concept of a relation between a set of inputs and outputs, where each input is associated with exactly one output. This foundational topic is crucial in mathematics and serves as a building block for more advanced concepts in algebra, calculus, and beyond.</td>
                    <td><span style="color: blue;">Basic Algebra, Set Theory, Understanding of Variables</span></td>
                    <td><span style="color: red;">Graphing Techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of addition and subtraction</strong></td>
                    <td>Addition and subtraction are fundamental arithmetic operations that involve combining quantities and determining the difference between them, respectively. Mastery of these concepts is essential for developing further mathematical skills and problem-solving abilities.</td>
                    <td><span style="color: blue;">Number recognition, Basic number sense</span></td>
                    <td><span style="color: red;">Counting skills, Understanding of quantity</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of algorithms</strong></td>
                    <td>Understanding of algorithms involves grasping the principles and techniques used to solve problems through a step-by-step procedure or formula. It encompasses the study of algorithm design, analysis, and optimization for efficient problem-solving in computer science.</td>
                    <td><span style="color: blue;">Basic programming concepts, Data structures, Mathematical foundations (e.g., discrete mathematics)</span></td>
                    <td><span style="color: red;">Problem-solving techniques, Complexity analysis</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of algorithms and data structures</strong></td>
                    <td>This topic involves the study of algorithms, which are step-by-step procedures for solving problems, and data structures, which are ways of organizing and storing data. Mastery of this topic is essential for efficient software development and problem-solving in computer science.</td>
                    <td><span style="color: blue;">Basic programming concepts, Mathematical foundations (e.g., discrete mathematics)</span></td>
                    <td><span style="color: red;">Complexity analysis, Basic understanding of computer architecture, Familiarity with recursion</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of basic arithmetic</strong></td>
                    <td>Basic arithmetic involves the foundational mathematical operations of addition, subtraction, multiplication, and division, which are essential for solving numerical problems and understanding more complex mathematical concepts. Mastery of these operations is crucial for everyday calculations and further mathematical learning.</td>
                    <td><span style="color: blue;">Number recognition, Counting, Understanding of numerical order</span></td>
                    <td><span style="color: red;">Basic concepts of quantity</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)</strong></td>
                    <td>This topic encompasses the foundational mathematical operations that form the basis for more complex calculations and problem-solving. Mastery of these operations is essential for everyday mathematics and further mathematical learning.</td>
                    <td><span style="color: blue;">Number recognition, Basic counting skills, Concept of quantity</span></td>
                    <td><span style="color: red;">Understanding of number values</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of big O notation</strong></td>
                    <td>Big O notation is a mathematical concept used to describe the upper bound of an algorithm's time or space complexity, providing a high-level understanding of its efficiency in relation to input size. It helps in comparing the performance of different algorithms and understanding their scalability.</td>
                    <td><span style="color: blue;">Basic understanding of algorithms</span></td>
                    <td><span style="color: red;">Familiarity with data structures, Knowledge of mathematical functions, Introduction to computational complexity</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of computer systems</strong></td>
                    <td>Understanding computer systems involves comprehending the fundamental components and functions of hardware and software, including how they interact to perform tasks and process data. This knowledge is essential for troubleshooting, system design, and optimizing performance in various computing environments.</td>
                    <td><span style="color: blue;">Basic computer literacy</span></td>
                    <td><span style="color: red;">Fundamentals of programming, Introduction to operating systems, Basic networking concepts, Digital logic design</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of coordinate systems</strong></td>
                    <td>Understanding of coordinate systems involves the study of systems that define a set of values used to represent points in space, typically through numerical coordinates. This topic is fundamental in mathematics, physics, and engineering, as it provides the framework for graphing and analyzing spatial relationships.</td>
                    <td><span style="color: blue;">Basic algebra, Geometry</span></td>
                    <td><span style="color: red;">Understanding of functions, Graphing techniques</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of coordinates and the Cartesian plane</strong></td>
                    <td>This topic involves the comprehension of the Cartesian coordinate system, which uses ordered pairs to define the position of points in a two-dimensional space. It serves as a foundational concept in geometry and algebra, facilitating the representation and analysis of geometric shapes and relationships.</td>
                    <td><span style="color: blue;">Basic arithmetic operations</span></td>
                    <td><span style="color: red;">Understanding of numbers and number lines, Introduction to geometry, Concept of points and lines</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of geometric shapes</strong></td>
                    <td>Understanding of geometric shapes involves recognizing, classifying, and analyzing two-dimensional and three-dimensional figures based on their properties, such as sides, angles, and symmetry. This foundational knowledge is essential for further studies in geometry, spatial reasoning, and various applications in mathematics and science.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic concepts of points, lines, and angles, Introduction to measurement and units, Understanding of symmetry and congruence, Familiarity with basic mathematical operations</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of logic and problem-solving</strong></td>
                    <td>This topic encompasses the principles of logical reasoning and the methodologies used to approach and resolve complex problems. It involves the application of deductive and inductive reasoning to analyze situations and derive solutions effectively.</td>
                    <td><span style="color: blue;">Introduction to logical reasoning, Critical thinking skills</span></td>
                    <td><span style="color: red;">Basic arithmetic and mathematical concepts, Familiarity with algorithms and basic programming concepts</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of logical operators</strong></td>
                    <td>Logical operators are symbols or words used to connect two or more propositions in logic, allowing for the construction of complex logical statements. Mastery of these operators is essential for problem-solving in mathematics, computer science, and philosophy.</td>
                    <td><span style="color: blue;">Familiarity with truth tables</span></td>
                    <td><span style="color: red;">Basic understanding of propositional logic, Knowledge of logical statements, Introduction to Boolean algebra</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of mathematical logic</strong></td>
                    <td>Mathematical logic is a subfield of mathematics that deals with formal systems, symbolic reasoning, and the principles of valid inference. It encompasses the study of propositional and predicate logic, as well as the foundations of mathematical proofs and set theory.</td>
                    <td><span style="color: blue;">Basic algebra, Set theory, Proof techniques, Functions and relations</span></td>
                    <td><span style="color: red;">Propositional logic</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of numbers</strong></td>
                    <td>Understanding of numbers encompasses the ability to recognize, interpret, and manipulate numerical values and concepts. It serves as a foundational skill for mathematical reasoning and problem-solving across various disciplines.</td>
                    <td><span style="color: blue;">Number recognition, Basic counting skills</span></td>
                    <td><span style="color: red;">Understanding of quantity, Introduction to mathematical symbols, Basic addition and subtraction</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of numbers (integers, fractions, decimals)</strong></td>
                    <td>This topic encompasses the comprehension and manipulation of various types of numbers, including whole numbers (integers), parts of whole numbers (fractions), and numbers expressed in decimal form. Mastery of these concepts is essential for performing arithmetic operations and solving real-world mathematical problems.</td>
                    <td><span style="color: blue;">Basic number recognition, Concept of quantity, Basic arithmetic operations (addition, subtraction, multiplication, division)</span></td>
                    <td><span style="color: red;">Understanding of the number line</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of numbers (natural, whole, integers, rational, and real numbers)</strong></td>
                    <td>This topic covers the classification and properties of different types of numbers, including natural numbers, whole numbers, integers, rational numbers, and real numbers, providing a foundational understanding of numerical systems used in mathematics.</td>
                    <td><span style="color: blue;">Basic arithmetic operations (addition, subtraction, multiplication, division)</span></td>
                    <td><span style="color: red;">Understanding of number lines, Concept of sets and subsets, Basic properties of operations (commutative, associative, distributive)</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of numerical order</strong></td>
                    <td>Understanding of numerical order involves recognizing and arranging numbers in a sequence based on their value, which is fundamental for performing arithmetic operations and solving mathematical problems. This concept is essential for developing number sense and is foundational for more advanced mathematical topics.</td>
                    <td><span style="color: blue;">Number recognition, Basic counting skills</span></td>
                    <td><span style="color: red;">Understanding of greater than and less than, Familiarity with number lines</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of parentheses</strong></td>
                    <td>The understanding of parentheses involves recognizing their role in mathematical expressions and programming syntax as a means to indicate order of operations or to group elements. Mastery of this concept is essential for accurate computation and logical structuring in various fields, including mathematics and computer science.</td>
                    <td><span style="color: blue;">Basic arithmetic operations, Order of operations (PEMDAS/BODMAS), Algebraic expressions, Logical reasoning</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of place value</strong></td>
                    <td>Place value is the numerical value that a digit holds based on its position within a number, which is essential for understanding the structure of the decimal number system. Mastery of place value enables students to perform operations with multi-digit numbers and comprehend larger numerical concepts.</td>
                    <td><span style="color: blue;">Basic counting skills, Knowledge of the decimal system</span></td>
                    <td><span style="color: red;">Understanding of digits and numbers, Ability to compare and order numbers</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of propositions</strong></td>
                    <td>Understanding of propositions involves grasping the nature of declarative statements that can be classified as true or false. This foundational concept is essential in logic, mathematics, and philosophy, as it underpins more complex reasoning and argumentation structures.</td>
                    <td><span style="color: blue;">Set theory, Critical thinking</span></td>
                    <td><span style="color: red;">Basic logic, Truth values, Logical connectives</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of software development life cycle (SDLC)</strong></td>
                    <td>The software development life cycle (SDLC) is a structured process that outlines the stages involved in developing software applications, from initial planning and requirements gathering to design, implementation, testing, deployment, and maintenance. Understanding SDLC is crucial for ensuring that software projects are completed efficiently and meet user requirements.</td>
                    <td><span style="color: blue;">Basic programming concepts</span></td>
                    <td><span style="color: red;">Fundamentals of project management, Software engineering principles, Requirements analysis techniques, Testing methodologies</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of symbols and operations</strong></td>
                    <td>This topic encompasses the ability to recognize, interpret, and manipulate mathematical symbols and operations, which are foundational for problem-solving in mathematics. It is essential for developing skills in algebra, arithmetic, and higher-level mathematics.</td>
                    <td><span style="color: blue;">Basic number sense, Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)</span></td>
                    <td><span style="color: red;">Recognition of mathematical symbols, Familiarity with the concept of variables, Introduction to mathematical expressions</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of variables and constants</strong></td>
                    <td>This topic involves the comprehension of variables as symbolic representations of data that can change, and constants as fixed values that do not change. Mastery of these concepts is essential for foundational programming and mathematical problem-solving.</td>
                    <td><span style="color: blue;">Basic arithmetic, Algebraic expressions, Mathematical notation, Introduction to programming concepts</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of whole numbers</strong></td>
                    <td>Understanding whole numbers involves recognizing and working with non-negative integers, including concepts of counting, ordering, and basic arithmetic operations such as addition and subtraction. This foundational knowledge is essential for further mathematical learning and problem-solving.</td>
                    <td><span style="color: blue;">Number recognition</span></td>
                    <td><span style="color: red;">Counting skills, Basic addition and subtraction, Understanding of the number line</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Visual perception skills</strong></td>
                    <td>Visual perception skills refer to the ability to interpret and make sense of visual information from the environment, encompassing processes such as recognition, discrimination, and spatial awareness. These skills are crucial for tasks such as reading, writing, and navigating spaces effectively.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">Basic anatomy of the eye, Understanding of the visual processing pathway, Introduction to cognitive psychology, Fundamentals of sensory perception, Developmental stages of visual perception</span></td>
                </tr>
                
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function toggleExpand() {
            const container = document.getElementById('graphContainer');
            const overlay = document.getElementById('overlay');
            const expandBtn = document.getElementById('expandBtn');

            if (container.classList.contains('expanded')) {
                container.classList.remove('expanded');
                overlay.classList.remove('active');
                expandBtn.innerHTML = '🔍 Expand';
                document.body.style.overflow = 'auto';

                // Resize network after collapse
                setTimeout(() => {
                    if (window.network && window.network.redraw) {
                        window.network.redraw();
                        window.network.fit();
                    }
                }, 300);
            } else {
                container.classList.add('expanded');
                overlay.classList.add('active');
                expandBtn.innerHTML = '❌ Close';
                document.body.style.overflow = 'hidden';

                // Resize network after expansion
                setTimeout(() => {
                    if (window.network && window.network.redraw) {
                        window.network.redraw();
                        window.network.fit();
                    }
                }, 300);
            }
        }

        function resetGraph() {
            // Try to reset the network view if vis.js methods are available
            if (window.network && window.network.fit) {
                window.network.fit();
            }
        }

        // Close expanded view with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const container = document.getElementById('graphContainer');
                if (container.classList.contains('expanded')) {
                    toggleExpand();
                }
            }
        });
    </script>

    <script src="lib/bindings/utils.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>
<script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>
<script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "black"}, "id": "Deep Learning", "label": "Deep Learning", "shape": "dot", "size": 45, "title": "Depth: 0\nDescription: Deep Learning is a subset of machine learning that utilizes neural networks with multiple layers to model complex patterns in large datasets. It has applications in various fields such as computer vision, natural language processing, and speech recognition."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Calculus", "label": "Calculus", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Calculus is a branch of mathematics that studies continuous change, focusing on concepts such as derivatives, integrals, limits, and infinite series. It provides essential tools for analyzing dynamic systems and is foundational for advanced studies in mathematics, physics, engineering, and economics."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Linear Algebra", "label": "Linear Algebra", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Linear Algebra is a branch of mathematics that deals with vector spaces, linear transformations, and systems of linear equations. It is foundational for various fields such as engineering, physics, computer science, and economics."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Machine Learning Fundamentals", "label": "Machine Learning Fundamentals", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Machine Learning Fundamentals covers the basic principles and techniques of machine learning, including supervised and unsupervised learning, model evaluation, and algorithm selection. This topic serves as a foundation for understanding how machines can learn from data and make predictions or decisions."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Probability and Statistics", "label": "Probability and Statistics", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Probability and Statistics is a branch of mathematics that deals with the analysis of random phenomena and the interpretation of data. It provides tools for making inferences about populations based on sample data and for quantifying uncertainty."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Programming in Python", "label": "Programming in Python", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Programming in Python involves writing code in the Python programming language to create software applications, automate tasks, and analyze data. It emphasizes readability and simplicity, making it an ideal language for beginners and experienced developers alike."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Algebra", "label": "Algebra", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Algebra is a branch of mathematics that deals with symbols and the rules for manipulating those symbols to solve equations and understand relationships between quantities. It serves as a foundational tool for higher-level mathematics and various applications in science and engineering."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic Algebra", "label": "Basic Algebra", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic Algebra involves the study of mathematical symbols and the rules for manipulating these symbols to solve equations and inequalities. It serves as a foundational component for higher-level mathematics and problem-solving skills."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic Calculus", "label": "Basic Calculus", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic Calculus is the study of the fundamental concepts of differentiation and integration, focusing on the rates of change and the accumulation of quantities. It serves as a foundation for understanding more advanced mathematical concepts and applications in various fields."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic understanding of computer science concepts", "label": "Basic understanding of computer science concepts", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: This topic encompasses the foundational principles of computer science, including algorithms, data structures, programming languages, and the theory of computation. It serves as an essential introduction for students to grasp how computers operate and how software is developed."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Data Structures", "label": "Data Structures", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Data Structures are specialized formats for organizing, processing, and storing data in a computer, enabling efficient access and modification. They form the backbone of computer algorithms and are essential for effective programming and software development."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Descriptive Statistics", "label": "Descriptive Statistics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Descriptive statistics involves the methods of summarizing and organizing data to provide a clear overview of its main features, typically through measures such as mean, median, mode, and standard deviation. It serves as a foundational tool for data analysis, allowing researchers to present quantitative descriptions in a manageable form."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Experience with using a text editor or integrated development environment (IDE)", "label": "Experience with using a text editor or integrated development environment (IDE)", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: This topic encompasses the skills and knowledge required to effectively utilize text editors and integrated development environments for software development, including features such as syntax highlighting, debugging, and version control integration."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Familiarity with algorithms and data structures", "label": "Familiarity with algorithms and data structures", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Familiarity with algorithms and data structures involves understanding the fundamental techniques for organizing and manipulating data efficiently, as well as the methods for solving computational problems. This knowledge is essential for software development, computer science, and data analysis."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Functions", "label": "Functions", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Functions are mathematical constructs that relate an input to a single output, defined by a specific rule or formula. They are foundational in various fields such as mathematics, computer science, and engineering, serving as a means to model relationships and perform calculations."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Functions and Graphs", "label": "Functions and Graphs", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Functions and Graphs is a fundamental topic in mathematics that explores the relationship between inputs and outputs through functions, and visually represents these relationships using graphs. Understanding this topic is essential for analyzing mathematical behavior and solving real-world problems."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Geometry", "label": "Geometry", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Geometry is a branch of mathematics that deals with the properties and relationships of points, lines, surfaces, and solids. It encompasses various concepts such as shapes, sizes, relative positions of figures, and the properties of space."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Knowledge of basic mathematics", "label": "Knowledge of basic mathematics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Knowledge of basic mathematics encompasses fundamental arithmetic operations, number sense, and the ability to solve simple mathematical problems. It serves as the foundation for more advanced mathematical concepts and applications in various fields."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Limits", "label": "Limits", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Limits are a fundamental concept in calculus that describe the behavior of a function as its input approaches a particular value. They are essential for understanding continuity, derivatives, and integrals."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Programming Basics", "label": "Programming Basics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Programming Basics introduces fundamental concepts of computer programming, including syntax, data types, control structures, and basic algorithms, enabling learners to write simple programs. This foundational knowledge is essential for further study in software development and computer science."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Set Theory", "label": "Set Theory", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Set Theory is a branch of mathematical logic that studies sets, which are collections of objects. It serves as a foundational system for various areas of mathematics, providing a framework for understanding relations, functions, and cardinality."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Statistics", "label": "Statistics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Statistics is the branch of mathematics that deals with the collection, analysis, interpretation, presentation, and organization of data. It provides tools for making inferences about populations based on sample data and is essential for decision-making in various fields."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Trigonometry", "label": "Trigonometry", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Trigonometry is a branch of mathematics that studies the relationships between the angles and sides of triangles, particularly right triangles. It is essential for understanding concepts in geometry, physics, engineering, and various applied sciences."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Understanding of fundamental programming concepts such as variables, control structures, and functions", "label": "Understanding of fundamental programming concepts such as variables, control structures, and functions", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: This topic encompasses the basic building blocks of programming, including the use of variables to store data, control structures to dictate the flow of execution, and functions to encapsulate reusable code. Mastery of these concepts is essential for developing effective algorithms and writing efficient code."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Algebraic expressions", "label": "Algebraic expressions", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Algebraic expressions are mathematical phrases that include numbers, variables, and operation symbols, representing a value or relationship. They serve as the foundation for more complex algebraic concepts and problem-solving techniques."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Algorithms", "label": "Algorithms", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Algorithms are step-by-step procedures or formulas for solving problems and performing tasks, often implemented in programming to automate processes and optimize performance. They are fundamental to computer science and are used in various applications, from data processing to artificial intelligence."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Arithmetic", "label": "Arithmetic", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Arithmetic is the branch of mathematics dealing with the properties and manipulation of numbers, including operations such as addition, subtraction, multiplication, and division. It serves as the foundation for more advanced mathematical concepts and applications."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Arithmetic operations", "label": "Arithmetic operations", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Arithmetic operations refer to the basic mathematical processes of addition, subtraction, multiplication, and division, which are foundational for performing calculations and solving numerical problems. Mastery of these operations is essential for advancing in mathematics and its applications in various fields."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic Geometry", "label": "Basic Geometry", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic Geometry is the branch of mathematics that deals with the properties and relationships of points, lines, angles, surfaces, and solids. It serves as a foundational subject that introduces students to spatial reasoning and the principles of measurement."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic Logic", "label": "Basic Logic", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic Logic is the study of the principles of valid reasoning and argumentation, focusing on the structure of statements and the relationships between them. It provides foundational tools for evaluating arguments and understanding logical relationships in various fields."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic Mathematics", "label": "Basic Mathematics", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic Mathematics encompasses fundamental concepts such as arithmetic operations, number theory, and introductory algebra, forming the foundation for more advanced mathematical studies. It is essential for everyday problem-solving and critical thinking skills."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic Programming Concepts", "label": "Basic Programming Concepts", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic Programming Concepts encompass fundamental ideas and principles that form the foundation of programming, including variables, data types, control structures, and functions. Understanding these concepts is essential for writing and comprehending code in any programming language."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic Trigonometry", "label": "Basic Trigonometry", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic Trigonometry is the study of the relationships between the angles and sides of triangles, particularly right triangles. It introduces fundamental concepts such as sine, cosine, and tangent, which are essential for solving various geometrical and real-world problems."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic arithmetic", "label": "Basic arithmetic", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic arithmetic is the branch of mathematics that deals with the fundamental operations of addition, subtraction, multiplication, and division. It serves as the foundation for more advanced mathematical concepts and everyday calculations."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic arithmetic operations", "label": "Basic arithmetic operations", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic arithmetic operations refer to the fundamental mathematical processes of addition, subtraction, multiplication, and division, which are essential for performing calculations and solving numerical problems. Mastery of these operations is crucial for further study in mathematics and related fields."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "label": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic arithmetic operations are the foundational mathematical processes used to manipulate numbers, including addition (combining values), subtraction (finding the difference), multiplication (repeated addition), and division (distributing a value into equal parts). Mastery of these operations is essential for further mathematical learning and everyday problem-solving."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic knowledge of recursion", "label": "Basic knowledge of recursion", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Recursion is a programming technique where a function calls itself to solve a problem by breaking it down into smaller, more manageable subproblems. Understanding recursion is essential for solving problems that can be defined in terms of smaller instances of the same problem."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic mathematical concepts", "label": "Basic mathematical concepts", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic mathematical concepts encompass fundamental ideas such as numbers, operations, and relationships that form the foundation for more advanced mathematics. These concepts include arithmetic operations, number properties, and basic geometric principles essential for everyday problem-solving."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic mathematics", "label": "Basic mathematics", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic mathematics encompasses foundational concepts such as arithmetic operations, number theory, and basic geometry, which are essential for understanding more advanced mathematical topics. It serves as the building block for problem-solving and analytical skills in various fields."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic problem-solving skills", "label": "Basic problem-solving skills", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic problem-solving skills involve the ability to identify, analyze, and develop solutions for various challenges or obstacles. These skills are essential for effective decision-making and critical thinking in both academic and real-world contexts."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic programming concepts", "label": "Basic programming concepts", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic programming concepts encompass fundamental principles and constructs used in programming languages, including variables, data types, control structures, and functions. Mastery of these concepts is essential for developing problem-solving skills and writing effective code."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic programming skills", "label": "Basic programming skills", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic programming skills encompass the foundational knowledge and abilities required to write, understand, and debug simple code in a programming language. This includes concepts such as variables, control structures, data types, and basic algorithms."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic properties of numbers", "label": "Basic properties of numbers", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: The basic properties of numbers refer to fundamental characteristics that govern how numbers interact with one another, including concepts such as commutativity, associativity, distributivity, identity elements, and inverses. Understanding these properties is essential for performing arithmetic operations and solving mathematical problems effectively."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic understanding of programming concepts", "label": "Basic understanding of programming concepts", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: This topic encompasses the foundational ideas and principles that underpin programming, including variables, control structures, data types, and algorithms. A basic understanding of these concepts is essential for anyone looking to learn how to write and understand computer programs."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Complexity Analysis", "label": "Complexity Analysis", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Complexity Analysis is the study of the resources required for the execution of algorithms, primarily focusing on time and space complexity. It provides a framework for evaluating the efficiency of algorithms and understanding their scalability with respect to input size."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Concept of place value", "label": "Concept of place value", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: The concept of place value is a foundational principle in mathematics that assigns a numerical value to a digit based on its position within a number. It is essential for understanding how numbers are structured and how they can be manipulated in arithmetic operations."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Coordinate Systems", "label": "Coordinate Systems", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Coordinate systems are mathematical frameworks used to uniquely determine the position of points in space through ordered pairs or triplets of numbers. They are essential in various fields such as geometry, physics, and computer graphics for representing spatial relationships and transformations."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Coordinate geometry", "label": "Coordinate geometry", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Coordinate geometry, also known as analytic geometry, is the study of geometric figures using a coordinate system, allowing for the representation and analysis of shapes and their properties through algebraic equations. It combines algebra and geometry to solve problems involving points, lines, and curves in a two-dimensional or three-dimensional space."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Data Analysis", "label": "Data Analysis", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Data analysis is the process of systematically applying statistical and logical techniques to describe, summarize, and compare data. It involves extracting meaningful insights from raw data to support decision-making and problem-solving."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Data Collection Techniques", "label": "Data Collection Techniques", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Data Collection Techniques encompass various methods used to gather information for research purposes, enabling researchers to obtain quantitative and qualitative data. Understanding these techniques is essential for designing effective studies and ensuring the reliability and validity of the collected data."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Elementary Algebra", "label": "Elementary Algebra", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Elementary Algebra is a branch of mathematics that deals with the basic concepts of algebra, including the manipulation of variables, the solving of equations, and the understanding of functions and their properties. It serves as a foundational course for higher-level mathematics and is essential for various applications in science, engineering, and everyday problem-solving."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Familiarity with a programming environment", "label": "Familiarity with a programming environment", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Familiarity with a programming environment refers to the understanding and ability to effectively use tools, interfaces, and features provided by software platforms for coding, debugging, and executing programs. This knowledge is essential for developers to enhance productivity and streamline the software development process."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Familiarity with basic data types", "label": "Familiarity with basic data types", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Familiarity with basic data types involves understanding the fundamental categories of data used in programming and data analysis, such as integers, floats, strings, and booleans. This knowledge is essential for effective data manipulation and coding practices."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Familiarity with file systems and directory structures", "label": "Familiarity with file systems and directory structures", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: This topic encompasses understanding how data is organized, stored, and accessed on storage devices through various file systems and their hierarchical directory structures. It is essential for effective data management and navigation within operating systems."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Familiarity with logical operators", "label": "Familiarity with logical operators", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Familiarity with logical operators involves understanding the basic logical constructs used in programming and mathematics, such as AND, OR, and NOT, which are essential for constructing logical expressions and making decisions in algorithms. Mastery of these operators is crucial for effective problem-solving and programming logic."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Functions and Relations", "label": "Functions and Relations", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Functions and Relations are fundamental concepts in mathematics that describe the relationship between sets of inputs and outputs, where a function assigns exactly one output for each input, while a relation may associate multiple outputs with a single input. Understanding these concepts is essential for exploring more advanced topics in algebra, calculus, and discrete mathematics."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Graphing", "label": "Graphing", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Graphing is the visual representation of data or mathematical functions on a coordinate system, allowing for the analysis of relationships and trends. It is a fundamental skill in mathematics and science that aids in interpreting quantitative information."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Inequalities", "label": "Inequalities", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Inequalities are mathematical expressions that describe the relative size or order of two values, using symbols such as \u003c, \u003e, \u2264, and \u2265. They are fundamental in various fields of mathematics, including algebra and calculus, as they help in understanding the relationships between quantities."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Introduction to Probability", "label": "Introduction to Probability", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Introduction to Probability covers the fundamental concepts of probability theory, including the calculation of probabilities, the understanding of random variables, and the principles of combinatorics. This topic serves as a foundation for more advanced studies in statistics and data analysis."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Introduction to computer science", "label": "Introduction to computer science", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Introduction to computer science is a foundational course that covers the fundamental principles of computing, including algorithms, data structures, programming languages, and the basics of software development. It aims to equip students with the skills necessary to understand and solve computational problems."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Introduction to mathematical proofs", "label": "Introduction to mathematical proofs", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: This topic introduces students to the fundamental concepts and techniques used in constructing and understanding mathematical proofs, emphasizing logical reasoning and the structure of mathematical arguments."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Introduction to software development methodologies", "label": "Introduction to software development methodologies", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: This topic provides an overview of various software development methodologies, including Agile, Waterfall, and DevOps, focusing on their principles, processes, and best practices. It aims to equip learners with the foundational knowledge necessary to select and implement appropriate methodologies in software projects."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Knowledge of programming languages syntax", "label": "Knowledge of programming languages syntax", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Knowledge of programming languages syntax refers to the understanding of the rules and structure that govern how code is written in various programming languages. This includes familiarity with keywords, operators, and the organization of statements that enable effective communication with a computer."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Linear equations", "label": "Linear equations", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Linear equations are mathematical statements that express the equality of two linear expressions, typically in the form of y = mx + b, where m represents the slope and b the y-intercept. They are foundational in algebra and are used to model relationships between variables."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Logical reasoning", "label": "Logical reasoning", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Logical reasoning is the process of using structured, coherent thought to analyze arguments, draw conclusions, and solve problems based on given premises. It encompasses both deductive and inductive reasoning techniques to evaluate the validity of statements and arguments."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Logical reasoning skills", "label": "Logical reasoning skills", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Logical reasoning skills involve the ability to analyze information, draw conclusions, and make decisions based on logical principles. These skills are essential for problem-solving and critical thinking in various academic and real-world contexts."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Mathematical Notation", "label": "Mathematical Notation", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Mathematical notation is a system of symbols and signs used to represent numbers, operations, relationships, and functions in mathematics. It provides a concise and unambiguous way to communicate mathematical ideas and concepts."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Mathematical Reasoning", "label": "Mathematical Reasoning", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Mathematical reasoning involves the logical process of deriving conclusions from premises using mathematical concepts and principles. It encompasses various forms of reasoning, including deductive and inductive reasoning, to solve problems and prove theorems."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Mathematical foundations (e.g., discrete mathematics)", "label": "Mathematical foundations (e.g., discrete mathematics)", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Mathematical foundations encompass the essential concepts and principles that form the basis for various branches of mathematics, with discrete mathematics focusing on structures that are fundamentally discrete rather than continuous. This area includes topics such as logic, set theory, combinatorics, graph theory, and algorithms, which are crucial for understanding more advanced mathematical and computational theories."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Mathematical reasoning", "label": "Mathematical reasoning", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Mathematical reasoning is the process of using logical thinking to analyze mathematical concepts, formulate arguments, and solve problems. It encompasses various techniques, including deductive and inductive reasoning, to derive conclusions from premises or known facts."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Mathematics for Computer Science", "label": "Mathematics for Computer Science", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Mathematics for Computer Science encompasses the mathematical concepts and techniques that are essential for understanding algorithms, data structures, and computational theory. It includes topics such as discrete mathematics, logic, and combinatorics, which form the foundation for problem-solving in computer science."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Number Theory", "label": "Number Theory", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Number Theory is a branch of pure mathematics devoted to the study of the integers and their properties, including concepts such as divisibility, prime numbers, and congruences. It serves as a foundational area for various applications in cryptography, computer science, and algebra."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Order of operations", "label": "Order of operations", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: The order of operations is a mathematical rule that dictates the sequence in which different operations (such as addition, subtraction, multiplication, and division) should be performed to ensure consistent results in calculations. This is often remembered by the acronym PEMDAS, which stands for Parentheses, Exponents, Multiplication and Division (from left to right), Addition and Subtraction (from left to right)."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Probability Theory", "label": "Probability Theory", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Probability Theory is a branch of mathematics that deals with the analysis of random phenomena and the quantification of uncertainty. It provides a framework for modeling and reasoning about events and outcomes in various fields such as statistics, finance, and science."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Problem-solving skills", "label": "Problem-solving skills", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Problem-solving skills refer to the ability to identify, analyze, and resolve issues effectively and efficiently. These skills encompass critical thinking, creativity, and decision-making processes essential for navigating complex situations."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Understanding of Angles", "label": "Understanding of Angles", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Understanding of angles involves recognizing and measuring the space between two intersecting lines or surfaces, typically expressed in degrees. This topic is foundational in geometry and is essential for further studies in trigonometry, physics, and engineering."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Understanding of Variables", "label": "Understanding of Variables", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Understanding of variables is the foundational concept in mathematics and programming that involves recognizing and manipulating symbols that represent quantities or values. It is essential for solving equations, performing calculations, and writing algorithms."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Understanding of binary and number systems", "label": "Understanding of binary and number systems", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: This topic involves the study of various numeral systems, particularly the binary system, which is foundational for computer science and digital electronics. It encompasses the conversion between different bases and the representation of data in binary form."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Understanding of complexity analysis", "label": "Understanding of complexity analysis", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Complexity analysis is the study of the resources required for the execution of algorithms, primarily focusing on time and space complexity. It helps in evaluating the efficiency of algorithms and understanding their scalability in relation to input size."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Understanding of fractions and decimals", "label": "Understanding of fractions and decimals", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: This topic involves recognizing, comparing, and performing operations with fractions and decimals, which are essential components of number sense and mathematical literacy. Mastery of these concepts enables students to solve real-world problems involving parts of a whole and numerical representation."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Understanding of numbers and number systems", "label": "Understanding of numbers and number systems", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: This topic encompasses the study of numerical values, their representations, and the various systems used to categorize and manipulate these numbers, including natural numbers, integers, rational numbers, and real numbers. It lays the foundation for mathematical reasoning and problem-solving across various disciplines."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Understanding of spatial reasoning", "label": "Understanding of spatial reasoning", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Spatial reasoning refers to the ability to visualize and manipulate objects in a three-dimensional space. It is a critical skill in fields such as mathematics, engineering, and architecture, facilitating problem-solving and critical thinking."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Understanding of variables", "label": "Understanding of variables", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Understanding of variables involves recognizing their role as symbolic representations of data that can change or vary within mathematical expressions and programming contexts. This foundational concept is crucial for problem-solving and logical reasoning in mathematics and computer science."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Variables", "label": "Variables", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Variables are symbolic representations used to store data values in programming and mathematics, allowing for dynamic manipulation and computation. They serve as placeholders for information that can change during the execution of a program or within mathematical expressions."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Ability to interpret data sets", "label": "Ability to interpret data sets", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The ability to interpret data sets involves analyzing and making sense of numerical and categorical data to draw meaningful conclusions, identify trends, and inform decision-making. This skill is essential in various fields, including statistics, data science, and research."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Algebraic Expressions", "label": "Algebraic Expressions", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Algebraic expressions are mathematical phrases that include numbers, variables, and operations. They serve as the foundational building blocks for algebra, allowing for the representation of relationships and the formulation of equations."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Algebraic manipulation", "label": "Algebraic manipulation", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Algebraic manipulation involves the process of rearranging and simplifying algebraic expressions using various mathematical operations and properties. It is a fundamental skill in algebra that enables the solving of equations and the understanding of mathematical relationships."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Algorithm Design", "label": "Algorithm Design", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Algorithm Design is the process of defining a step-by-step procedure or formula for solving a problem or performing a task, focusing on the efficiency and effectiveness of the solution. It encompasses various strategies and techniques to create algorithms that optimize performance and resource utilization."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Analytical reasoning", "label": "Analytical reasoning", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Analytical reasoning involves the ability to evaluate information, identify patterns, and draw logical conclusions based on given data. It is a critical skill used in problem-solving and decision-making across various academic and professional fields."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic Arithmetic", "label": "Basic Arithmetic", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic Arithmetic encompasses the fundamental operations of mathematics, including addition, subtraction, multiplication, and division, which are essential for performing calculations and solving numerical problems. Mastery of these operations forms the foundation for more advanced mathematical concepts."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic Number Theory", "label": "Basic Number Theory", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic Number Theory is the branch of mathematics that deals with the properties and relationships of integers, including concepts such as divisibility, prime numbers, and modular arithmetic. It serves as a foundational area of study that underpins many advanced topics in mathematics and computer science."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic Programming", "label": "Basic Programming", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic programming involves the foundational concepts and techniques used to write simple computer programs, including understanding syntax, data types, control structures, and algorithms. It serves as an introduction to the principles of coding and problem-solving using programming languages."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic Statistics", "label": "Basic Statistics", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic Statistics involves the collection, analysis, interpretation, presentation, and organization of data. It provides foundational tools for understanding data distributions, measures of central tendency, and variability."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic algebra", "label": "Basic algebra", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic algebra involves the study of mathematical symbols and the rules for manipulating these symbols to solve equations and understand relationships between quantities. It serves as a foundational component for more advanced mathematical concepts and applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic algebraic concepts", "label": "Basic algebraic concepts", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic algebraic concepts involve the foundational principles of algebra, including the understanding of variables, constants, expressions, and equations. Mastery of these concepts is essential for solving mathematical problems and progressing to more advanced algebraic topics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic comprehension of truth tables", "label": "Basic comprehension of truth tables", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Truth tables are a mathematical table used to determine the truth values of logical expressions based on their variables. Understanding truth tables is essential for analyzing logical operations and constructing logical arguments in fields such as mathematics, computer science, and philosophy."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic computer literacy", "label": "Basic computer literacy", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic computer literacy refers to the foundational skills required to effectively use computers and related technology, including understanding hardware, software, and basic troubleshooting. It encompasses the ability to navigate operating systems, utilize applications, and engage with the internet safely and efficiently."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic counting", "label": "Basic counting", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic counting is the foundational skill of enumerating objects or quantities, typically involving the recognition and use of numbers to quantify items in a set. It serves as the basis for more advanced mathematical concepts such as addition, subtraction, and number theory."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic counting skills", "label": "Basic counting skills", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic counting skills involve the ability to recognize numbers and perform simple counting tasks, which are foundational for more advanced mathematical concepts. These skills are essential for everyday activities and serve as the building blocks for arithmetic operations."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic geometric shapes", "label": "Basic geometric shapes", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic geometric shapes are fundamental figures in geometry, including circles, triangles, squares, and rectangles, that serve as the building blocks for more complex geometric concepts. Understanding these shapes involves recognizing their properties, such as sides, angles, and symmetry."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic geometry concepts", "label": "Basic geometry concepts", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic geometry concepts encompass fundamental ideas related to shapes, sizes, and the properties of space, including points, lines, angles, and surfaces. These concepts serve as the foundation for more advanced studies in geometry and related fields."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic knowledge of computer operation", "label": "Basic knowledge of computer operation", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic knowledge of computer operation involves understanding the fundamental functions and components of a computer system, including hardware, software, and user interfaces. This knowledge enables individuals to effectively interact with computers for various tasks such as file management, internet browsing, and application usage."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic knowledge of computer operations", "label": "Basic knowledge of computer operations", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic knowledge of computer operations encompasses the fundamental skills and understanding required to effectively use a computer, including navigating the operating system, managing files, and utilizing essential software applications. This foundational knowledge is crucial for engaging with more advanced computing concepts and technologies."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic number line usage", "label": "Basic number line usage", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic number line usage involves understanding how to represent numbers on a linear scale, allowing for the visualization of numerical relationships, such as greater than, less than, and the concept of zero. It serves as a foundational tool for arithmetic operations and helps in grasping more complex mathematical concepts."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic number recognition", "label": "Basic number recognition", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic number recognition involves the ability to identify and understand numbers, including their symbols and quantities, which is foundational for early mathematical skills. This skill is crucial for developing further numeracy and arithmetic abilities in children."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic number sense", "label": "Basic number sense", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic number sense refers to the foundational understanding of numbers and their relationships, including the ability to recognize, compare, and manipulate numbers in various contexts. It is essential for developing mathematical reasoning and problem-solving skills."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic principles of computer science", "label": "Basic principles of computer science", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic principles of computer science encompass foundational concepts such as algorithms, data structures, and programming logic that form the basis for understanding how computers operate and solve problems. This topic serves as an introduction to computational thinking and the systematic approach to problem-solving using technology."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic properties of lines and angles", "label": "Basic properties of lines and angles", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic covers the fundamental characteristics and relationships of lines and angles, including concepts such as parallel lines, intersecting lines, complementary and supplementary angles, and the properties of angles formed by transversals. Understanding these properties is essential for solving geometric problems and proofs."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic troubleshooting and debugging skills", "label": "Basic troubleshooting and debugging skills", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic troubleshooting and debugging skills involve the ability to identify, diagnose, and resolve issues in software or hardware systems. These skills are essential for maintaining system functionality and improving user experience."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic understanding of Boolean algebra", "label": "Basic understanding of Boolean algebra", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Boolean algebra is a branch of mathematics that deals with variables that have two possible values, typically represented as true and false. It is fundamental in computer science, digital logic design, and set theory, providing the basis for logical reasoning and operations."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic understanding of algorithms", "label": "Basic understanding of algorithms", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: A basic understanding of algorithms involves grasping the fundamental principles of problem-solving through step-by-step procedures, including how to analyze their efficiency and effectiveness. This foundational knowledge is essential for further study in computer science and programming."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic understanding of computer science principles", "label": "Basic understanding of computer science principles", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic encompasses the foundational concepts and theories that underpin computer science, including algorithms, data structures, and the principles of programming. It provides learners with the essential knowledge needed to engage in further study or practical application of computer science."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic understanding of computer systems", "label": "Basic understanding of computer systems", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic encompasses the foundational concepts of how computer systems operate, including hardware components, software interactions, and basic data processing. It serves as an essential introduction for anyone looking to delve into more advanced computing subjects."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Big O Notation", "label": "Big O Notation", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Big O Notation is a mathematical concept used to describe the upper bound of an algorithm\u0027s running time or space requirements in relation to the size of the input data. It provides a high-level understanding of the algorithm\u0027s efficiency and scalability."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Combinatorics", "label": "Combinatorics", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Combinatorics is a branch of mathematics focused on counting, arrangement, and combination of objects. It plays a crucial role in various fields such as computer science, probability, and optimization."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Communication skills", "label": "Communication skills", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Communication skills refer to the ability to convey information effectively and efficiently, encompassing verbal, non-verbal, and written forms of communication. Mastery of these skills is essential for personal and professional success, as they facilitate understanding and collaboration among individuals."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Complexity theory", "label": "Complexity theory", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Complexity theory is a branch of theoretical computer science that studies the resources required for solving computational problems, particularly focusing on the classification of problems based on their inherent difficulty and the efficiency of algorithms that solve them."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Concept of equality", "label": "Concept of equality", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The concept of equality refers to the principle that all individuals should have the same rights, opportunities, and treatment under the law, irrespective of their background or characteristics. It encompasses various dimensions, including social, political, and economic equality, and is fundamental to democratic societies."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Concept of equality and inequalities", "label": "Concept of equality and inequalities", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The concept of equality and inequalities involves understanding the relationships between quantities, where equality denotes that two expressions are equivalent, while inequalities express the relative size or order of values. This topic is fundamental in various fields, including mathematics, economics, and social sciences, as it helps in analyzing and comparing different scenarios."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Concept of quantity", "label": "Concept of quantity", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The concept of quantity refers to the understanding and measurement of amounts, sizes, or magnitudes of objects or phenomena, which is foundational in mathematics and the sciences. It encompasses the ability to compare, classify, and manipulate numerical values and their representations."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Concept of ratios", "label": "Concept of ratios", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The concept of ratios involves comparing two or more quantities to express their relative sizes or proportions. Ratios are fundamental in mathematics and are used in various fields such as finance, science, and statistics to analyze relationships between different entities."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Control Structures", "label": "Control Structures", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Control structures are fundamental programming constructs that dictate the flow of execution in a program, allowing for decision-making, iteration, and branching based on specified conditions. They are essential for creating dynamic and responsive algorithms in software development."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Control structures (if statements, loops)", "label": "Control structures (if statements, loops)", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Control structures are essential programming constructs that enable the execution of specific code blocks based on certain conditions (if statements) or the repetition of code blocks (loops). They are fundamental for implementing logic and flow in programming languages."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Coordinate System", "label": "Coordinate System", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: A coordinate system is a mathematical framework that uses numbers to uniquely determine the position of points or objects in space, typically defined by a set of axes and a reference point. It is essential for graphing functions, solving geometric problems, and understanding spatial relationships in mathematics and physics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Counting", "label": "Counting", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Counting is the process of determining the quantity of items in a set, typically by assigning a unique number to each item in a sequential manner. It serves as a foundational concept in mathematics, enabling further exploration of arithmetic and number theory."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Creativity", "label": "Creativity", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Creativity is the ability to generate novel and valuable ideas or solutions, often involving the integration of diverse concepts and perspectives. It encompasses various cognitive processes and is essential in fields such as art, science, and problem-solving."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Critical thinking", "label": "Critical thinking", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Critical thinking is the ability to analyze, evaluate, and synthesize information in order to make reasoned judgments and decisions. It involves questioning assumptions, identifying biases, and applying logical reasoning to solve problems."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Critical thinking skills", "label": "Critical thinking skills", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Critical thinking skills involve the ability to analyze information, evaluate evidence, and construct reasoned arguments to make informed decisions. These skills are essential for problem-solving and effective communication in both academic and real-world contexts."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Data Management", "label": "Data Management", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Data Management encompasses the practices, processes, and technologies used to collect, store, organize, and maintain data throughout its lifecycle, ensuring its accuracy, accessibility, and security. It is essential for effective decision-making and operational efficiency in various fields."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Data Types", "label": "Data Types", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Data types are classifications that specify which type of value a variable can hold, determining the operations that can be performed on that data. Understanding data types is essential for effective programming and data manipulation in computer science."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Data Visualization", "label": "Data Visualization", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Data Visualization is the graphical representation of information and data, utilizing visual elements like charts, graphs, and maps to communicate complex data insights clearly and effectively. It enables users to identify patterns, trends, and outliers in data through visual context."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Data structures", "label": "Data structures", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Data structures are specialized formats for organizing, managing, and storing data in a computer so that it can be accessed and modified efficiently. They are fundamental to computer science and programming, enabling the development of efficient algorithms and applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Data structures (arrays, lists)", "label": "Data structures (arrays, lists)", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Data structures such as arrays and lists are fundamental components in computer science that allow for the organization and storage of data in a systematic way, enabling efficient access and manipulation. Understanding these structures is crucial for developing algorithms and solving computational problems."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Discrete mathematics", "label": "Discrete mathematics", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Discrete mathematics is the study of mathematical structures that are fundamentally discrete rather than continuous. It includes topics such as logic, set theory, combinatorics, graph theory, and algorithms, which are essential for computer science and related fields."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Domain and range", "label": "Domain and range", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Domain and range are fundamental concepts in mathematics that describe the set of possible input values (domain) and the set of possible output values (range) of a function. Understanding these concepts is essential for analyzing the behavior of functions and their graphical representations."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Ethics in Research", "label": "Ethics in Research", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Ethics in Research refers to the moral principles and guidelines that govern the conduct of research, ensuring integrity, accountability, and respect for participants. It encompasses issues such as informed consent, confidentiality, and the responsible use of data."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with addition and subtraction", "label": "Familiarity with addition and subtraction", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with addition and subtraction refers to the understanding and ability to perform basic arithmetic operations involving the combination and separation of quantities. This foundational skill is essential for further mathematical learning and everyday problem-solving."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with argument structures", "label": "Familiarity with argument structures", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with argument structures involves understanding the components and organization of arguments, including premises, conclusions, and the relationships between them. This knowledge is essential for critical thinking, effective communication, and engaging in logical discourse."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with basic algebra", "label": "Familiarity with basic algebra", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with basic algebra involves understanding fundamental concepts such as variables, constants, and mathematical operations, which are essential for solving equations and manipulating algebraic expressions. It serves as a foundational skill necessary for more advanced mathematical topics and real-world problem-solving."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with command line interfaces", "label": "Familiarity with command line interfaces", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with command line interfaces (CLIs) involves understanding how to interact with a computer\u0027s operating system through text-based commands, enabling users to perform tasks efficiently without a graphical user interface. This skill is essential for software development, system administration, and automation tasks."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with counting", "label": "Familiarity with counting", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with counting refers to the understanding and ability to enumerate objects, recognize numerical sequences, and apply basic counting principles in various contexts. This foundational skill is essential for developing more advanced mathematical concepts and problem-solving abilities."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with data storage concepts", "label": "Familiarity with data storage concepts", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with data storage concepts involves understanding the various methods and technologies used to store, retrieve, and manage data in digital systems. This knowledge is essential for effective data management and utilization in computing environments."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with decimal number system", "label": "Familiarity with decimal number system", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The decimal number system, also known as base-10, is the standard system for denoting integer and non-integer numbers. It is based on ten symbols (0-9) and is widely used in everyday counting and arithmetic operations."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with fractions and decimals", "label": "Familiarity with fractions and decimals", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with fractions and decimals involves understanding the representation and manipulation of numbers that are not whole, including their equivalences and operations. This foundational knowledge is essential for more advanced mathematical concepts and real-world applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with logic and boolean expressions", "label": "Familiarity with logic and boolean expressions", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with logic and boolean expressions involves understanding the principles of logical reasoning and the use of boolean algebra to manipulate true/false values. This knowledge is essential for fields such as computer science, mathematics, and philosophy, where logical operations are fundamental."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with logical connectives", "label": "Familiarity with logical connectives", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with logical connectives involves understanding the basic operators used in formal logic, such as conjunction, disjunction, negation, implication, and equivalence, which are essential for constructing and interpreting logical statements. Mastery of these connectives is crucial for engaging in logical reasoning and problem-solving in mathematics, computer science, and philosophy."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with mathematical concepts", "label": "Familiarity with mathematical concepts", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with mathematical concepts refers to the understanding and recognition of fundamental mathematical ideas, principles, and operations that form the foundation for more advanced mathematical reasoning and problem-solving. This familiarity is essential for engaging with various fields of study that rely on mathematical applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with multiplication and division", "label": "Familiarity with multiplication and division", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with multiplication and division involves understanding the concepts of repeated addition and equal grouping, which are foundational operations in arithmetic. Mastery of these operations is essential for solving more complex mathematical problems and real-world applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with problem-solving techniques", "label": "Familiarity with problem-solving techniques", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with problem-solving techniques involves understanding various methodologies and strategies used to identify, analyze, and resolve issues effectively. This knowledge enables individuals to approach challenges systematically and enhances critical thinking skills."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with programming concepts", "label": "Familiarity with programming concepts", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with programming concepts refers to the understanding of fundamental principles and terminology used in programming, such as variables, control structures, data types, and algorithms. This foundational knowledge is essential for anyone looking to learn programming languages and develop software applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with project management principles", "label": "Familiarity with project management principles", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with project management principles involves understanding the foundational concepts and methodologies that guide the planning, execution, and completion of projects. This knowledge is essential for effectively managing resources, timelines, and stakeholder expectations in various organizational contexts."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with set theory", "label": "Familiarity with set theory", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with set theory involves understanding the fundamental concepts of sets, including operations such as union, intersection, and difference, as well as the properties and relations of sets. It serves as a foundational aspect of mathematics and logic, essential for advanced studies in various fields such as mathematics, computer science, and statistics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with shapes and their properties", "label": "Familiarity with shapes and their properties", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic involves understanding various geometric shapes, including their characteristics, classifications, and the relationships between them. It is foundational for further studies in geometry and spatial reasoning."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with syllogisms", "label": "Familiarity with syllogisms", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with syllogisms involves understanding the structure of deductive reasoning where a conclusion is drawn from two premises. It is a foundational aspect of logic that helps in evaluating arguments and reasoning processes."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Familiarity with truth tables", "label": "Familiarity with truth tables", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Familiarity with truth tables involves understanding the systematic representation of logical expressions and their corresponding truth values. This foundational concept is essential in fields such as mathematics, computer science, and philosophy for analyzing logical propositions and circuits."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Foundational knowledge of data structures", "label": "Foundational knowledge of data structures", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Foundational knowledge of data structures involves understanding the basic ways of organizing and storing data in a computer, which is essential for efficient data manipulation and retrieval. This knowledge serves as a critical building block for more advanced topics in computer science and software development."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Functions and procedures", "label": "Functions and procedures", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Functions and procedures are fundamental programming constructs that allow for the encapsulation of code into reusable blocks, enabling modular programming and improving code organization. They facilitate the execution of specific tasks and can take inputs (parameters) and return outputs (results)."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Functions and relations", "label": "Functions and relations", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Functions and relations are fundamental concepts in mathematics that describe the relationship between sets of inputs and outputs. A function assigns exactly one output to each input, while a relation can associate multiple outputs with a single input."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Graphing concepts", "label": "Graphing concepts", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Graphing concepts involve the representation of mathematical relationships and data visually using graphs, which can include lines, bars, and other shapes. Understanding these concepts is essential for interpreting data and solving mathematical problems effectively."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Graphing on a number line", "label": "Graphing on a number line", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Graphing on a number line involves representing numbers as points on a straight line, where the position of each point corresponds to its value. This visual representation helps in understanding numerical relationships and performing operations such as addition and subtraction."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to algorithms", "label": "Introduction to algorithms", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Introduction to algorithms is a foundational course that covers the design, analysis, and implementation of algorithms, emphasizing their efficiency and effectiveness in solving computational problems. It serves as a critical building block for further studies in computer science and software engineering."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to argumentation", "label": "Introduction to argumentation", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Introduction to argumentation is a foundational course that explores the principles and techniques of constructing, analyzing, and evaluating arguments. It emphasizes critical thinking skills and the role of reasoning in persuasive communication."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to division", "label": "Introduction to division", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Introduction to division is a foundational mathematical concept that involves understanding how to split a quantity into equal parts or groups. It serves as a critical building block for more advanced arithmetic operations and problem-solving skills."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to equations and inequalities", "label": "Introduction to equations and inequalities", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic introduces the fundamental concepts of equations and inequalities, including their definitions, properties, and methods for solving them. It serves as a foundation for understanding more complex algebraic concepts and real-world applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to functions and relations", "label": "Introduction to functions and relations", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic introduces the concepts of functions and relations, focusing on their definitions, properties, and graphical representations. It provides foundational knowledge essential for understanding more complex mathematical concepts and their applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to logical reasoning", "label": "Introduction to logical reasoning", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Introduction to logical reasoning is a foundational course that explores the principles of valid reasoning, argument structure, and critical thinking skills necessary for evaluating arguments and making sound conclusions. It serves as a basis for understanding more complex logical concepts and applications in various fields."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to mathematical logic", "label": "Introduction to mathematical logic", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Introduction to mathematical logic is a foundational course that explores the principles of formal reasoning, including the structure of arguments, the nature of mathematical proofs, and the formulation of logical statements. It serves as a gateway to understanding more advanced topics in mathematics and computer science."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to mathematical notation", "label": "Introduction to mathematical notation", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic covers the basic symbols and conventions used in mathematics to represent numbers, operations, and relationships. Understanding mathematical notation is essential for interpreting and communicating mathematical ideas effectively."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to mathematical reasoning", "label": "Introduction to mathematical reasoning", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic covers the foundational principles of logical thinking and argumentation in mathematics, emphasizing the development of proofs and the understanding of mathematical concepts through rigorous reasoning. It serves as an essential framework for students to approach mathematical problems systematically and critically."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to mathematical symbols and notation", "label": "Introduction to mathematical symbols and notation", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic covers the basic symbols and notation used in mathematics, including operators, variables, and functions, which are essential for communicating mathematical ideas clearly and effectively. Understanding these symbols is foundational for further study in mathematics and related fields."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to programming concepts", "label": "Introduction to programming concepts", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic covers the foundational principles of programming, including basic syntax, data types, control structures, and algorithms, aimed at beginners to develop problem-solving skills through coding. It serves as a gateway to more advanced programming and software development topics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Introduction to variables and expressions", "label": "Introduction to variables and expressions", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic introduces the concept of variables as symbols that represent numbers or values in mathematical expressions, and explores how these expressions can be manipulated and evaluated. It lays the foundation for understanding algebraic concepts and problem-solving techniques."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Knowledge of conditional statements", "label": "Knowledge of conditional statements", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Knowledge of conditional statements involves understanding how to use \u0027if\u0027, \u0027else if\u0027, and \u0027else\u0027 constructs to control the flow of execution in programming and logic. This foundational concept is essential for making decisions based on specific conditions in algorithms and code."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Knowledge of exponents", "label": "Knowledge of exponents", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Knowledge of exponents involves understanding the mathematical notation and rules for expressing numbers raised to a power, which indicates repeated multiplication. This concept is foundational for various mathematical operations and applications in algebra, science, and engineering."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Knowledge of functions and their properties", "label": "Knowledge of functions and their properties", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic encompasses the understanding of mathematical functions, including their definitions, types, and characteristics such as domain, range, continuity, and behavior under transformations. Mastery of functions is essential for advanced studies in calculus, algebra, and various applications in science and engineering."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Knowledge of operating systems", "label": "Knowledge of operating systems", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Knowledge of operating systems encompasses understanding the software that manages computer hardware and software resources, as well as providing common services for computer programs. This knowledge is essential for grasping how applications interact with the hardware and how system resources are allocated and managed."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Knowledge of spatial reasoning", "label": "Knowledge of spatial reasoning", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Knowledge of spatial reasoning refers to the ability to visualize and manipulate objects in a three-dimensional space, which is essential for problem-solving in fields such as mathematics, engineering, and architecture. This cognitive skill involves understanding spatial relationships and dimensions, enabling individuals to navigate and interact with their environment effectively."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Knowledge of the Cartesian coordinate system", "label": "Knowledge of the Cartesian coordinate system", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The Cartesian coordinate system is a mathematical framework that uses two perpendicular axes (x and y) to define the position of points in a two-dimensional space. It serves as a foundational concept in geometry, algebra, and various applications in science and engineering."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Knowledge of the decimal system", "label": "Knowledge of the decimal system", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The decimal system is a base-10 numeral system that uses ten digits (0-9) to represent numbers, facilitating arithmetic operations and numerical representation. Understanding this system is essential for performing calculations and interpreting numerical data in various fields."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Knowledge of version control systems", "label": "Knowledge of version control systems", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Knowledge of version control systems involves understanding the tools and practices used to manage changes to source code over time, facilitating collaboration among developers and maintaining a history of project modifications. This knowledge is essential for software development, enabling teams to track progress, revert changes, and manage multiple versions of code efficiently."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Line segments and rays", "label": "Line segments and rays", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Line segments and rays are fundamental concepts in geometry that represent parts of a line. A line segment has two endpoints, while a ray has one endpoint and extends infinitely in one direction."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Logic", "label": "Logic", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Logic is the study of reasoning, argumentation, and the principles of valid inference. It encompasses the analysis of propositions, the structure of arguments, and the evaluation of truth values."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Logic and Propositional Calculus", "label": "Logic and Propositional Calculus", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Logic and Propositional Calculus is a branch of mathematical logic that deals with the formalization of logical statements and their relationships through symbolic representation and rules of inference. It provides the foundational framework for understanding logical reasoning, validity, and the structure of arguments."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Logic and reasoning", "label": "Logic and reasoning", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Logic and reasoning encompass the systematic study of valid inference, argument structure, and the principles of correct reasoning. This topic is essential for developing critical thinking skills and understanding how to construct and evaluate arguments effectively."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Logical Reasoning", "label": "Logical Reasoning", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Logical reasoning is the process of using structured thinking to analyze arguments, identify fallacies, and derive conclusions based on premises. It encompasses both deductive and inductive reasoning techniques to evaluate the validity of statements and arguments."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Logical reasoning and problem-solving skills", "label": "Logical reasoning and problem-solving skills", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Logical reasoning and problem-solving skills involve the ability to analyze information, identify patterns, and apply logical principles to derive solutions to complex problems. These skills are essential for effective decision-making and critical thinking in various academic and real-world contexts."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Logical thinking", "label": "Logical thinking", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Logical thinking is the ability to reason systematically and draw valid conclusions based on premises and evidence. It involves the application of formal logic principles to solve problems and make decisions effectively."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Mathematical Foundations", "label": "Mathematical Foundations", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Mathematical Foundations encompasses the basic concepts and principles that underlie mathematics, including logic, set theory, and the structure of mathematical reasoning. It serves as the groundwork for more advanced mathematical studies and applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Mathematical Operations", "label": "Mathematical Operations", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Mathematical operations refer to the basic processes of arithmetic, including addition, subtraction, multiplication, and division, which are foundational for performing calculations and solving mathematical problems. These operations form the building blocks for more complex mathematical concepts and applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Mathematical Proof Techniques", "label": "Mathematical Proof Techniques", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Mathematical proof techniques are systematic methods used to establish the validity of mathematical statements through logical reasoning and argumentation. These techniques include direct proof, proof by contradiction, proof by induction, and others, serving as foundational tools in mathematics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Mathematical logic", "label": "Mathematical logic", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Mathematical logic is a subfield of mathematics that deals with formal systems, symbolic reasoning, and the principles of valid inference. It encompasses the study of propositional and predicate logic, as well as the foundations of mathematics and computability theory."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Mathematical notation", "label": "Mathematical notation", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Mathematical notation is a system of symbols and signs used to represent numbers, operations, and relationships in mathematics, enabling precise communication of mathematical ideas. It serves as a universal language that facilitates the expression of complex concepts in a concise manner."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Mathematics", "label": "Mathematics", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Mathematics is the abstract science of number, quantity, and space, either as abstract concepts (pure mathematics), or as applied to other disciplines such as physics and engineering (applied mathematics). It encompasses various fields including arithmetic, algebra, geometry, and calculus, providing tools for problem-solving and logical reasoning."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Measurement concepts", "label": "Measurement concepts", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Measurement concepts encompass the principles and methods used to quantify physical properties, enabling the comparison and analysis of various phenomena. This topic includes understanding units of measurement, precision, accuracy, and the significance of measurement in scientific inquiry."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Number recognition", "label": "Number recognition", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Number recognition is the ability to identify and understand numerical symbols and their corresponding values. This foundational skill is essential for early mathematics learning and is often developed in preschool and kindergarten settings."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Number sense", "label": "Number sense", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Number sense refers to the intuitive understanding of numbers, their relationships, and the ability to perform mental calculations and estimations. It encompasses skills such as recognizing quantities, understanding numerical patterns, and applying mathematical reasoning in various contexts."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Order of operations (PEMDAS/BODMAS)", "label": "Order of operations (PEMDAS/BODMAS)", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The order of operations is a mathematical rule that dictates the sequence in which operations should be performed to accurately evaluate expressions. It is commonly remembered using the acronyms PEMDAS (Parentheses, Exponents, Multiplication and Division, Addition and Subtraction) or BODMAS (Brackets, Orders, Division and Multiplication, Addition and Subtraction)."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Order of operations rules", "label": "Order of operations rules", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The order of operations rules are a set of guidelines used to determine the sequence in which mathematical operations should be performed to accurately evaluate expressions. This ensures consistency and correctness in calculations involving multiple operations such as addition, subtraction, multiplication, and division."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Ordered pairs", "label": "Ordered pairs", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: An ordered pair is a pair of elements where the order in which they are listed is significant, typically represented as (a, b). This concept is foundational in mathematics, particularly in coordinate systems and relations."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Place value system", "label": "Place value system", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The place value system is a numerical system that assigns a value to a digit based on its position within a number, allowing for the representation of large numbers and the execution of arithmetic operations. It is foundational for understanding how numbers are structured and manipulated in mathematics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Proof Techniques", "label": "Proof Techniques", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Proof techniques are methods used in mathematics and computer science to establish the validity of statements or theorems. Common techniques include direct proof, proof by contradiction, proof by induction, and proof by contrapositive."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Proof techniques", "label": "Proof techniques", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Proof techniques are systematic methods used to establish the validity of mathematical statements through logical reasoning. Common techniques include direct proof, proof by contradiction, proof by induction, and contrapositive proof."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Properties of parallel and intersecting lines", "label": "Properties of parallel and intersecting lines", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic explores the characteristics and relationships between parallel lines, which never meet, and intersecting lines, which cross at a point. Understanding these properties is essential for solving geometric problems and proving theorems related to angles and shapes."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Research Methodology", "label": "Research Methodology", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Research Methodology refers to the systematic approach and techniques used to conduct research, encompassing the principles, procedures, and strategies for collecting and analyzing data to answer specific research questions. It provides a framework for researchers to ensure the validity and reliability of their findings."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Sampling Methods", "label": "Sampling Methods", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Sampling methods are techniques used to select a subset of individuals or observations from a larger population to make inferences about that population. These methods are crucial in statistics for ensuring that samples accurately represent the population being studied."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Set theory", "label": "Set theory", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Set theory is a branch of mathematical logic that studies sets, which are collections of objects. It provides the foundational framework for various areas of mathematics, including functions, relations, and cardinality."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Sets", "label": "Sets", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Sets are fundamental mathematical objects that represent collections of distinct elements, often used to define relationships and operations in mathematics. They serve as the foundation for various branches of mathematics, including algebra, calculus, and discrete mathematics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Simple addition and subtraction", "label": "Simple addition and subtraction", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Simple addition and subtraction are fundamental arithmetic operations that involve combining quantities (addition) or determining the difference between quantities (subtraction). Mastery of these concepts is essential for further mathematical learning and everyday problem-solving."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Simple fractions and decimals", "label": "Simple fractions and decimals", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Simple fractions and decimals are fundamental mathematical concepts that represent parts of a whole. Understanding these concepts is essential for performing basic arithmetic operations and for grasping more complex mathematical ideas."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Simple geometric shapes and their properties", "label": "Simple geometric shapes and their properties", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic explores basic geometric shapes such as circles, triangles, squares, and rectangles, focusing on their defining characteristics, relationships, and the principles that govern their properties. Understanding these shapes is fundamental for further studies in geometry and spatial reasoning."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Statistical Analysis", "label": "Statistical Analysis", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Statistical analysis involves the collection, examination, interpretation, and presentation of data to uncover patterns and inform decision-making. It employs various statistical methods to summarize data and draw conclusions based on empirical evidence."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of Functions", "label": "Understanding of Functions", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Understanding of functions involves comprehending the concept of a relation between a set of inputs and outputs, where each input is associated with exactly one output. This foundational topic is crucial in mathematics and serves as a building block for more advanced concepts in algebra, calculus, and beyond."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of addition and subtraction", "label": "Understanding of addition and subtraction", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Addition and subtraction are fundamental arithmetic operations that involve combining quantities and determining the difference between them, respectively. Mastery of these concepts is essential for developing further mathematical skills and problem-solving abilities."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of algorithms", "label": "Understanding of algorithms", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Understanding of algorithms involves grasping the principles and techniques used to solve problems through a step-by-step procedure or formula. It encompasses the study of algorithm design, analysis, and optimization for efficient problem-solving in computer science."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of algorithms and data structures", "label": "Understanding of algorithms and data structures", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic involves the study of algorithms, which are step-by-step procedures for solving problems, and data structures, which are ways of organizing and storing data. Mastery of this topic is essential for efficient software development and problem-solving in computer science."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of basic arithmetic", "label": "Understanding of basic arithmetic", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic arithmetic involves the foundational mathematical operations of addition, subtraction, multiplication, and division, which are essential for solving numerical problems and understanding more complex mathematical concepts. Mastery of these operations is crucial for everyday calculations and further mathematical learning."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)", "label": "Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic encompasses the foundational mathematical operations that form the basis for more complex calculations and problem-solving. Mastery of these operations is essential for everyday mathematics and further mathematical learning."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of big O notation", "label": "Understanding of big O notation", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Big O notation is a mathematical concept used to describe the upper bound of an algorithm\u0027s time or space complexity, providing a high-level understanding of its efficiency in relation to input size. It helps in comparing the performance of different algorithms and understanding their scalability."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of computer systems", "label": "Understanding of computer systems", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Understanding computer systems involves comprehending the fundamental components and functions of hardware and software, including how they interact to perform tasks and process data. This knowledge is essential for troubleshooting, system design, and optimizing performance in various computing environments."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of coordinate systems", "label": "Understanding of coordinate systems", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Understanding of coordinate systems involves the study of systems that define a set of values used to represent points in space, typically through numerical coordinates. This topic is fundamental in mathematics, physics, and engineering, as it provides the framework for graphing and analyzing spatial relationships."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of coordinates and the Cartesian plane", "label": "Understanding of coordinates and the Cartesian plane", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic involves the comprehension of the Cartesian coordinate system, which uses ordered pairs to define the position of points in a two-dimensional space. It serves as a foundational concept in geometry and algebra, facilitating the representation and analysis of geometric shapes and relationships."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of geometric shapes", "label": "Understanding of geometric shapes", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Understanding of geometric shapes involves recognizing, classifying, and analyzing two-dimensional and three-dimensional figures based on their properties, such as sides, angles, and symmetry. This foundational knowledge is essential for further studies in geometry, spatial reasoning, and various applications in mathematics and science."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of logic and problem-solving", "label": "Understanding of logic and problem-solving", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic encompasses the principles of logical reasoning and the methodologies used to approach and resolve complex problems. It involves the application of deductive and inductive reasoning to analyze situations and derive solutions effectively."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of logical operators", "label": "Understanding of logical operators", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Logical operators are symbols or words used to connect two or more propositions in logic, allowing for the construction of complex logical statements. Mastery of these operators is essential for problem-solving in mathematics, computer science, and philosophy."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of mathematical logic", "label": "Understanding of mathematical logic", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Mathematical logic is a subfield of mathematics that deals with formal systems, symbolic reasoning, and the principles of valid inference. It encompasses the study of propositional and predicate logic, as well as the foundations of mathematical proofs and set theory."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of numbers", "label": "Understanding of numbers", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Understanding of numbers encompasses the ability to recognize, interpret, and manipulate numerical values and concepts. It serves as a foundational skill for mathematical reasoning and problem-solving across various disciplines."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of numbers (integers, fractions, decimals)", "label": "Understanding of numbers (integers, fractions, decimals)", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic encompasses the comprehension and manipulation of various types of numbers, including whole numbers (integers), parts of whole numbers (fractions), and numbers expressed in decimal form. Mastery of these concepts is essential for performing arithmetic operations and solving real-world mathematical problems."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of numbers (natural, whole, integers, rational, and real numbers)", "label": "Understanding of numbers (natural, whole, integers, rational, and real numbers)", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic covers the classification and properties of different types of numbers, including natural numbers, whole numbers, integers, rational numbers, and real numbers, providing a foundational understanding of numerical systems used in mathematics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of numerical order", "label": "Understanding of numerical order", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Understanding of numerical order involves recognizing and arranging numbers in a sequence based on their value, which is fundamental for performing arithmetic operations and solving mathematical problems. This concept is essential for developing number sense and is foundational for more advanced mathematical topics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of parentheses", "label": "Understanding of parentheses", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The understanding of parentheses involves recognizing their role in mathematical expressions and programming syntax as a means to indicate order of operations or to group elements. Mastery of this concept is essential for accurate computation and logical structuring in various fields, including mathematics and computer science."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of place value", "label": "Understanding of place value", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Place value is the numerical value that a digit holds based on its position within a number, which is essential for understanding the structure of the decimal number system. Mastery of place value enables students to perform operations with multi-digit numbers and comprehend larger numerical concepts."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of propositions", "label": "Understanding of propositions", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Understanding of propositions involves grasping the nature of declarative statements that can be classified as true or false. This foundational concept is essential in logic, mathematics, and philosophy, as it underpins more complex reasoning and argumentation structures."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of software development life cycle (SDLC)", "label": "Understanding of software development life cycle (SDLC)", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The software development life cycle (SDLC) is a structured process that outlines the stages involved in developing software applications, from initial planning and requirements gathering to design, implementation, testing, deployment, and maintenance. Understanding SDLC is crucial for ensuring that software projects are completed efficiently and meet user requirements."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of symbols and operations", "label": "Understanding of symbols and operations", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic encompasses the ability to recognize, interpret, and manipulate mathematical symbols and operations, which are foundational for problem-solving in mathematics. It is essential for developing skills in algebra, arithmetic, and higher-level mathematics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of variables and constants", "label": "Understanding of variables and constants", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: This topic involves the comprehension of variables as symbolic representations of data that can change, and constants as fixed values that do not change. Mastery of these concepts is essential for foundational programming and mathematical problem-solving."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of whole numbers", "label": "Understanding of whole numbers", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Understanding whole numbers involves recognizing and working with non-negative integers, including concepts of counting, ordering, and basic arithmetic operations such as addition and subtraction. This foundational knowledge is essential for further mathematical learning and problem-solving."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Visual perception skills", "label": "Visual perception skills", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Visual perception skills refer to the ability to interpret and make sense of visual information from the environment, encompassing processes such as recognition, discrimination, and spatial awareness. These skills are crucial for tasks such as reading, writing, and navigating spaces effectively."}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "red", "dashes": true, "from": "Deep Learning", "to": "Linear Algebra", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Deep Learning", "to": "Calculus", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Deep Learning", "to": "Probability and Statistics", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Deep Learning", "to": "Machine Learning Fundamentals", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Deep Learning", "to": "Programming in Python", "width": 1}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Trigonometry", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Calculus", "to": "Algebra", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Calculus", "to": "Functions", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Calculus", "to": "Limits", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Linear Algebra", "to": "Basic Algebra", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Linear Algebra", "to": "Geometry", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Linear Algebra", "to": "Functions and Graphs", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Linear Algebra", "to": "Trigonometry", "width": 1}, {"arrows": "to", "color": "blue", "from": "Machine Learning Fundamentals", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning Fundamentals", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Machine Learning Fundamentals", "to": "Statistics", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Machine Learning Fundamentals", "to": "Programming Basics", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Machine Learning Fundamentals", "to": "Data Structures", "width": 1}, {"arrows": "to", "color": "blue", "from": "Probability and Statistics", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability and Statistics", "to": "Functions and Graphs", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Probability and Statistics", "to": "Set Theory", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Probability and Statistics", "to": "Descriptive Statistics", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Probability and Statistics", "to": "Basic Calculus", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Programming in Python", "to": "Basic understanding of computer science concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Programming in Python", "to": "Familiarity with algorithms and data structures", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Programming in Python", "to": "Knowledge of basic mathematics", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Programming in Python", "to": "Experience with using a text editor or integrated development environment (IDE)", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Programming in Python", "to": "Understanding of fundamental programming concepts such as variables, control structures, and functions", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Algebra", "to": "Arithmetic", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Algebra", "to": "Basic Geometry", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Algebra", "to": "Number Theory", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Algebra", "to": "Understanding of Variables", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Algebra", "to": "Arithmetic operations", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Algebra", "to": "Understanding of variables", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Algebra", "to": "Basic properties of numbers", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Algebra", "to": "Order of operations", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic Calculus", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Calculus", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Calculus", "to": "Graphing", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Calculus", "to": "Trigonometry", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic understanding of computer science concepts", "to": "Mathematical reasoning", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic understanding of computer science concepts", "to": "Basic programming skills", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic understanding of computer science concepts", "to": "Understanding of binary and number systems", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic understanding of computer science concepts", "to": "Familiarity with logical operators", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Structures", "to": "Basic Programming Concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Structures", "to": "Algorithms", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Structures", "to": "Mathematics for Computer Science", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Structures", "to": "Complexity Analysis", "width": 1}, {"arrows": "to", "color": "blue", "from": "Descriptive Statistics", "to": "Understanding of Variables", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Descriptive Statistics", "to": "Basic Mathematics", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Descriptive Statistics", "to": "Data Collection Techniques", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Descriptive Statistics", "to": "Introduction to Probability", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Experience with using a text editor or integrated development environment (IDE)", "to": "Basic understanding of programming concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Experience with using a text editor or integrated development environment (IDE)", "to": "Familiarity with file systems and directory structures", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Experience with using a text editor or integrated development environment (IDE)", "to": "Knowledge of programming languages syntax", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Experience with using a text editor or integrated development environment (IDE)", "to": "Introduction to software development methodologies", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with algorithms and data structures", "to": "Basic programming concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with algorithms and data structures", "to": "Mathematical foundations (e.g., discrete mathematics)", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with algorithms and data structures", "to": "Understanding of complexity analysis", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with algorithms and data structures", "to": "Basic knowledge of recursion", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with algorithms and data structures", "to": "Familiarity with basic data types", "width": 1}, {"arrows": "to", "color": "blue", "from": "Functions", "to": "Algebraic expressions", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Functions", "to": "Variables", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Functions", "to": "Graphing", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Functions", "to": "Basic arithmetic operations", "width": 1}, {"arrows": "to", "color": "blue", "from": "Functions and Graphs", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Functions and Graphs", "to": "Algebraic expressions", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Functions and Graphs", "to": "Coordinate geometry", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Functions and Graphs", "to": "Inequalities", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Functions and Graphs", "to": "Linear equations", "width": 1}, {"arrows": "to", "color": "blue", "from": "Geometry", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Geometry", "to": "Basic arithmetic", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Geometry", "to": "Understanding of spatial reasoning", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Geometry", "to": "Introduction to mathematical proofs", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Knowledge of basic mathematics", "to": "Understanding of numbers and number systems", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Knowledge of basic mathematics", "to": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Knowledge of basic mathematics", "to": "Concept of place value", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Knowledge of basic mathematics", "to": "Understanding of fractions and decimals", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Knowledge of basic mathematics", "to": "Basic problem-solving skills", "width": 1}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Graphing", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Limits", "to": "Basic Trigonometry", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Programming Basics", "to": "Basic mathematics", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Programming Basics", "to": "Logical reasoning", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Programming Basics", "to": "Problem-solving skills", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Set Theory", "to": "Basic Logic", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Set Theory", "to": "Mathematical Notation", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Set Theory", "to": "Elementary Algebra", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Set Theory", "to": "Functions and Relations", "width": 1}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Statistics", "to": "Probability Theory", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Statistics", "to": "Data Analysis", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Statistics", "to": "Mathematical Reasoning", "width": 1}, {"arrows": "to", "color": "blue", "from": "Trigonometry", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Trigonometry", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Trigonometry", "to": "Understanding of Angles", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Trigonometry", "to": "Coordinate Systems", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of fundamental programming concepts such as variables, control structures, and functions", "to": "Basic mathematical concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of fundamental programming concepts such as variables, control structures, and functions", "to": "Logical reasoning skills", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of fundamental programming concepts such as variables, control structures, and functions", "to": "Introduction to computer science", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of fundamental programming concepts such as variables, control structures, and functions", "to": "Familiarity with a programming environment", "width": 1}, {"arrows": "to", "color": "blue", "from": "Algebraic expressions", "to": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic expressions", "to": "Order of operations (PEMDAS/BODMAS)", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Algebraic expressions", "to": "Understanding of variables and constants", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Algebraic expressions", "to": "Introduction to equations and inequalities", "width": 1}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Algorithms", "to": "Data structures", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Algorithms", "to": "Mathematical logic", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Algorithms", "to": "Complexity theory", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Algorithms", "to": "Discrete mathematics", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Arithmetic", "to": "Number recognition", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Arithmetic", "to": "Basic counting", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Arithmetic", "to": "Understanding of numerical order", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Arithmetic", "to": "Simple addition and subtraction", "width": 1}, {"arrows": "to", "color": "blue", "from": "Arithmetic operations", "to": "Basic properties of numbers", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Arithmetic operations", "to": "Number sense", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Arithmetic operations", "to": "Understanding of whole numbers", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Arithmetic operations", "to": "Concept of equality", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Geometry", "to": "Understanding of basic arithmetic", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Geometry", "to": "Familiarity with basic algebra", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Geometry", "to": "Knowledge of spatial reasoning", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Geometry", "to": "Introduction to mathematical reasoning", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Logic", "to": "Understanding of propositions", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Logic", "to": "Familiarity with logical connectives", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Logic", "to": "Basic comprehension of truth tables", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic Mathematics", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Mathematics", "to": "Basic counting", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Mathematics", "to": "Understanding of addition and subtraction", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Mathematics", "to": "Familiarity with multiplication and division", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic Programming Concepts", "to": "Mathematical reasoning", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Programming Concepts", "to": "Logical thinking", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Programming Concepts", "to": "Basic computer literacy", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic Trigonometry", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Trigonometry", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Trigonometry", "to": "Understanding of Angles", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic Trigonometry", "to": "Coordinate System", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic arithmetic", "to": "Understanding of numbers", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic arithmetic", "to": "Concept of quantity", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic arithmetic", "to": "Basic number recognition", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic arithmetic", "to": "Understanding of symbols and operations", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic arithmetic operations", "to": "Understanding of numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic arithmetic operations", "to": "Concept of quantity", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic arithmetic operations", "to": "Basic number line usage", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "to": "Understanding of numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "to": "Concept of quantity", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "to": "Familiarity with counting", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "to": "Basic number sense", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic knowledge of recursion", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic knowledge of recursion", "to": "Control structures (if statements, loops)", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic knowledge of recursion", "to": "Functions and procedures", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic knowledge of recursion", "to": "Data structures (arrays, lists)", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic mathematical concepts", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic mathematical concepts", "to": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic mathematical concepts", "to": "Understanding of place value", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic mathematical concepts", "to": "Simple geometric shapes and their properties", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic mathematics", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic mathematics", "to": "Counting", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic mathematics", "to": "Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic mathematics", "to": "Simple fractions and decimals", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic problem-solving skills", "to": "Critical thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic problem-solving skills", "to": "Analytical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic problem-solving skills", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic problem-solving skills", "to": "Communication skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic programming concepts", "to": "Familiarity with mathematical concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic programming concepts", "to": "Logical reasoning skills", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic programming concepts", "to": "Understanding of algorithms", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic programming concepts", "to": "Basic knowledge of computer operations", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic programming skills", "to": "Understanding of logic and problem-solving", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic programming skills", "to": "Familiarity with mathematical concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic programming skills", "to": "Basic knowledge of computer operation", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic programming skills", "to": "Introduction to algorithms", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic properties of numbers", "to": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic properties of numbers", "to": "Understanding of numbers (natural, whole, integers, rational, and real numbers)", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic properties of numbers", "to": "Order of operations (PEMDAS/BODMAS)", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic properties of numbers", "to": "Concept of equality and inequalities", "width": 1}, {"arrows": "to", "color": "blue", "from": "Basic understanding of programming concepts", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic understanding of programming concepts", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic understanding of programming concepts", "to": "Understanding of computer systems", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Basic understanding of programming concepts", "to": "Familiarity with problem-solving techniques", "width": 1}, {"arrows": "to", "color": "blue", "from": "Complexity Analysis", "to": "Basic Programming Concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Complexity Analysis", "to": "Data Structures", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Complexity Analysis", "to": "Algorithm Design", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Complexity Analysis", "to": "Mathematical Foundations", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Complexity Analysis", "to": "Big O Notation", "width": 1}, {"arrows": "to", "color": "blue", "from": "Concept of place value", "to": "Understanding of numbers", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Concept of place value", "to": "Basic counting skills", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Concept of place value", "to": "Knowledge of the decimal system", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Concept of place value", "to": "Familiarity with addition and subtraction", "width": 1}, {"arrows": "to", "color": "blue", "from": "Coordinate Systems", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate Systems", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate Systems", "to": "Trigonometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate Systems", "to": "Functions and Graphs", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate geometry", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Coordinate geometry", "to": "Understanding of geometric shapes", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Coordinate geometry", "to": "Knowledge of the Cartesian coordinate system", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Coordinate geometry", "to": "Basic properties of lines and angles", "width": 1}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Data Management", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Analysis", "to": "Mathematics", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Analysis", "to": "Basic Programming", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Analysis", "to": "Data Visualization", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Collection Techniques", "to": "Research Methodology", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Collection Techniques", "to": "Statistical Analysis", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Collection Techniques", "to": "Sampling Methods", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Collection Techniques", "to": "Ethics in Research", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Data Collection Techniques", "to": "Data Management", "width": 1}, {"arrows": "to", "color": "blue", "from": "Elementary Algebra", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Elementary Algebra", "to": "Order of operations", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Elementary Algebra", "to": "Understanding of numbers (integers, fractions, decimals)", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Elementary Algebra", "to": "Introduction to variables and expressions", "width": 1}, {"arrows": "to", "color": "blue", "from": "Familiarity with a programming environment", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with a programming environment", "to": "Knowledge of version control systems", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with a programming environment", "to": "Understanding of algorithms and data structures", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with a programming environment", "to": "Familiarity with command line interfaces", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with a programming environment", "to": "Basic troubleshooting and debugging skills", "width": 1}, {"arrows": "to", "color": "blue", "from": "Familiarity with basic data types", "to": "Understanding of variables", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with basic data types", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with basic data types", "to": "Introduction to programming concepts", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with basic data types", "to": "Familiarity with logic and boolean expressions", "width": 1}, {"arrows": "to", "color": "blue", "from": "Familiarity with file systems and directory structures", "to": "Introduction to programming concepts", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with file systems and directory structures", "to": "Basic understanding of computer systems", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with file systems and directory structures", "to": "Knowledge of operating systems", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with file systems and directory structures", "to": "Familiarity with data storage concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with logical operators", "to": "Basic understanding of Boolean algebra", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with logical operators", "to": "Introduction to programming concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with logical operators", "to": "Knowledge of conditional statements", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Familiarity with logical operators", "to": "Familiarity with truth tables", "width": 1}, {"arrows": "to", "color": "blue", "from": "Functions and Relations", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions and Relations", "to": "Graphing", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Functions and Relations", "to": "Sets", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Functions and Relations", "to": "Ordered pairs", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Functions and Relations", "to": "Domain and range", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Graphing", "to": "Understanding of coordinates and the Cartesian plane", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Graphing", "to": "Basic algebraic concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Graphing", "to": "Knowledge of functions and their properties", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Graphing", "to": "Ability to interpret data sets", "width": 1}, {"arrows": "to", "color": "blue", "from": "Inequalities", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Inequalities", "to": "Algebraic expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Inequalities", "to": "Understanding of variables", "width": 2}, {"arrows": "to", "color": "blue", "from": "Inequalities", "to": "Basic properties of numbers", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Inequalities", "to": "Graphing on a number line", "width": 1}, {"arrows": "to", "color": "blue", "from": "Introduction to Probability", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to Probability", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Introduction to Probability", "to": "Understanding of Functions", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Introduction to Probability", "to": "Basic Statistics", "width": 1}, {"arrows": "to", "color": "blue", "from": "Introduction to computer science", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to computer science", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to computer science", "to": "Understanding of algorithms", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Introduction to computer science", "to": "Familiarity with programming concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Introduction to mathematical proofs", "to": "Basic algebra", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Introduction to mathematical proofs", "to": "Understanding of mathematical logic", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Introduction to mathematical proofs", "to": "Familiarity with set theory", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Introduction to mathematical proofs", "to": "Introduction to functions and relations", "width": 1}, {"arrows": "to", "color": "blue", "from": "Introduction to software development methodologies", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Introduction to software development methodologies", "to": "Understanding of software development life cycle (SDLC)", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Introduction to software development methodologies", "to": "Familiarity with project management principles", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Introduction to software development methodologies", "to": "Knowledge of version control systems", "width": 1}, {"arrows": "to", "color": "blue", "from": "Knowledge of programming languages syntax", "to": "Familiarity with algorithms and data structures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Knowledge of programming languages syntax", "to": "Introduction to programming concepts", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Knowledge of programming languages syntax", "to": "Basic understanding of computer science principles", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Knowledge of programming languages syntax", "to": "Logical reasoning and problem-solving skills", "width": 1}, {"arrows": "to", "color": "blue", "from": "Linear equations", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear equations", "to": "Understanding of variables", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear equations", "to": "Order of operations", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Linear equations", "to": "Graphing concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Linear equations", "to": "Algebraic manipulation", "width": 1}, {"arrows": "to", "color": "blue", "from": "Logical reasoning", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Logical reasoning", "to": "Critical thinking", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Logical reasoning", "to": "Understanding of logical operators", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Logical reasoning", "to": "Familiarity with argument structures", "width": 1}, {"arrows": "to", "color": "blue", "from": "Logical reasoning skills", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical reasoning skills", "to": "Understanding of logical operators", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Logical reasoning skills", "to": "Familiarity with syllogisms", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Logical reasoning skills", "to": "Critical thinking skills", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Logical reasoning skills", "to": "Introduction to argumentation", "width": 1}, {"arrows": "to", "color": "blue", "from": "Mathematical Notation", "to": "Basic Arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Notation", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Mathematical Notation", "to": "Algebraic Expressions", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Mathematical Notation", "to": "Logical Reasoning", "width": 1}, {"arrows": "to", "color": "blue", "from": "Mathematical Reasoning", "to": "Basic Arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Reasoning", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Reasoning", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Mathematical Reasoning", "to": "Logic", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Mathematical Reasoning", "to": "Proof Techniques", "width": 1}, {"arrows": "to", "color": "blue", "from": "Mathematical foundations (e.g., discrete mathematics)", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical foundations (e.g., discrete mathematics)", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical foundations (e.g., discrete mathematics)", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Mathematical foundations (e.g., discrete mathematics)", "to": "Logic and reasoning", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Mathematical foundations (e.g., discrete mathematics)", "to": "Functions and relations", "width": 1}, {"arrows": "to", "color": "blue", "from": "Mathematical reasoning", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical reasoning", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical reasoning", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Mathematical reasoning", "to": "Set theory", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Mathematical reasoning", "to": "Proof techniques", "width": 1}, {"arrows": "to", "color": "blue", "from": "Mathematics for Computer Science", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics for Computer Science", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics for Computer Science", "to": "Functions and Relations", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Mathematics for Computer Science", "to": "Logic and Propositional Calculus", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Mathematics for Computer Science", "to": "Basic Number Theory", "width": 1}, {"arrows": "to", "color": "blue", "from": "Number Theory", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Theory", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Theory", "to": "Functions and Relations", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Number Theory", "to": "Basic Arithmetic", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Number Theory", "to": "Mathematical Proof Techniques", "width": 1}, {"arrows": "to", "color": "blue", "from": "Order of operations", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Order of operations", "to": "Understanding of parentheses", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Order of operations", "to": "Knowledge of exponents", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Order of operations", "to": "Order of operations rules", "width": 1}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Probability Theory", "to": "Combinatorics", "width": 1}, {"arrows": "to", "color": "blue", "from": "Problem-solving skills", "to": "Critical thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem-solving skills", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Problem-solving skills", "to": "Analytical reasoning", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Problem-solving skills", "to": "Communication skills", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Problem-solving skills", "to": "Creativity", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of Angles", "to": "Basic geometric shapes", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of Angles", "to": "Line segments and rays", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of Angles", "to": "Measurement concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of Angles", "to": "Properties of parallel and intersecting lines", "width": 1}, {"arrows": "to", "color": "blue", "from": "Understanding of Variables", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Variables", "to": "Algebraic expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Variables", "to": "Mathematical notation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Variables", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of binary and number systems", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of binary and number systems", "to": "Understanding of place value", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of binary and number systems", "to": "Introduction to mathematical logic", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of binary and number systems", "to": "Familiarity with decimal number system", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of complexity analysis", "to": "Basic understanding of algorithms", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of complexity analysis", "to": "Foundational knowledge of data structures", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of complexity analysis", "to": "Introduction to mathematical notation", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of complexity analysis", "to": "Basic principles of computer science", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of complexity analysis", "to": "Understanding of big O notation", "width": 1}, {"arrows": "to", "color": "blue", "from": "Understanding of fractions and decimals", "to": "Basic number sense", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of fractions and decimals", "to": "Understanding of whole numbers", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of fractions and decimals", "to": "Introduction to division", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of fractions and decimals", "to": "Concept of ratios", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of fractions and decimals", "to": "Place value system", "width": 1}, {"arrows": "to", "color": "blue", "from": "Understanding of numbers and number systems", "to": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of numbers and number systems", "to": "Understanding of place value", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of numbers and number systems", "to": "Familiarity with fractions and decimals", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of numbers and number systems", "to": "Introduction to mathematical symbols and notation", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of spatial reasoning", "to": "Basic geometry concepts", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of spatial reasoning", "to": "Visual perception skills", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of spatial reasoning", "to": "Understanding of coordinate systems", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of spatial reasoning", "to": "Introduction to logical reasoning", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of spatial reasoning", "to": "Familiarity with shapes and their properties", "width": 1}, {"arrows": "to", "color": "blue", "from": "Understanding of variables", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of variables", "to": "Algebraic expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of variables", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Understanding of variables", "to": "Mathematical notation", "width": 1}, {"arrows": "to", "color": "blue", "from": "Variables", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "red", "dashes": true, "from": "Variables", "to": "Data Types", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Variables", "to": "Mathematical Operations", "width": 1}, {"arrows": "to", "color": "red", "dashes": true, "from": "Variables", "to": "Control Structures", "width": 1}, {"arrows": "to", "color": "blue", "from": "Ability to interpret data sets", "to": "Critical thinking skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic Expressions", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic Expressions", "to": "Understanding of variables", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic manipulation", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic manipulation", "to": "Understanding of variables and constants", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithm Design", "to": "Data Structures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithm Design", "to": "Mathematical Foundations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithm Design", "to": "Complexity Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Analytical reasoning", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Analytical reasoning", "to": "Critical thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Analytical reasoning", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Analytical reasoning", "to": "Problem-solving skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Arithmetic", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Number Theory", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Number Theory", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Programming", "to": "Understanding of algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Programming", "to": "Basic mathematical concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Programming", "to": "Logical reasoning skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Statistics", "to": "Arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Statistics", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic algebra", "to": "Arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic algebra", "to": "Basic properties of numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic algebra", "to": "Order of operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic algebraic concepts", "to": "Understanding of numbers and number systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic algebraic concepts", "to": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic counting", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic counting skills", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic counting skills", "to": "Basic number sense", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic geometry concepts", "to": "Familiarity with shapes and their properties", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic number line usage", "to": "Understanding of whole numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic number line usage", "to": "Basic counting skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic number sense", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic principles of computer science", "to": "Mathematical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic principles of computer science", "to": "Basic programming skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic troubleshooting and debugging skills", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic understanding of Boolean algebra", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic understanding of Boolean algebra", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic understanding of algorithms", "to": "Mathematical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic understanding of algorithms", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic understanding of algorithms", "to": "Logical thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic understanding of computer science principles", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic understanding of computer science principles", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic understanding of computer systems", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic understanding of computer systems", "to": "Understanding of binary and number systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Big O Notation", "to": "Basic understanding of algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Big O Notation", "to": "Introduction to mathematical notation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Combinatorics", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Combinatorics", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Communication skills", "to": "Critical thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Complexity theory", "to": "Discrete mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Complexity theory", "to": "Algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Complexity theory", "to": "Data structures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Complexity theory", "to": "Mathematical logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Concept of equality and inequalities", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Concept of quantity", "to": "Number sense", "width": 2}, {"arrows": "to", "color": "blue", "from": "Concept of quantity", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Concept of quantity", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Concept of ratios", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Concept of ratios", "to": "Number sense", "width": 2}, {"arrows": "to", "color": "blue", "from": "Control Structures", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Control structures (if statements, loops)", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate System", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate System", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate System", "to": "Understanding of Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical thinking", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical thinking skills", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Types", "to": "Variables", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Types", "to": "Basic Programming Concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Types", "to": "Control Structures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Visualization", "to": "Basic Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Visualization", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Visualization", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data structures", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data structures", "to": "Algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data structures (arrays, lists)", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data structures (arrays, lists)", "to": "Functions and procedures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Discrete mathematics", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Discrete mathematics", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Discrete mathematics", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Discrete mathematics", "to": "Functions and relations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Domain and range", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Domain and range", "to": "Sets", "width": 2}, {"arrows": "to", "color": "blue", "from": "Domain and range", "to": "Graphing", "width": 2}, {"arrows": "to", "color": "blue", "from": "Domain and range", "to": "Algebraic expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ethics in Research", "to": "Research Methodology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ethics in Research", "to": "Data Management", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with addition and subtraction", "to": "Basic counting skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with command line interfaces", "to": "Basic computer literacy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with counting", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with data storage concepts", "to": "Basic understanding of computer systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with decimal number system", "to": "Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with fractions and decimals", "to": "Understanding of whole numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with logic and boolean expressions", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with logic and boolean expressions", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with logic and boolean expressions", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with mathematical concepts", "to": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with problem-solving techniques", "to": "Critical thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with problem-solving techniques", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with problem-solving techniques", "to": "Analytical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with programming concepts", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with programming concepts", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with programming concepts", "to": "Understanding of computer systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with programming concepts", "to": "Basic problem-solving skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with set theory", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Familiarity with truth tables", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Foundational knowledge of data structures", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Foundational knowledge of data structures", "to": "Understanding of algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Foundational knowledge of data structures", "to": "Mathematical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions and procedures", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions and procedures", "to": "Control structures (if statements, loops)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions and relations", "to": "Sets", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions and relations", "to": "Ordered pairs", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions and relations", "to": "Domain and range", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions and relations", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing concepts", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing concepts", "to": "Functions and relations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing concepts", "to": "Algebraic expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to algorithms", "to": "Basic programming skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to argumentation", "to": "Critical thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to equations and inequalities", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to equations and inequalities", "to": "Understanding of variables and constants", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to functions and relations", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to mathematical logic", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to mathematical logic", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to mathematical notation", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to mathematical notation", "to": "Understanding of numbers (integers, fractions, decimals)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to mathematical reasoning", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to mathematical reasoning", "to": "Familiarity with logical operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to mathematical reasoning", "to": "Basic problem-solving skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to mathematical symbols and notation", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to programming concepts", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to programming concepts", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to variables and expressions", "to": "Introduction to mathematical notation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to variables and expressions", "to": "Basic problem-solving skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Knowledge of conditional statements", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Knowledge of exponents", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Knowledge of exponents", "to": "Order of operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Knowledge of functions and their properties", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Knowledge of spatial reasoning", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Knowledge of the Cartesian coordinate system", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Knowledge of the decimal system", "to": "Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Line segments and rays", "to": "Basic geometric shapes", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic and reasoning", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic and reasoning", "to": "Understanding of propositions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Reasoning", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Reasoning", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical reasoning and problem-solving skills", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical reasoning and problem-solving skills", "to": "Critical thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical reasoning and problem-solving skills", "to": "Understanding of logical operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical thinking", "to": "Critical thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical thinking", "to": "Basic mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical thinking", "to": "Understanding of logical operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical thinking", "to": "Problem-solving skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Foundations", "to": "Basic Arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Foundations", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Foundations", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Foundations", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Foundations", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Proof Techniques", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Proof Techniques", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Proof Techniques", "to": "Logical Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Proof Techniques", "to": "Functions and Relations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical notation", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical notation", "to": "Algebraic expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical notation", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical notation", "to": "Logic and reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical notation", "to": "Functions and relations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Logical Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement concepts", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number recognition", "to": "Basic counting skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number sense", "to": "Basic counting skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number sense", "to": "Understanding of whole numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Order of operations (PEMDAS/BODMAS)", "to": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Order of operations rules", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Order of operations rules", "to": "Understanding of parentheses", "width": 2}, {"arrows": "to", "color": "blue", "from": "Place value system", "to": "Understanding of numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Place value system", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Proof Techniques", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Proof Techniques", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Proof Techniques", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Proof Techniques", "to": "Functions and relations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Proof Techniques", "to": "Mathematical notation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Proof techniques", "to": "Mathematical logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Proof techniques", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Proof techniques", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Proof techniques", "to": "Functions and relations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Research Methodology", "to": "Basic Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Research Methodology", "to": "Data Collection Techniques", "width": 2}, {"arrows": "to", "color": "blue", "from": "Research Methodology", "to": "Ethics in Research", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sampling Methods", "to": "Basic Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sampling Methods", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sampling Methods", "to": "Data Collection Techniques", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sampling Methods", "to": "Descriptive Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set theory", "to": "Mathematical notation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Simple addition and subtraction", "to": "Understanding of numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Simple addition and subtraction", "to": "Basic counting skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Simple addition and subtraction", "to": "Concept of quantity", "width": 2}, {"arrows": "to", "color": "blue", "from": "Simple fractions and decimals", "to": "Understanding of whole numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Simple fractions and decimals", "to": "Place value system", "width": 2}, {"arrows": "to", "color": "blue", "from": "Simple geometric shapes and their properties", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Analysis", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Analysis", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Analysis", "to": "Descriptive Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Analysis", "to": "Data Visualization", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Functions", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Functions", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Functions", "to": "Understanding of Variables", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of addition and subtraction", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of addition and subtraction", "to": "Basic number sense", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of algorithms", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of algorithms", "to": "Data structures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of algorithms", "to": "Mathematical foundations (e.g., discrete mathematics)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of algorithms and data structures", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of algorithms and data structures", "to": "Mathematical foundations (e.g., discrete mathematics)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of basic arithmetic", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of basic arithmetic", "to": "Counting", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of basic arithmetic", "to": "Understanding of numerical order", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)", "to": "Basic counting skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)", "to": "Concept of quantity", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of big O notation", "to": "Basic understanding of algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of computer systems", "to": "Basic computer literacy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of coordinate systems", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of coordinate systems", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of coordinates and the Cartesian plane", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of logic and problem-solving", "to": "Introduction to logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of logic and problem-solving", "to": "Critical thinking skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of logical operators", "to": "Familiarity with truth tables", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of mathematical logic", "to": "Basic algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of mathematical logic", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of mathematical logic", "to": "Proof techniques", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of mathematical logic", "to": "Functions and relations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of numbers", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of numbers", "to": "Basic counting skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of numbers (integers, fractions, decimals)", "to": "Basic number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of numbers (integers, fractions, decimals)", "to": "Concept of quantity", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of numbers (integers, fractions, decimals)", "to": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of numbers (natural, whole, integers, rational, and real numbers)", "to": "Basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of numerical order", "to": "Number recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of numerical order", "to": "Basic counting skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of parentheses", "to": "Basic arithmetic operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of parentheses", "to": "Order of operations (PEMDAS/BODMAS)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of parentheses", "to": "Algebraic expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of parentheses", "to": "Logical reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of place value", "to": "Basic counting skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of place value", "to": "Knowledge of the decimal system", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of propositions", "to": "Set theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of propositions", "to": "Critical thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of software development life cycle (SDLC)", "to": "Basic programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of symbols and operations", "to": "Basic number sense", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of symbols and operations", "to": "Understanding of basic arithmetic operations (addition, subtraction, multiplication, division)", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of variables and constants", "to": "Basic arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of variables and constants", "to": "Algebraic expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of variables and constants", "to": "Mathematical notation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of variables and constants", "to": "Introduction to programming concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of whole numbers", "to": "Number recognition", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 100}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  
                      network.on("stabilizationProgress", function(params) {
                          document.getElementById('loadingBar').removeAttribute("style");
                          var maxWidth = 496;
                          var minWidth = 20;
                          var widthFactor = params.iterations/params.total;
                          var width = Math.max(minWidth,maxWidth * widthFactor);
                          document.getElementById('bar').style.width = width + 'px';
                          document.getElementById('text').innerHTML = Math.round(widthFactor*100) + '%';
                      });
                      network.once("stabilizationIterationsDone", function() {
                          document.getElementById('text').innerHTML = '100%';
                          document.getElementById('bar').style.width = '496px';
                          document.getElementById('loadingBar').style.opacity = 0;
                          // really clean the dom element
                          setTimeout(function () {document.getElementById('loadingBar').style.display = 'none';}, 500);
                      });
                  

                  return network;

              }
              drawGraph();
        </script>
</body>
</html>
        