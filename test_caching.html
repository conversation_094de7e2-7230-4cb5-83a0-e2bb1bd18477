
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .graph-container {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .graph-container.expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1000;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .graph-container.expanded .graph-content {
            height: calc(100vh - 120px) !important;
        }
        .graph-container.expanded .graph-content > div {
            height: 100% !important;
        }
        .graph-container.expanded #mynetworkid {
            height: 100% !important;
        }
        .graph-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 5px;
        }
        .control-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .control-btn:hover {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .graph-content {
            height: 500px;
            transition: height 0.3s ease;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f0f0;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="overlay" id="overlay" onclick="toggleExpand()"></div>

    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">59</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">4</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">15</div>
                <div class="stat-label">Topics with Dependencies</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>

            <div class="graph-container" id="graphContainer">
                <div class="graph-controls">
                    <button class="control-btn" onclick="toggleExpand()" id="expandBtn">
                        🔍 Expand
                    </button>
                    <button class="control-btn" onclick="resetGraph()">
                        🔄 Reset View
                    </button>
                </div>
                <div class="graph-content">
                    <div id="mynetwork" class="card-body"></div>
                </div>
            </div>
        </div>

        
        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th>Depth</th>
                        <th>Term</th>
                        <th>Description</th>
                        <th>Seen Dependencies</th>
                        <th>Unseen Dependencies</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Machine Learning</strong></td>
                    <td>A field of artificial intelligence that uses statistical techniques to give computer systems the ability to learn.</td>
                    <td><span style="color: blue;">Statistics, Linear Algebra, Calculus, Programming Fundamentals</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Python Programming</strong></td>
                    <td>Error fetching description for Python Programming</td>
                    <td><span style="color: blue;">Programming Fundamentals, Computer Science, Logic, Mathematics</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Basic problem-solving skills</strong></td>
                    <td>Basic problem-solving skills involve the ability to identify, analyze, and resolve issues effectively using logical reasoning and critical thinking. These skills are essential for making informed decisions and navigating everyday challenges.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Basic understanding of programming concepts</strong></td>
                    <td>This topic encompasses the foundational principles of programming, including variables, data types, control structures, and algorithms. It serves as an essential introduction for individuals seeking to develop their coding skills and understand how software operates.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Calculus</strong></td>
                    <td>The mathematical study of continuous change.</td>
                    <td><span style="color: blue;">Mathematics, Limits, Functions</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Data Preprocessing</strong></td>
                    <td>Data Preprocessing is the process of transforming raw data into a clean and usable format for analysis, which includes tasks such as data cleaning, normalization, and feature selection. It is a crucial step in the data analysis pipeline that ensures the quality and relevance of the data used in machine learning models.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Familiarity with data types and variables</strong></td>
                    <td>Familiarity with data types and variables involves understanding the different kinds of data that can be stored in programming languages and how these data types can be manipulated through variables. This foundational knowledge is essential for effective programming and data management.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Knowledge of control structures (if statements, loops)</strong></td>
                    <td>Control structures are fundamental programming constructs that dictate the flow of execution in a program. Understanding if statements and loops is essential for implementing decision-making and repetitive tasks in coding.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Linear Algebra</strong></td>
                    <td>The branch of mathematics concerning linear equations and linear functions.</td>
                    <td><span style="color: blue;">Mathematics, Vector Spaces</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Programming</strong></td>
                    <td>The process of creating a set of instructions that tell a computer how to perform a task.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Programming (Python or R)</strong></td>
                    <td>Programming in Python or R involves writing code to create software applications, analyze data, and automate tasks using these high-level programming languages. Both languages are widely used in data science, web development, and scientific computing due to their simplicity and extensive libraries.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Programming Fundamentals</strong></td>
                    <td>Basic concepts of computer programming including variables, control structures, and algorithms.</td>
                    <td><span style="color: blue;">Logic, Mathematics, Computer Science</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Statistics</strong></td>
                    <td>The discipline that concerns the collection, organization, analysis, interpretation, and presentation of data.</td>
                    <td><span style="color: blue;">Mathematics, Probability Theory</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Understanding of functions and modular programming</strong></td>
                    <td>This topic encompasses the principles of defining and using functions to encapsulate code, as well as the organization of code into modules to promote reusability and maintainability in programming. It is essential for developing structured and efficient software applications.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Algebra</strong></td>
                    <td>Algebra is a branch of mathematics that deals with symbols and the rules for manipulating those symbols to solve equations and understand relationships between quantities. It serves as a foundational tool for higher-level mathematics and various applications in science and engineering.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Algorithms</strong></td>
                    <td>A finite sequence of well-defined instructions for solving a problem.</td>
                    <td><span style="color: blue;">Computer Science, Mathematics, Logic, Data Structures</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Analytical skills</strong></td>
                    <td>Analytical skills refer to the ability to systematically and critically evaluate information, identify patterns, and solve problems through logical reasoning. These skills are essential for decision-making and are applicable across various disciplines, including science, mathematics, and social studies.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic Algebra</strong></td>
                    <td>Basic Algebra is the branch of mathematics that deals with the manipulation of symbols and the rules for solving equations, focusing on operations with variables and constants. It serves as a foundational skill for higher-level mathematics and various applications in science and engineering.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic Statistics</strong></td>
                    <td>Basic Statistics involves the collection, analysis, interpretation, presentation, and organization of data. It provides foundational tools for understanding data patterns and making informed decisions based on quantitative information.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic computer literacy</strong></td>
                    <td>Basic computer literacy refers to the essential skills and knowledge required to effectively use computers and technology, including understanding hardware, software, and basic troubleshooting. It encompasses the ability to navigate operating systems, utilize applications, and engage with the internet safely and responsibly.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic input/output operations</strong></td>
                    <td>Basic input/output operations refer to the fundamental processes through which a computer system receives data (input) and sends data (output) to various devices, enabling interaction with users and other systems. These operations are essential for understanding how data is processed and communicated within software applications.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic knowledge of data structures</strong></td>
                    <td>Basic knowledge of data structures involves understanding the organization, management, and storage formats of data for efficient access and modification. This foundational topic is essential for programming and algorithm development, as it underpins how data is manipulated in software applications.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic mathematics</strong></td>
                    <td>Basic mathematics encompasses fundamental concepts such as arithmetic operations, number theory, and basic algebra, which serve as the foundation for more advanced mathematical studies. It is essential for everyday problem-solving and logical reasoning.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic programming concepts</strong></td>
                    <td>Basic programming concepts encompass fundamental principles and constructs used in programming, such as variables, data types, control structures, and functions. These concepts serve as the foundation for writing and understanding code in various programming languages.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Communication skills</strong></td>
                    <td>Communication skills encompass the ability to convey information effectively and efficiently through verbal, non-verbal, and written means. Mastering these skills is essential for fostering understanding and collaboration in both personal and professional contexts.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Computer Science</strong></td>
                    <td>The study of algorithmic processes and computational systems.</td>
                    <td><span style="color: blue;">Mathematics, Logic</span></td>
                    <td><span style="color: red;">Discrete Mathematics</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Control structures (if statements, loops)</strong></td>
                    <td>Control structures are essential programming constructs that allow developers to dictate the flow of execution in a program based on certain conditions (if statements) or to repeat a block of code multiple times (loops). Mastery of these structures is crucial for implementing logic and managing program behavior effectively.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Critical thinking</strong></td>
                    <td>Critical thinking is the ability to analyze, evaluate, and synthesize information in a logical and systematic manner, enabling individuals to make reasoned judgments and solve problems effectively. It involves questioning assumptions, recognizing biases, and assessing the validity of arguments.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Data Analysis</strong></td>
                    <td>Data Analysis is the process of inspecting, cleansing, transforming, and modeling data to discover useful information, inform conclusions, and support decision-making. It encompasses various techniques and tools to interpret data effectively and derive insights.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Data Structures</strong></td>
                    <td>Data Structures are specialized formats for organizing, processing, and storing data in a computer, which enable efficient data manipulation and retrieval. Understanding data structures is essential for optimizing algorithms and solving complex computational problems.</td>
                    <td><span style="color: blue;">Computer Science, Programming Fundamentals, Mathematics</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Data Visualization</strong></td>
                    <td>Data Visualization is the graphical representation of information and data, utilizing visual elements like charts, graphs, and maps to communicate complex data insights effectively. It enables users to identify patterns, trends, and outliers in data through visual contexts.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Data types and variables</strong></td>
                    <td>Data types and variables are fundamental concepts in programming that define the type of data a variable can hold and how that data can be manipulated. Understanding these concepts is essential for effective coding and data management in any programming language.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Derivatives</strong></td>
                    <td>A measure of how a function changes as its input changes.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Exposure to software development principles</strong></td>
                    <td>Exposure to software development principles involves understanding the foundational concepts and methodologies that guide the creation and maintenance of software applications. This includes knowledge of best practices, design patterns, and the software development lifecycle.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Familiarity with computer systems</strong></td>
                    <td>Familiarity with computer systems refers to the understanding and ability to navigate, operate, and troubleshoot various hardware and software components of computers. This knowledge is essential for effectively utilizing technology in both personal and professional settings.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Familiarity with mathematical concepts</strong></td>
                    <td>Familiarity with mathematical concepts refers to the understanding and recognition of fundamental mathematical ideas, principles, and operations that form the foundation for more advanced mathematical reasoning and problem-solving. This familiarity enables individuals to apply mathematical thinking in various contexts, including academic, professional, and everyday situations.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Familiarity with syntax and semantics of a programming language</strong></td>
                    <td>This topic encompasses the understanding of the rules that govern the structure of a programming language (syntax) and the meaning of its constructs (semantics). Mastery of both aspects is essential for effective programming and code comprehension.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Functions</strong></td>
                    <td>Mathematical relations that assign exactly one output to each input.</td>
                    <td><span style="color: blue;">Mathematics</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Functions and Graphs</strong></td>
                    <td>Functions and Graphs is a mathematical topic that explores the relationship between inputs and outputs through functions, and their visual representation on a coordinate plane. Understanding this topic is essential for analyzing patterns, solving equations, and interpreting data in various fields of study.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Geometry</strong></td>
                    <td>Geometry is a branch of mathematics that studies the properties and relationships of points, lines, surfaces, and solids. It involves the exploration of shapes, sizes, and the relative position of figures in space.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Introduction to Machine Learning</strong></td>
                    <td>Introduction to Machine Learning is a foundational course that explores the principles and techniques used to develop algorithms that enable computers to learn from and make predictions based on data. It covers key concepts such as supervised and unsupervised learning, model evaluation, and the importance of data preprocessing.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Introduction to algorithms</strong></td>
                    <td>Introduction to algorithms is a foundational course that covers the design, analysis, and implementation of algorithms, focusing on their efficiency and effectiveness in solving computational problems. It serves as a critical building block for computer science and software engineering disciplines.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Introduction to algorithms and problem-solving techniques</strong></td>
                    <td>This topic covers the fundamental concepts of algorithms, including their design, analysis, and implementation, as well as various problem-solving strategies used in computer science. It aims to equip students with the skills to approach complex problems systematically and develop efficient solutions.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Knowledge of mathematical operations</strong></td>
                    <td>Knowledge of mathematical operations encompasses the understanding and application of basic arithmetic processes, including addition, subtraction, multiplication, and division, which are foundational for more complex mathematical concepts. Mastery of these operations is essential for problem-solving and critical thinking in various academic and real-world contexts.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Knowledge of problem-solving techniques</strong></td>
                    <td>Knowledge of problem-solving techniques encompasses the understanding and application of various strategies and methods to identify, analyze, and resolve issues effectively. This knowledge is essential for enhancing critical thinking and decision-making skills across various disciplines.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Knowledge of syntax and semantics of a programming language</strong></td>
                    <td>This topic encompasses the understanding of the rules that govern the structure of statements in a programming language (syntax) and the meaning behind those statements (semantics). Mastery of both aspects is crucial for effective programming and software development.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Limits</strong></td>
                    <td>A fundamental concept in calculus and analysis concerning the behavior of a function near a particular input.</td>
                    <td><span style="color: blue;">Mathematics, Functions</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Logical reasoning</strong></td>
                    <td>Logical reasoning is the process of using structured, systematic thinking to evaluate arguments and draw conclusions based on premises. It involves the ability to analyze relationships between statements and assess the validity of inferences.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Mathematical Reasoning</strong></td>
                    <td>Mathematical reasoning involves the process of using logical thinking and mathematical concepts to solve problems, make deductions, and construct valid arguments. It is essential for understanding proofs, developing algorithms, and applying mathematics in various fields.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Mathematics</strong></td>
                    <td>The abstract science of number, quantity, and space.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Probability Theory</strong></td>
                    <td>The branch of mathematics concerned with probability.</td>
                    <td><span style="color: blue;">Mathematics, Set Theory</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Problem-solving skills</strong></td>
                    <td>Problem-solving skills refer to the ability to identify, analyze, and resolve challenges effectively and efficiently. These skills encompass critical thinking, creativity, and logical reasoning to devise solutions in various contexts.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Trigonometry</strong></td>
                    <td>Trigonometry is a branch of mathematics that studies the relationships between the angles and sides of triangles, particularly right triangles. It provides essential tools for understanding periodic phenomena and is widely used in various fields such as physics, engineering, and computer science.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Understanding of algorithms</strong></td>
                    <td>Understanding algorithms involves grasping the step-by-step procedures or formulas for solving problems and performing computations. It encompasses the study of algorithm design, analysis, and optimization to improve efficiency and effectiveness in problem-solving.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Understanding of algorithms and data structures</strong></td>
                    <td>This topic encompasses the study of algorithms, which are step-by-step procedures for solving problems, and data structures, which are ways to organize and store data efficiently. Mastery of this topic is essential for effective programming and software development, as it enables the selection of appropriate algorithms and data structures to optimize performance and resource usage.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Understanding of computer systems</strong></td>
                    <td>This topic encompasses the fundamental principles and components of computer systems, including hardware, software, and their interactions. It provides a foundational knowledge necessary for analyzing how computers process data and execute instructions.</td>
                    <td><span style="color: blue;">None</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Vector Spaces</strong></td>
                    <td>A collection of objects called vectors that can be added together and multiplied by numbers.</td>
                    <td><span style="color: blue;">Linear Algebra, Mathematics</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Logic</strong></td>
                    <td>The systematic study of the principles of valid inference and correct reasoning.</td>
                    <td><span style="color: blue;">Mathematics</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Set Theory</strong></td>
                    <td>The branch of mathematical logic that studies sets.</td>
                    <td><span style="color: blue;">Mathematics, Logic</span></td>
                    <td><span style="color: red;">None</span></td>
                </tr>
                
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function toggleExpand() {
            const container = document.getElementById('graphContainer');
            const overlay = document.getElementById('overlay');
            const expandBtn = document.getElementById('expandBtn');

            if (container.classList.contains('expanded')) {
                container.classList.remove('expanded');
                overlay.classList.remove('active');
                expandBtn.innerHTML = '🔍 Expand';
                document.body.style.overflow = 'auto';

                // Resize network after collapse
                setTimeout(() => {
                    if (window.network && window.network.redraw) {
                        window.network.redraw();
                        window.network.fit();
                    }
                }, 300);
            } else {
                container.classList.add('expanded');
                overlay.classList.add('active');
                expandBtn.innerHTML = '❌ Close';
                document.body.style.overflow = 'hidden';

                // Resize network after expansion
                setTimeout(() => {
                    if (window.network && window.network.redraw) {
                        window.network.redraw();
                        window.network.fit();
                    }
                }, 300);
            }
        }

        function resetGraph() {
            // Try to reset the network view if vis.js methods are available
            if (window.network && window.network.fit) {
                window.network.fit();
            }
        }

        // Close expanded view with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const container = document.getElementById('graphContainer');
                if (container.classList.contains('expanded')) {
                    toggleExpand();
                }
            }
        });
    </script>

    <script src="lib/bindings/utils.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>
<script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>
<script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "black"}, "id": "Machine Learning", "label": "Machine Learning", "shape": "dot", "size": 45, "title": "Depth: 0\nDescription: A field of artificial intelligence that uses statistical techniques to give computer systems the ability to learn."}, {"color": "#ff6b6b", "font": {"color": "black"}, "id": "Python Programming", "label": "Python Programming", "shape": "dot", "size": 45, "title": "Depth: 0\nDescription: Error fetching description for Python Programming"}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Basic problem-solving skills", "label": "Basic problem-solving skills", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Basic problem-solving skills involve the ability to identify, analyze, and resolve issues effectively using logical reasoning and critical thinking. These skills are essential for making informed decisions and navigating everyday challenges."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Basic understanding of programming concepts", "label": "Basic understanding of programming concepts", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: This topic encompasses the foundational principles of programming, including variables, data types, control structures, and algorithms. It serves as an essential introduction for individuals seeking to develop their coding skills and understand how software operates."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Calculus", "label": "Calculus", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The mathematical study of continuous change."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Data Preprocessing", "label": "Data Preprocessing", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Data Preprocessing is the process of transforming raw data into a clean and usable format for analysis, which includes tasks such as data cleaning, normalization, and feature selection. It is a crucial step in the data analysis pipeline that ensures the quality and relevance of the data used in machine learning models."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Familiarity with data types and variables", "label": "Familiarity with data types and variables", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Familiarity with data types and variables involves understanding the different kinds of data that can be stored in programming languages and how these data types can be manipulated through variables. This foundational knowledge is essential for effective programming and data management."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Knowledge of control structures (if statements, loops)", "label": "Knowledge of control structures (if statements, loops)", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Control structures are fundamental programming constructs that dictate the flow of execution in a program. Understanding if statements and loops is essential for implementing decision-making and repetitive tasks in coding."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Linear Algebra", "label": "Linear Algebra", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The branch of mathematics concerning linear equations and linear functions."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Programming", "label": "Programming", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The process of creating a set of instructions that tell a computer how to perform a task."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Programming (Python or R)", "label": "Programming (Python or R)", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Programming in Python or R involves writing code to create software applications, analyze data, and automate tasks using these high-level programming languages. Both languages are widely used in data science, web development, and scientific computing due to their simplicity and extensive libraries."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Programming Fundamentals", "label": "Programming Fundamentals", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Basic concepts of computer programming including variables, control structures, and algorithms."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Statistics", "label": "Statistics", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: The discipline that concerns the collection, organization, analysis, interpretation, and presentation of data."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Understanding of functions and modular programming", "label": "Understanding of functions and modular programming", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: This topic encompasses the principles of defining and using functions to encapsulate code, as well as the organization of code into modules to promote reusability and maintainability in programming. It is essential for developing structured and efficient software applications."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Algebra", "label": "Algebra", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Algebra is a branch of mathematics that deals with symbols and the rules for manipulating those symbols to solve equations and understand relationships between quantities. It serves as a foundational tool for higher-level mathematics and various applications in science and engineering."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Algorithms", "label": "Algorithms", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: A finite sequence of well-defined instructions for solving a problem."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Analytical skills", "label": "Analytical skills", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Analytical skills refer to the ability to systematically and critically evaluate information, identify patterns, and solve problems through logical reasoning. These skills are essential for decision-making and are applicable across various disciplines, including science, mathematics, and social studies."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic Algebra", "label": "Basic Algebra", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic Algebra is the branch of mathematics that deals with the manipulation of symbols and the rules for solving equations, focusing on operations with variables and constants. It serves as a foundational skill for higher-level mathematics and various applications in science and engineering."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic Statistics", "label": "Basic Statistics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic Statistics involves the collection, analysis, interpretation, presentation, and organization of data. It provides foundational tools for understanding data patterns and making informed decisions based on quantitative information."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic computer literacy", "label": "Basic computer literacy", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic computer literacy refers to the essential skills and knowledge required to effectively use computers and technology, including understanding hardware, software, and basic troubleshooting. It encompasses the ability to navigate operating systems, utilize applications, and engage with the internet safely and responsibly."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic input/output operations", "label": "Basic input/output operations", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic input/output operations refer to the fundamental processes through which a computer system receives data (input) and sends data (output) to various devices, enabling interaction with users and other systems. These operations are essential for understanding how data is processed and communicated within software applications."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic knowledge of data structures", "label": "Basic knowledge of data structures", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic knowledge of data structures involves understanding the organization, management, and storage formats of data for efficient access and modification. This foundational topic is essential for programming and algorithm development, as it underpins how data is manipulated in software applications."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic mathematics", "label": "Basic mathematics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic mathematics encompasses fundamental concepts such as arithmetic operations, number theory, and basic algebra, which serve as the foundation for more advanced mathematical studies. It is essential for everyday problem-solving and logical reasoning."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic programming concepts", "label": "Basic programming concepts", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic programming concepts encompass fundamental principles and constructs used in programming, such as variables, data types, control structures, and functions. These concepts serve as the foundation for writing and understanding code in various programming languages."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Communication skills", "label": "Communication skills", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Communication skills encompass the ability to convey information effectively and efficiently through verbal, non-verbal, and written means. Mastering these skills is essential for fostering understanding and collaboration in both personal and professional contexts."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Computer Science", "label": "Computer Science", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: The study of algorithmic processes and computational systems."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Control structures (if statements, loops)", "label": "Control structures (if statements, loops)", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Control structures are essential programming constructs that allow developers to dictate the flow of execution in a program based on certain conditions (if statements) or to repeat a block of code multiple times (loops). Mastery of these structures is crucial for implementing logic and managing program behavior effectively."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Critical thinking", "label": "Critical thinking", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Critical thinking is the ability to analyze, evaluate, and synthesize information in a logical and systematic manner, enabling individuals to make reasoned judgments and solve problems effectively. It involves questioning assumptions, recognizing biases, and assessing the validity of arguments."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Data Analysis", "label": "Data Analysis", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Data Analysis is the process of inspecting, cleansing, transforming, and modeling data to discover useful information, inform conclusions, and support decision-making. It encompasses various techniques and tools to interpret data effectively and derive insights."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Data Structures", "label": "Data Structures", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Data Structures are specialized formats for organizing, processing, and storing data in a computer, which enable efficient data manipulation and retrieval. Understanding data structures is essential for optimizing algorithms and solving complex computational problems."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Data Visualization", "label": "Data Visualization", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Data Visualization is the graphical representation of information and data, utilizing visual elements like charts, graphs, and maps to communicate complex data insights effectively. It enables users to identify patterns, trends, and outliers in data through visual contexts."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Data types and variables", "label": "Data types and variables", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Data types and variables are fundamental concepts in programming that define the type of data a variable can hold and how that data can be manipulated. Understanding these concepts is essential for effective coding and data management in any programming language."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Derivatives", "label": "Derivatives", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: A measure of how a function changes as its input changes."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Exposure to software development principles", "label": "Exposure to software development principles", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Exposure to software development principles involves understanding the foundational concepts and methodologies that guide the creation and maintenance of software applications. This includes knowledge of best practices, design patterns, and the software development lifecycle."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Familiarity with computer systems", "label": "Familiarity with computer systems", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Familiarity with computer systems refers to the understanding and ability to navigate, operate, and troubleshoot various hardware and software components of computers. This knowledge is essential for effectively utilizing technology in both personal and professional settings."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Familiarity with mathematical concepts", "label": "Familiarity with mathematical concepts", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Familiarity with mathematical concepts refers to the understanding and recognition of fundamental mathematical ideas, principles, and operations that form the foundation for more advanced mathematical reasoning and problem-solving. This familiarity enables individuals to apply mathematical thinking in various contexts, including academic, professional, and everyday situations."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Familiarity with syntax and semantics of a programming language", "label": "Familiarity with syntax and semantics of a programming language", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: This topic encompasses the understanding of the rules that govern the structure of a programming language (syntax) and the meaning of its constructs (semantics). Mastery of both aspects is essential for effective programming and code comprehension."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Functions", "label": "Functions", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Mathematical relations that assign exactly one output to each input."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Functions and Graphs", "label": "Functions and Graphs", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Functions and Graphs is a mathematical topic that explores the relationship between inputs and outputs through functions, and their visual representation on a coordinate plane. Understanding this topic is essential for analyzing patterns, solving equations, and interpreting data in various fields of study."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Geometry", "label": "Geometry", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Geometry is a branch of mathematics that studies the properties and relationships of points, lines, surfaces, and solids. It involves the exploration of shapes, sizes, and the relative position of figures in space."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Introduction to Machine Learning", "label": "Introduction to Machine Learning", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Introduction to Machine Learning is a foundational course that explores the principles and techniques used to develop algorithms that enable computers to learn from and make predictions based on data. It covers key concepts such as supervised and unsupervised learning, model evaluation, and the importance of data preprocessing."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Introduction to algorithms", "label": "Introduction to algorithms", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Introduction to algorithms is a foundational course that covers the design, analysis, and implementation of algorithms, focusing on their efficiency and effectiveness in solving computational problems. It serves as a critical building block for computer science and software engineering disciplines."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Introduction to algorithms and problem-solving techniques", "label": "Introduction to algorithms and problem-solving techniques", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: This topic covers the fundamental concepts of algorithms, including their design, analysis, and implementation, as well as various problem-solving strategies used in computer science. It aims to equip students with the skills to approach complex problems systematically and develop efficient solutions."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Knowledge of mathematical operations", "label": "Knowledge of mathematical operations", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Knowledge of mathematical operations encompasses the understanding and application of basic arithmetic processes, including addition, subtraction, multiplication, and division, which are foundational for more complex mathematical concepts. Mastery of these operations is essential for problem-solving and critical thinking in various academic and real-world contexts."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Knowledge of problem-solving techniques", "label": "Knowledge of problem-solving techniques", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Knowledge of problem-solving techniques encompasses the understanding and application of various strategies and methods to identify, analyze, and resolve issues effectively. This knowledge is essential for enhancing critical thinking and decision-making skills across various disciplines."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Knowledge of syntax and semantics of a programming language", "label": "Knowledge of syntax and semantics of a programming language", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: This topic encompasses the understanding of the rules that govern the structure of statements in a programming language (syntax) and the meaning behind those statements (semantics). Mastery of both aspects is crucial for effective programming and software development."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Limits", "label": "Limits", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: A fundamental concept in calculus and analysis concerning the behavior of a function near a particular input."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Logical reasoning", "label": "Logical reasoning", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Logical reasoning is the process of using structured, systematic thinking to evaluate arguments and draw conclusions based on premises. It involves the ability to analyze relationships between statements and assess the validity of inferences."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Mathematical Reasoning", "label": "Mathematical Reasoning", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Mathematical reasoning involves the process of using logical thinking and mathematical concepts to solve problems, make deductions, and construct valid arguments. It is essential for understanding proofs, developing algorithms, and applying mathematics in various fields."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Mathematics", "label": "Mathematics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: The abstract science of number, quantity, and space."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Probability Theory", "label": "Probability Theory", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: The branch of mathematics concerned with probability."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Problem-solving skills", "label": "Problem-solving skills", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Problem-solving skills refer to the ability to identify, analyze, and resolve challenges effectively and efficiently. These skills encompass critical thinking, creativity, and logical reasoning to devise solutions in various contexts."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Trigonometry", "label": "Trigonometry", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Trigonometry is a branch of mathematics that studies the relationships between the angles and sides of triangles, particularly right triangles. It provides essential tools for understanding periodic phenomena and is widely used in various fields such as physics, engineering, and computer science."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Understanding of algorithms", "label": "Understanding of algorithms", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Understanding algorithms involves grasping the step-by-step procedures or formulas for solving problems and performing computations. It encompasses the study of algorithm design, analysis, and optimization to improve efficiency and effectiveness in problem-solving."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Understanding of algorithms and data structures", "label": "Understanding of algorithms and data structures", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: This topic encompasses the study of algorithms, which are step-by-step procedures for solving problems, and data structures, which are ways to organize and store data efficiently. Mastery of this topic is essential for effective programming and software development, as it enables the selection of appropriate algorithms and data structures to optimize performance and resource usage."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Understanding of computer systems", "label": "Understanding of computer systems", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: This topic encompasses the fundamental principles and components of computer systems, including hardware, software, and their interactions. It provides a foundational knowledge necessary for analyzing how computers process data and execute instructions."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Vector Spaces", "label": "Vector Spaces", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: A collection of objects called vectors that can be added together and multiplied by numbers."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Logic", "label": "Logic", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: The systematic study of the principles of valid inference and correct reasoning."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Set Theory", "label": "Set Theory", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: The branch of mathematical logic that studies sets."}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Programming Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Python Programming", "to": "Programming Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Python Programming", "to": "Computer Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Python Programming", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Python Programming", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Limits", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Vector Spaces", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Fundamentals", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Fundamentals", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Fundamentals", "to": "Computer Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Computer Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Data Structures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Computer Science", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Computer Science", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Structures", "to": "Computer Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Structures", "to": "Programming Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Structures", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Spaces", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Spaces", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Logic", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 100}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
</body>
</html>
        