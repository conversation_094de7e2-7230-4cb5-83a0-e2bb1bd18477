import sqlite3
import json

conn = sqlite3.connect('topics.db')
cursor = conn.execute('SELECT term, depth, prerequisites FROM topics WHERE term = "Machine Learning"')
result = cursor.fetchone()
if result:
    term, depth, prereqs = result
    print(f'Machine Learning (depth {depth}) prerequisites: {json.loads(prereqs)}')
else:
    print('Machine Learning not found')

# Check all depth 1 terms for parent dependencies
print('\nChecking for parent dependencies:')
cursor = conn.execute('SELECT term, depth, prerequisites FROM topics WHERE depth = 1')
depth1_terms = []
for term, depth, prereqs in cursor.fetchall():
    depth1_terms.append(term)
    prereq_list = json.loads(prereqs)
    parent_deps = [p for p in prereq_list if p == 'Data Science']
    if parent_deps:
        print(f'{term} has parent dependency: {parent_deps}')

# Check for same-depth dependencies
print('\nChecking for same-depth dependencies at depth 1:')
cursor = conn.execute('SELECT term, depth, prerequisites FROM topics WHERE depth = 1')
for term, depth, prereqs in cursor.fetchall():
    prereq_list = json.loads(prereqs)
    same_depth_deps = [p for p in prereq_list if p in depth1_terms]
    if same_depth_deps:
        print(f'{term} has same-depth dependencies: {same_depth_deps}')

if not any(parent_deps for _, _, prereqs in cursor.fetchall() for parent_deps in [[p for p in json.loads(prereqs) if p == 'Data Science']]):
    print('✅ No parent dependencies found!')

# Check all terms for their prerequisites
print('\nAll depth 1 terms and their prerequisites:')
cursor = conn.execute('SELECT term, prerequisites FROM topics WHERE depth = 1 ORDER BY term')
for term, prereqs in cursor.fetchall():
    print(f'{term}: {json.loads(prereqs)}')

conn.close()
