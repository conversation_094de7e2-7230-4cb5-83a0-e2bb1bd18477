#!/usr/bin/env python3
"""
Topic Tree Builder and Visualizer

A Python application that builds a hierarchical topic dependency tree
and visualizes it as an interactive graph.
"""

import sqlite3
import json
import argparse
import sys
from typing import List, Tuple, Optional
from collections import deque
import logging

# Third-party imports
try:
    from pyvis.network import Network
except ImportError:
    print("Error: pyvis not installed. Run: pip install -r requirements.txt")
    sys.exit(1)


class TopicTreeBuilder:
    """Builds and manages a topic dependency tree using SQLite database."""
    
    def __init__(self, db_path: str = "topics.db"):
        """Initialize the topic tree builder with database connection."""
        self.db_path = db_path
        self.conn = None
        self._setup_logging()
        self._setup_database()
    
    def _setup_logging(self):
        """Configure logging for the application."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def _setup_database(self):
        """Initialize SQLite database and create topics table if not exists."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.execute("PRAGMA foreign_keys = ON")
            
            # Create topics table with specified schema
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS topics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                depth INTEGER NOT NULL,
                term TEXT UNIQUE NOT NULL,
                description TEXT,
                unseen_dependent_terms TEXT DEFAULT '[]',
                seen_dependent_terms TEXT DEFAULT '[]'
            )
            """
            self.conn.execute(create_table_sql)
            self.conn.commit()
            self.logger.info(f"Database initialized at {self.db_path}")
            
        except sqlite3.Error as e:
            self.logger.error(f"Database setup error: {e}")
            raise
    
    def fetch_term_info(self, term: str) -> Tuple[str, List[str]]:
        """
        STUB FUNCTION - Replace with actual implementation.
        
        This function should fetch information about a term from an external source
        (API, knowledge base, etc.) and return its description and dependent terms.
        
        Args:
            term: The term to fetch information for
            
        Returns:
            Tuple of (description, list_of_dependent_terms)
            
        TODO: Replace this stub with actual implementation that:
        - Calls an external API or knowledge base
        - Handles rate limiting and errors
        - Returns real term descriptions and dependencies
        """
        # STUB IMPLEMENTATION - Replace with real data source
        stub_data = {
            "Machine Learning": (
                "A field of artificial intelligence that uses statistical techniques to give computer systems the ability to learn.",
                ["Statistics", "Linear Algebra", "Calculus", "Programming"]
            ),
            "Statistics": (
                "The discipline that concerns the collection, organization, analysis, interpretation, and presentation of data.",
                ["Probability Theory", "Mathematics"]
            ),
            "Linear Algebra": (
                "The branch of mathematics concerning linear equations and linear functions.",
                ["Mathematics", "Vector Spaces"]
            ),
            "Calculus": (
                "The mathematical study of continuous change.",
                ["Mathematics", "Limits", "Derivatives"]
            ),
            "Programming": (
                "The process of creating a set of instructions that tell a computer how to perform a task.",
                ["Computer Science", "Algorithms"]
            ),
            "Probability Theory": (
                "The branch of mathematics concerned with probability.",
                ["Mathematics", "Set Theory"]
            ),
            "Mathematics": (
                "The abstract science of number, quantity, and space.",
                []
            ),
            "Vector Spaces": (
                "A collection of objects called vectors that can be added together and multiplied by numbers.",
                ["Mathematics", "Linear Algebra"]
            ),
            "Limits": (
                "A fundamental concept in calculus and analysis concerning the behavior of a function near a particular input.",
                ["Mathematics"]
            ),
            "Derivatives": (
                "A measure of how a function changes as its input changes.",
                ["Calculus", "Limits"]
            ),
            "Computer Science": (
                "The study of algorithmic processes and computational systems.",
                ["Mathematics", "Logic"]
            ),
            "Algorithms": (
                "A finite sequence of well-defined instructions for solving a problem.",
                ["Computer Science", "Mathematics"]
            ),
            "Set Theory": (
                "The branch of mathematical logic that studies sets.",
                ["Mathematics", "Logic"]
            ),
            "Logic": (
                "The systematic study of the principles of valid inference and correct reasoning.",
                ["Mathematics"]
            )
        }
        
        if term in stub_data:
            description, dependencies = stub_data[term]
            self.logger.info(f"Fetched info for '{term}': {len(dependencies)} dependencies")
            return description, dependencies
        else:
            # Return generic info for unknown terms
            self.logger.warning(f"No stub data for '{term}', returning generic info")
            return f"Description for {term} (placeholder)", []
    
    def term_exists(self, term: str) -> bool:
        """Check if a term already exists in the database."""
        try:
            cursor = self.conn.execute(
                "SELECT COUNT(*) FROM topics WHERE term = ?", (term,)
            )
            count = cursor.fetchone()[0]
            return count > 0
        except sqlite3.Error as e:
            self.logger.error(f"Error checking term existence: {e}")
            return False
    
    def insert_term(self, term: str, depth: int, description: str = None) -> bool:
        """
        Insert a new term into the database.
        
        Args:
            term: The term to insert
            depth: The depth level of the term
            description: Optional description of the term
            
        Returns:
            True if insertion successful, False otherwise
        """
        try:
            self.conn.execute(
                """INSERT INTO topics (term, depth, description, unseen_dependent_terms, seen_dependent_terms)
                   VALUES (?, ?, ?, '[]', '[]')""",
                (term, depth, description)
            )
            self.conn.commit()
            self.logger.info(f"Inserted term '{term}' at depth {depth}")
            return True
        except sqlite3.IntegrityError:
            self.logger.warning(f"Term '{term}' already exists, skipping insertion")
            return False
        except sqlite3.Error as e:
            self.logger.error(f"Error inserting term '{term}': {e}")
            return False

    def update_term_dependencies(self, term: str, seen_deps: List[str], unseen_deps: List[str]):
        """
        Update the dependency lists for a term.

        Args:
            term: The term to update
            seen_deps: List of dependencies that exist in the database
            unseen_deps: List of dependencies not yet in the database
        """
        try:
            self.conn.execute(
                """UPDATE topics
                   SET seen_dependent_terms = ?, unseen_dependent_terms = ?
                   WHERE term = ?""",
                (json.dumps(seen_deps), json.dumps(unseen_deps), term)
            )
            self.conn.commit()
            self.logger.info(f"Updated dependencies for '{term}': {len(seen_deps)} seen, {len(unseen_deps)} unseen")
        except sqlite3.Error as e:
            self.logger.error(f"Error updating dependencies for '{term}': {e}")

    def get_terms_at_depth(self, depth: int) -> List[str]:
        """Get all terms at a specific depth level."""
        try:
            cursor = self.conn.execute(
                "SELECT term FROM topics WHERE depth = ?", (depth,)
            )
            terms = [row[0] for row in cursor.fetchall()]
            return terms
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching terms at depth {depth}: {e}")
            return []

    def build_topic_tree(self, root_term: str, max_depth: int = 5):
        """
        Build the topic tree starting from a root term.

        Args:
            root_term: The starting term for the tree
            max_depth: Maximum depth to traverse (default: 5, so depths 0-5)
        """
        self.logger.info(f"Building topic tree starting from '{root_term}'")

        # Insert root term if it doesn't exist
        if not self.term_exists(root_term):
            description, _ = self.fetch_term_info(root_term)
            self.insert_term(root_term, 0, description)

        # Process terms level by level (breadth-first)
        for current_depth in range(max_depth + 1):
            terms_at_depth = self.get_terms_at_depth(current_depth)

            if not terms_at_depth:
                self.logger.info(f"No terms at depth {current_depth}, stopping")
                break

            self.logger.info(f"Processing {len(terms_at_depth)} terms at depth {current_depth}")

            for term in terms_at_depth:
                self._process_term(term, current_depth, max_depth)

        self.logger.info("Topic tree building completed")

    def _process_term(self, term: str, current_depth: int, max_depth: int):
        """
        Process a single term: fetch its dependencies and update the database.

        Args:
            term: The term to process
            current_depth: Current depth of the term
            max_depth: Maximum allowed depth
        """
        try:
            # Fetch term information
            description, dependent_terms = self.fetch_term_info(term)

            # Split dependencies into seen and unseen
            seen_deps = []
            unseen_deps = []

            for dep_term in dependent_terms:
                if self.term_exists(dep_term):
                    seen_deps.append(dep_term)
                else:
                    unseen_deps.append(dep_term)
                    # Insert new term if we haven't reached max depth
                    if current_depth < max_depth:
                        dep_description, _ = self.fetch_term_info(dep_term)
                        self.insert_term(dep_term, current_depth + 1, dep_description)

            # Update the term's dependency lists
            self.update_term_dependencies(term, seen_deps, unseen_deps)

        except Exception as e:
            self.logger.error(f"Error processing term '{term}': {e}")

    def get_all_topics(self) -> List[Tuple]:
        """Get all topics from the database."""
        try:
            cursor = self.conn.execute(
                """SELECT id, depth, term, description,
                          seen_dependent_terms, unseen_dependent_terms
                   FROM topics ORDER BY depth, term"""
            )
            return cursor.fetchall()
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching all topics: {e}")
            return []

    def generate_graph_visualization(self, output_file: str = "graph.html"):
        """
        Generate an interactive HTML graph visualization of the topic tree.

        Args:
            output_file: Path to the output HTML file
        """
        self.logger.info(f"Generating graph visualization: {output_file}")

        # Create pyvis network
        net = Network(
            height="600px",
            width="100%",
            bgcolor="#ffffff",
            font_color="black",
            directed=True
        )

        # Configure physics for better layout
        net.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "stabilization": {"iterations": 100}
          }
        }
        """)

        # Get all topics from database
        topics = self.get_all_topics()

        if not topics:
            self.logger.warning("No topics found in database")
            return

        # Color scheme for different depths
        depth_colors = {
            0: "#ff6b6b",  # Red for root
            1: "#4ecdc4",  # Teal for depth 1
            2: "#45b7d1",  # Blue for depth 2
            3: "#96ceb4",  # Green for depth 3
            4: "#feca57",  # Yellow for depth 4
            5: "#ff9ff3",  # Pink for depth 5
        }

        # Add nodes
        for topic_id, depth, term, description, seen_deps_json, unseen_deps_json in topics:
            color = depth_colors.get(depth, "#cccccc")

            # Create node with hover information
            title = f"Depth: {depth}\nDescription: {description or 'No description'}"

            net.add_node(
                term,
                label=term,
                color=color,
                title=title,
                size=20 + (5 - depth) * 5  # Larger nodes for shallower depths
            )

        # Add edges (dependencies)
        for topic_id, depth, term, description, seen_deps_json, unseen_deps_json in topics:
            try:
                seen_deps = json.loads(seen_deps_json) if seen_deps_json else []
                unseen_deps = json.loads(unseen_deps_json) if unseen_deps_json else []

                # Add edges to seen dependencies
                for dep in seen_deps:
                    net.add_edge(term, dep, color="blue", width=2)

                # Add edges to unseen dependencies (if they exist as nodes)
                for dep in unseen_deps:
                    if any(t[2] == dep for t in topics):  # Check if dependency exists as node
                        net.add_edge(term, dep, color="red", width=1, dashes=True)

            except json.JSONDecodeError as e:
                self.logger.error(f"Error parsing dependencies for '{term}': {e}")

        # Generate and save the HTML file
        try:
            # Use write_html instead of show for better compatibility
            net.write_html(output_file)
            self.logger.info(f"Graph visualization saved to {output_file}")
        except Exception as e:
            self.logger.error(f"Error generating visualization: {e}")
            # Fallback: try the show method
            try:
                net.show(output_file)
                self.logger.info(f"Graph visualization saved to {output_file} (using fallback method)")
            except Exception as e2:
                self.logger.error(f"Fallback method also failed: {e2}")

    def close(self):
        """Close the database connection."""
        if self.conn:
            self.conn.close()
            self.logger.info("Database connection closed")


def main():
    """Main CLI interface for the topic tree builder."""
    parser = argparse.ArgumentParser(
        description="Build and visualize a topic dependency tree",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python topic_graph.py --root "Machine Learning"
  python topic_graph.py --root "Statistics" --max-depth 3
  python topic_graph.py --root "Programming" --output custom_graph.html
        """
    )

    parser.add_argument(
        "--root", "-r",
        required=True,
        help="Root term to start building the topic tree from"
    )

    parser.add_argument(
        "--max-depth", "-d",
        type=int,
        default=5,
        help="Maximum depth to traverse (default: 5, meaning depths 0-5)"
    )

    parser.add_argument(
        "--output", "-o",
        default="graph.html",
        help="Output HTML file for the graph visualization (default: graph.html)"
    )

    parser.add_argument(
        "--db-path",
        default="topics.db",
        help="Path to the SQLite database file (default: topics.db)"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create and run the topic tree builder
    builder = None
    try:
        print(f"Building topic tree starting from: {args.root}")
        print(f"Maximum depth: {args.max_depth}")
        print(f"Database: {args.db_path}")
        print(f"Output: {args.output}")
        print("-" * 50)

        builder = TopicTreeBuilder(args.db_path)
        builder.build_topic_tree(args.root, args.max_depth)
        builder.generate_graph_visualization(args.output)

        print("-" * 50)
        print(f"✅ Topic tree building completed!")
        print(f"📊 Graph visualization saved to: {args.output}")
        print(f"💾 Database saved to: {args.db_path}")

    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
    finally:
        if builder:
            builder.close()


if __name__ == "__main__":
    main()
