#!/usr/bin/env python3
"""
Topic Tree Builder and Visualizer

A Python application that builds a hierarchical topic dependency tree
and visualizes it as an interactive graph.
"""

import sqlite3
import json
import argparse
import sys
import tempfile
import os
import re
import time
from typing import List, Tuple, Optional, Dict, Any
import logging

# Third-party imports
try:
    from pyvis.network import Network
except ImportError:
    print("Error: pyvis not installed. Run: pip install -r requirements.txt")
    sys.exit(1)

try:
    import openai
except ImportError:
    print("Error: openai not installed. Run: pip install -r requirements.txt")
    sys.exit(1)


class TopicTreeBuilder:
    """Builds and manages a topic dependency tree using SQLite database."""

    def __init__(self, db_path: str = "topics.db", fallback_only: bool = False):
        """Initialize the topic tree builder with database connection."""
        self.db_path = db_path
        self.conn = None
        self.fallback_only = fallback_only
        self.response_cache: Dict[str, Tuple[str, List[str]]] = {}
        self.api_call_count = 0
        self.cache_hits = 0
        self._setup_logging()
        if not fallback_only:
            self._setup_openai()
        self._setup_database()
    
    def _setup_logging(self):
        """Configure logging for the application."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def _setup_openai(self):
        """Initialize OpenAI client with API key from environment."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.error("OPENAI_API_KEY environment variable not set")
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable.")

        self.openai_client = openai.OpenAI(api_key=api_key)
        self.logger.info("OpenAI client initialized successfully")
    
    def _setup_database(self):
        """Initialize SQLite database and create topics table if not exists."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.execute("PRAGMA foreign_keys = ON")
            
            # Create topics table with specified schema
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS topics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                depth INTEGER NOT NULL,
                term TEXT UNIQUE NOT NULL,
                description TEXT,
                unseen_dependent_terms TEXT DEFAULT '[]',
                seen_dependent_terms TEXT DEFAULT '[]'
            )
            """
            self.conn.execute(create_table_sql)
            self.conn.commit()
            self.logger.info(f"Database initialized at {self.db_path}")
            
        except sqlite3.Error as e:
            self.logger.error(f"Database setup error: {e}")
            raise
    
    def fetch_term_info(self, term: str) -> Tuple[str, List[str]]:
        """
        Fetch term information using OpenAI API with caching and error handling.

        This function queries OpenAI's language model to get a description and
        list of prerequisite/dependent terms for the given topic.

        Args:
            term: The term to fetch information for

        Returns:
            Tuple of (description, list_of_dependent_terms)
        """
        # Check cache first
        if term in self.response_cache:
            self.cache_hits += 1
            self.logger.debug(f"Cache hit for '{term}' (cache hits: {self.cache_hits})")
            return self.response_cache[term]

        # Validate input
        if not term or not term.strip():
            self.logger.warning("Empty or invalid term provided")
            return "Invalid term", []

        term = term.strip()

        # If fallback_only mode is enabled, skip OpenAI entirely
        if self.fallback_only:
            self.logger.info(f"Fallback-only mode: using enhanced stub data for '{term}'")
            return self._get_fallback_data(term)

        try:
            # Construct the prompt for OpenAI
            prompt = self._create_term_analysis_prompt(term)

            # Make API call with retry logic
            response_data = self._call_openai_api(prompt)

            # Parse and validate response
            description, dependencies = self._parse_openai_response(response_data, term)

            # Cache the result
            self.response_cache[term] = (description, dependencies)

            self.logger.info(f"Fetched info for '{term}': {len(dependencies)} dependencies (API call #{self.api_call_count})")
            return description, dependencies

        except Exception as e:
            self.logger.error(f"Error fetching info for '{term}': {e}")
            # Fall back to enhanced stub data
            return self._get_fallback_data(term)

    def _get_fallback_data(self, term: str) -> Tuple[str, List[str]]:
        """
        Get fallback data when OpenAI API is unavailable.
        Enhanced stub data with more comprehensive coverage.
        """
        # Enhanced stub data with broader coverage
        fallback_data = {
            # Programming Languages
            "Python Programming": (
                "A high-level, interpreted programming language known for its simplicity and readability.",
                ["Programming Fundamentals", "Computer Science", "Logic", "Mathematics"]
            ),
            "Programming Fundamentals": (
                "Basic concepts of computer programming including variables, control structures, and algorithms.",
                ["Logic", "Mathematics", "Computer Science"]
            ),
            "JavaScript": (
                "A dynamic programming language primarily used for web development.",
                ["Programming Fundamentals", "Web Development", "Computer Science"]
            ),
            "Java": (
                "An object-oriented programming language designed for platform independence.",
                ["Programming Fundamentals", "Object-Oriented Programming", "Computer Science"]
            ),

            # Computer Science
            "Computer Science": (
                "The study of algorithmic processes and computational systems.",
                ["Mathematics", "Logic", "Discrete Mathematics"]
            ),
            "Algorithms": (
                "Step-by-step procedures for solving computational problems.",
                ["Computer Science", "Mathematics", "Logic", "Data Structures"]
            ),
            "Data Structures": (
                "Ways of organizing and storing data for efficient access and modification.",
                ["Computer Science", "Programming Fundamentals", "Mathematics"]
            ),
            "Object-Oriented Programming": (
                "A programming paradigm based on objects containing data and code.",
                ["Programming Fundamentals", "Computer Science"]
            ),

            # Mathematics
            "Mathematics": (
                "The abstract science of number, quantity, and space.",
                []
            ),
            "Statistics": (
                "The discipline that concerns collection, organization, analysis, and interpretation of data.",
                ["Mathematics", "Probability Theory"]
            ),
            "Linear Algebra": (
                "The branch of mathematics concerning linear equations and linear functions.",
                ["Mathematics", "Vector Spaces"]
            ),
            "Calculus": (
                "The mathematical study of continuous change.",
                ["Mathematics", "Limits", "Functions"]
            ),
            "Probability Theory": (
                "The branch of mathematics concerned with probability and random phenomena.",
                ["Mathematics", "Set Theory"]
            ),
            "Discrete Mathematics": (
                "The study of mathematical structures that are fundamentally discrete.",
                ["Mathematics", "Logic", "Set Theory"]
            ),

            # Machine Learning & AI
            "Machine Learning": (
                "A field of artificial intelligence that uses statistical techniques for learning from data.",
                ["Statistics", "Linear Algebra", "Calculus", "Programming Fundamentals"]
            ),
            "Artificial Intelligence": (
                "The simulation of human intelligence processes by machines.",
                ["Computer Science", "Mathematics", "Logic", "Machine Learning"]
            ),
            "Deep Learning": (
                "A subset of machine learning using artificial neural networks.",
                ["Machine Learning", "Linear Algebra", "Calculus", "Statistics"]
            ),
            "Neural Networks": (
                "Computing systems inspired by biological neural networks.",
                ["Machine Learning", "Linear Algebra", "Calculus"]
            ),

            # Web Development
            "Web Development": (
                "The work involved in developing websites and web applications.",
                ["Programming Fundamentals", "HTML", "CSS", "Computer Science"]
            ),
            "HTML": (
                "The standard markup language for creating web pages.",
                ["Web Development"]
            ),
            "CSS": (
                "A style sheet language used for describing the presentation of web pages.",
                ["HTML", "Web Development"]
            ),

            # Advanced Math Topics
            "Vector Spaces": (
                "A collection of objects called vectors that can be added and scaled.",
                ["Linear Algebra", "Mathematics"]
            ),
            "Limits": (
                "A fundamental concept describing the behavior of functions near specific points.",
                ["Mathematics", "Functions"]
            ),
            "Functions": (
                "Mathematical relations that assign exactly one output to each input.",
                ["Mathematics"]
            ),
            "Set Theory": (
                "The branch of mathematical logic that studies sets.",
                ["Mathematics", "Logic"]
            ),
            "Logic": (
                "The systematic study of the principles of valid inference and reasoning.",
                ["Mathematics"]
            ),

            # Physics
            "Physics": (
                "The natural science that studies matter, motion, and behavior through space and time.",
                ["Mathematics", "Calculus", "Linear Algebra"]
            ),
            "Quantum Mechanics": (
                "The branch of physics describing the behavior of matter and energy at atomic scales.",
                ["Physics", "Linear Algebra", "Calculus", "Probability Theory"]
            )
        }

        if term in fallback_data:
            description, dependencies = fallback_data[term]
            self.logger.info(f"Using fallback data for '{term}': {len(dependencies)} dependencies")
            return description, dependencies
        else:
            # Generate basic fallback for unknown terms
            self.logger.warning(f"No fallback data for '{term}', generating basic response")
            return f"A topic in the field of {term}", []

    def _create_term_analysis_prompt(self, term: str) -> str:
        """Create a structured prompt for OpenAI to analyze a term."""
        return f"""Analyze the academic/educational topic: "{term}"

Please provide a response in the following JSON format:
{{
    "term": "{term}",
    "description": "A comprehensive 1-2 sentence description of this topic",
    "prerequisites": ["list", "of", "prerequisite", "topics", "needed", "to", "understand", "this", "topic"]
}}

Guidelines:
- Description should be educational and concise (1-2 sentences)
- Prerequisites should be fundamental concepts needed to understand this topic
- List 2-6 prerequisites (fewer for basic topics, more for advanced topics)
- Use standard academic terminology
- Prerequisites should be more fundamental than the main topic
- Avoid circular dependencies
- Return valid JSON only

Topic to analyze: {term}"""

    def _call_openai_api(self, prompt: str) -> str:
        """Make API call to OpenAI with retry logic and rate limiting."""
        max_retries = 3
        base_delay = 1.0

        for attempt in range(max_retries):
            try:
                self.api_call_count += 1

                response = self.openai_client.chat.completions.create(
                    model="gpt-4o-mini",  # Least expensive model for Plus accounts
                    messages=[
                        {"role": "system", "content": "You are an educational expert that analyzes academic topics and their prerequisites. Always respond with valid JSON."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=300,
                    temperature=0.3,  # Lower temperature for more consistent responses
                    timeout=30
                )

                return response.choices[0].message.content.strip()

            except openai.RateLimitError as e:
                delay = base_delay * (2 ** attempt)
                self.logger.warning(f"Rate limit hit: {e}, retrying in {delay}s (attempt {attempt + 1}/{max_retries})")
                time.sleep(delay)

            except openai.APITimeoutError as e:
                delay = base_delay * (2 ** attempt)
                self.logger.warning(f"API timeout: {e}, retrying in {delay}s (attempt {attempt + 1}/{max_retries})")
                time.sleep(delay)

            except Exception as e:
                if attempt == max_retries - 1:
                    self.logger.error(f"Final API error after {max_retries} attempts: {e}")
                    raise
                delay = base_delay * (2 ** attempt)
                self.logger.warning(f"API error: {e}, retrying in {delay}s (attempt {attempt + 1}/{max_retries})")
                time.sleep(delay)

        raise Exception(f"Failed to get response after {max_retries} attempts")

    def _parse_openai_response(self, response_text: str, original_term: str) -> Tuple[str, List[str]]:
        """Parse and validate OpenAI JSON response."""
        try:
            # Clean up response text (remove markdown formatting if present)
            response_text = response_text.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()

            # Parse JSON
            data = json.loads(response_text)

            # Validate required fields
            if not isinstance(data, dict):
                raise ValueError("Response is not a JSON object")

            description = data.get('description', '').strip()
            prerequisites = data.get('prerequisites', [])

            # Validate description
            if not description:
                description = f"Description for {original_term}"
                self.logger.warning(f"Empty description in response for '{original_term}'")

            # Validate prerequisites
            if not isinstance(prerequisites, list):
                prerequisites = []
                self.logger.warning(f"Invalid prerequisites format for '{original_term}'")
            else:
                # Clean and validate prerequisite terms
                prerequisites = [
                    str(prereq).strip()
                    for prereq in prerequisites
                    if prereq and str(prereq).strip() and str(prereq).strip() != original_term
                ]
                # Limit to reasonable number
                prerequisites = prerequisites[:8]

            return description, prerequisites

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response for '{original_term}': {e}")
            self.logger.debug(f"Raw response: {response_text}")
            return f"Description for {original_term}", []

        except Exception as e:
            self.logger.error(f"Error parsing response for '{original_term}': {e}")
            return f"Description for {original_term}", []
    
    def term_exists(self, term: str) -> bool:
        """
        Check if a term already exists in the database.

        Args:
            term: The term to check for existence

        Returns:
            True if the term exists in the database, False otherwise

        Raises:
            sqlite3.Error: If database query fails
        """
        if not term or not term.strip():
            return False

        try:
            cursor = self.conn.execute(
                "SELECT COUNT(*) FROM topics WHERE term = ?", (term.strip(),)
            )
            count = cursor.fetchone()[0]
            return count > 0
        except sqlite3.Error as e:
            self.logger.error(f"Error checking term existence: {e}")
            return False
    
    def insert_term(self, term: str, depth: int, description: Optional[str] = None) -> bool:
        """
        Insert a new term into the database.

        Args:
            term: The term to insert (will be stripped of whitespace)
            depth: The depth level of the term (must be >= 0)
            description: Optional description of the term

        Returns:
            True if insertion successful, False otherwise

        Raises:
            ValueError: If term is empty or depth is negative
        """
        # Input validation
        if not term or not term.strip():
            raise ValueError("Term cannot be empty")
        if depth < 0:
            raise ValueError("Depth cannot be negative")

        term = term.strip()

        try:
            self.conn.execute(
                """INSERT INTO topics (term, depth, description, unseen_dependent_terms, seen_dependent_terms)
                   VALUES (?, ?, ?, '[]', '[]')""",
                (term, depth, description)
            )
            self.conn.commit()
            self.logger.info(f"Inserted term '{term}' at depth {depth}")
            return True
        except sqlite3.IntegrityError:
            self.logger.warning(f"Term '{term}' already exists, skipping insertion")
            return False
        except sqlite3.Error as e:
            self.logger.error(f"Error inserting term '{term}': {e}")
            return False

    def update_term_dependencies(self, term: str, seen_deps: List[str], unseen_deps: List[str]) -> None:
        """
        Update the dependency lists for a term.

        Args:
            term: The term to update (must exist in database)
            seen_deps: List of dependencies that exist in the database
            unseen_deps: List of dependencies not yet in the database

        Raises:
            ValueError: If term is empty
            sqlite3.Error: If database update fails
        """
        if not term or not term.strip():
            raise ValueError("Term cannot be empty")

        # Validate dependency lists
        seen_deps = [dep.strip() for dep in seen_deps if dep and dep.strip()]
        unseen_deps = [dep.strip() for dep in unseen_deps if dep and dep.strip()]

        try:
            self.conn.execute(
                """UPDATE topics
                   SET seen_dependent_terms = ?, unseen_dependent_terms = ?
                   WHERE term = ?""",
                (json.dumps(seen_deps), json.dumps(unseen_deps), term.strip())
            )
            self.conn.commit()
            self.logger.info(f"Updated dependencies for '{term}': {len(seen_deps)} seen, {len(unseen_deps)} unseen")
        except sqlite3.Error as e:
            self.logger.error(f"Error updating dependencies for '{term}': {e}")
            raise

    def get_terms_at_depth(self, depth: int) -> List[str]:
        """Get all terms at a specific depth level."""
        try:
            cursor = self.conn.execute(
                "SELECT term FROM topics WHERE depth = ?", (depth,)
            )
            terms = [row[0] for row in cursor.fetchall()]
            return terms
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching terms at depth {depth}: {e}")
            return []

    def build_topic_tree(self, root_term: str, max_depth: int = 5) -> None:
        """
        Build the topic tree starting from a root term.

        Args:
            root_term: The starting term for the tree
            max_depth: Maximum depth to traverse (default: 5, so depths 0-5)

        Raises:
            ValueError: If root_term is empty or max_depth is invalid
        """
        # Input validation
        if not root_term or not root_term.strip():
            raise ValueError("Root term cannot be empty")

        if max_depth < 0 or max_depth > 10:
            raise ValueError("Max depth must be between 0 and 10")

        root_term = root_term.strip()
        self.logger.info(f"Building topic tree starting from '{root_term}' (max depth: {max_depth})")

        # Insert root term if it doesn't exist
        if not self.term_exists(root_term):
            description, _ = self.fetch_term_info(root_term)
            self.insert_term(root_term, 0, description)

        # Process terms level by level (breadth-first)
        for current_depth in range(max_depth + 1):
            terms_at_depth = self.get_terms_at_depth(current_depth)

            if not terms_at_depth:
                self.logger.info(f"No terms at depth {current_depth}, stopping")
                break

            self.logger.info(f"Processing {len(terms_at_depth)} terms at depth {current_depth}")

            for term in terms_at_depth:
                self._process_term(term, current_depth, max_depth)

        self.logger.info("Topic tree building completed")

    def _process_term(self, term: str, current_depth: int, max_depth: int):
        """
        Process a single term: fetch its dependencies and update the database.

        Args:
            term: The term to process
            current_depth: Current depth of the term
            max_depth: Maximum allowed depth
        """
        try:
            # Fetch term information
            description, dependent_terms = self.fetch_term_info(term)

            # Split dependencies into seen and unseen
            seen_deps = []
            unseen_deps = []

            for dep_term in dependent_terms:
                if self.term_exists(dep_term):
                    seen_deps.append(dep_term)
                else:
                    unseen_deps.append(dep_term)
                    # Insert new term if we haven't reached max depth
                    if current_depth < max_depth:
                        dep_description, _ = self.fetch_term_info(dep_term)
                        self.insert_term(dep_term, current_depth + 1, dep_description)

            # Update the term's dependency lists
            self.update_term_dependencies(term, seen_deps, unseen_deps)

        except Exception as e:
            self.logger.error(f"Error processing term '{term}': {e}")

    def get_all_topics(self) -> List[Tuple]:
        """Get all topics from the database."""
        try:
            cursor = self.conn.execute(
                """SELECT id, depth, term, description,
                          seen_dependent_terms, unseen_dependent_terms
                   FROM topics ORDER BY depth, term"""
            )
            return cursor.fetchall()
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching all topics: {e}")
            return []

    def generate_graph_visualization(self, output_file: str = "graph.html"):
        """
        Generate an interactive HTML graph visualization of the topic tree with a data table.

        Args:
            output_file: Path to the output HTML file
        """
        self.logger.info(f"Generating graph visualization: {output_file}")

        # Create pyvis network
        net = Network(
            height="500px",
            width="100%",
            bgcolor="#ffffff",
            font_color="black",
            directed=True
        )

        # Configure physics for better layout
        net.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "stabilization": {"iterations": 100}
          }
        }
        """)

        # Get all topics from database
        topics = self.get_all_topics()

        if not topics:
            self.logger.warning("No topics found in database")
            return

        # Color scheme for different depths
        depth_colors = {
            0: "#ff6b6b",  # Red for root
            1: "#4ecdc4",  # Teal for depth 1
            2: "#45b7d1",  # Blue for depth 2
            3: "#96ceb4",  # Green for depth 3
            4: "#feca57",  # Yellow for depth 4
            5: "#ff9ff3",  # Pink for depth 5
        }

        # Add nodes
        for topic_id, depth, term, description, seen_deps_json, unseen_deps_json in topics:
            color = depth_colors.get(depth, "#cccccc")

            # Create node with hover information
            title = f"Depth: {depth}\nDescription: {description or 'No description'}"

            net.add_node(
                term,
                label=term,
                color=color,
                title=title,
                size=20 + (5 - depth) * 5  # Larger nodes for shallower depths
            )

        # Add edges (dependencies)
        for topic_id, depth, term, description, seen_deps_json, unseen_deps_json in topics:
            try:
                seen_deps = json.loads(seen_deps_json) if seen_deps_json else []
                unseen_deps = json.loads(unseen_deps_json) if unseen_deps_json else []

                # Add edges to seen dependencies
                for dep in seen_deps:
                    net.add_edge(term, dep, color="blue", width=2)

                # Add edges to unseen dependencies (if they exist as nodes)
                for dep in unseen_deps:
                    if any(t[2] == dep for t in topics):  # Check if dependency exists as node
                        net.add_edge(term, dep, color="red", width=1, dashes=True)

            except json.JSONDecodeError as e:
                self.logger.error(f"Error parsing dependencies for '{term}': {e}")

        # Generate the HTML content with both graph and table
        try:
            # Create a temporary file for the network
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as temp_file:
                temp_path = temp_file.name

            # Generate the network HTML
            net.write_html(temp_path)

            # Read the generated HTML
            with open(temp_path, 'r', encoding='utf-8') as f:
                network_html = f.read()

            # Clean up temp file
            os.unlink(temp_path)

            # Generate the complete HTML with table
            complete_html = self._generate_html_with_table(network_html, topics)

            # Write the complete HTML
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(complete_html)

            self.logger.info(f"Graph visualization with table saved to {output_file}")

        except Exception as e:
            self.logger.error(f"Error generating visualization: {e}")
            # Fallback: try the basic method
            try:
                net.write_html(output_file)
                self.logger.info(f"Graph visualization saved to {output_file} (basic version without table)")
            except Exception as e2:
                self.logger.error(f"Fallback method also failed: {e2}")

    def _generate_html_with_table(self, network_html: str, topics: List[Tuple]) -> str:
        """
        Generate complete HTML with both network graph and data table.

        Args:
            network_html: The HTML content from pyvis
            topics: List of topic tuples from database

        Returns:
            Complete HTML string with graph and table
        """
        # Color scheme for different depths (same as graph)
        depth_colors = {
            0: "#ff6b6b",  # Red for root
            1: "#4ecdc4",  # Teal for depth 1
            2: "#45b7d1",  # Blue for depth 2
            3: "#96ceb4",  # Green for depth 3
            4: "#feca57",  # Yellow for depth 4
            5: "#ff9ff3",  # Pink for depth 5
        }

        # Generate table rows
        table_rows = []
        for topic_id, depth, term, description, seen_deps_json, unseen_deps_json in topics:
            try:
                seen_deps = json.loads(seen_deps_json) if seen_deps_json else []
                unseen_deps = json.loads(unseen_deps_json) if unseen_deps_json else []

                color = depth_colors.get(depth, "#cccccc")

                # Format dependencies
                seen_deps_str = ", ".join(seen_deps) if seen_deps else "None"
                unseen_deps_str = ", ".join(unseen_deps) if unseen_deps else "None"

                table_rows.append(f"""
                <tr>
                    <td style="background-color: {color}; color: white; font-weight: bold; text-align: center;">{depth}</td>
                    <td><strong>{term}</strong></td>
                    <td>{description or 'No description'}</td>
                    <td><span style="color: blue;">{seen_deps_str}</span></td>
                    <td><span style="color: red;">{unseen_deps_str}</span></td>
                </tr>
                """)
            except json.JSONDecodeError:
                table_rows.append(f"""
                <tr>
                    <td>{depth}</td>
                    <td><strong>{term}</strong></td>
                    <td>{description or 'No description'}</td>
                    <td colspan="2">Error parsing dependencies</td>
                </tr>
                """)

        # Extract the network div from the original HTML
        # Find the network div and its content
        network_div_match = re.search(r'<div id="[^"]*"[^>]*>.*?</div>', network_html, re.DOTALL)
        network_div = network_div_match.group(0) if network_div_match else '<div>Network visualization not found</div>'

        # Extract the script tags
        script_matches = re.findall(r'<script[^>]*>.*?</script>', network_html, re.DOTALL)
        scripts = '\n'.join(script_matches)

        # Generate complete HTML
        complete_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }}
        .section {{
            margin-bottom: 30px;
        }}
        .section h2 {{
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }}
        th {{
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        tr:hover {{
            background-color: #f0f0f0;
        }}
        .legend {{
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 5px;
        }}
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }}
        .stats {{
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }}
        .stat-item {{
            text-align: center;
        }}
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }}
        .stat-label {{
            font-size: 14px;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{len(topics)}</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{max([t[1] for t in topics]) + 1 if topics else 0}</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{sum(1 for t in topics if json.loads(t[4] or '[]'))}</div>
                <div class="stat-label">Topics with Dependencies</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>
            {network_div}
        </div>

        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th>Depth</th>
                        <th>Term</th>
                        <th>Description</th>
                        <th>Seen Dependencies</th>
                        <th>Unseen Dependencies</th>
                    </tr>
                </thead>
                <tbody>
                    {''.join(table_rows)}
                </tbody>
            </table>
        </div>
    </div>

    {scripts}
</body>
</html>
        """

        return complete_html

    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get caching and API usage statistics."""
        total_requests = self.api_call_count + self.cache_hits
        cache_hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            "api_calls": self.api_call_count,
            "cache_hits": self.cache_hits,
            "total_requests": total_requests,
            "cache_hit_rate": f"{cache_hit_rate:.1f}%",
            "cached_terms": len(self.response_cache)
        }

    def clear_cache(self) -> None:
        """Clear the response cache."""
        self.response_cache.clear()
        self.cache_hits = 0
        self.logger.info("Response cache cleared")

    def close(self) -> None:
        """Close the database connection and log statistics."""
        if self.conn:
            self.conn.close()

        # Log final statistics
        stats = self.get_cache_statistics()
        self.logger.info(f"Session complete - API calls: {stats['api_calls']}, "
                        f"Cache hits: {stats['cache_hits']}, "
                        f"Cache hit rate: {stats['cache_hit_rate']}")
        self.logger.info("Database connection closed")


def main():
    """Main CLI interface for the topic tree builder."""
    parser = argparse.ArgumentParser(
        description="Build and visualize a topic dependency tree",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python topic_graph.py --root "Machine Learning"
  python topic_graph.py --root "Statistics" --max-depth 3
  python topic_graph.py --root "Programming" --output custom_graph.html
        """
    )

    parser.add_argument(
        "--root", "-r",
        required=True,
        help="Root term to start building the topic tree from"
    )

    parser.add_argument(
        "--max-depth", "-d",
        type=int,
        default=5,
        help="Maximum depth to traverse (default: 5, meaning depths 0-5)"
    )

    parser.add_argument(
        "--output", "-o",
        default="graph.html",
        help="Output HTML file for the graph visualization (default: graph.html)"
    )

    parser.add_argument(
        "--db-path",
        default="topics.db",
        help="Path to the SQLite database file (default: topics.db)"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    parser.add_argument(
        "--fallback-only",
        action="store_true",
        help="Use only fallback data (skip OpenAI API calls)"
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create and run the topic tree builder
    builder = None
    try:
        print(f"Building topic tree starting from: {args.root}")
        print(f"Maximum depth: {args.max_depth}")
        print(f"Database: {args.db_path}")
        print(f"Output: {args.output}")
        print("-" * 50)

        builder = TopicTreeBuilder(args.db_path, args.fallback_only)
        builder.build_topic_tree(args.root, args.max_depth)
        builder.generate_graph_visualization(args.output)

        # Display statistics
        stats = builder.get_cache_statistics()

        print("-" * 50)
        print(f"✅ Topic tree building completed!")
        print(f"📊 Graph visualization saved to: {args.output}")
        print(f"💾 Database saved to: {args.db_path}")
        print(f"🔄 API calls made: {stats['api_calls']}")
        print(f"💰 Cache hit rate: {stats['cache_hit_rate']}")
        print(f"📝 Terms cached: {stats['cached_terms']}")

    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
    finally:
        if builder:
            builder.close()


if __name__ == "__main__":
    main()
