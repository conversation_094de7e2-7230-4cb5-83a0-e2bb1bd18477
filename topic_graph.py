#!/usr/bin/env python3
"""
Topic Tree Builder and Visualizer

A Python application that builds a hierarchical topic dependency tree
and visualizes it as an interactive graph.
"""

import sqlite3
import json
import argparse
import sys
import tempfile
import os
import re
import time
from typing import List, Tuple, Optional, Dict, Any
import logging

# Third-party imports
try:
    from pyvis.network import Network
except ImportError:
    print("Error: pyvis not installed. Run: pip install -r requirements.txt")
    sys.exit(1)

try:
    import openai
except ImportError:
    print("Error: openai not installed. Run: pip install -r requirements.txt")
    sys.exit(1)


class TopicTreeBuilder:
    """Builds and manages a topic dependency tree using SQLite database."""

    def __init__(self, db_path: str = "topics.db", fallback_only: bool = False):
        """Initialize the topic tree builder with database connection."""
        self.db_path = db_path
        self.conn = None
        self.fallback_only = fallback_only
        self.response_cache: Dict[str, Tuple[str, List[str]]] = {}
        self.api_call_count = 0
        self.cache_hits = 0
        self._setup_logging()
        if not fallback_only:
            self._setup_openai()
        self._setup_database()
    
    def _setup_logging(self):
        """Configure logging for the application."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def _setup_openai(self):
        """Initialize OpenAI client with API key from environment."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.error("OPENAI_API_KEY environment variable not set")
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable.")

        self.openai_client = openai.OpenAI(api_key=api_key)
        self.logger.info("OpenAI client initialized successfully")
    
    def _setup_database(self):
        """Initialize SQLite database and create topics table if not exists."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.execute("PRAGMA foreign_keys = ON")
            
            # Drop existing table to recreate with new schema
            self.conn.execute("DROP TABLE IF EXISTS topics")

            # Create topics table with simplified schema
            create_table_sql = """
            CREATE TABLE topics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                depth INTEGER NOT NULL,
                term TEXT UNIQUE NOT NULL,
                description TEXT,
                prerequisites TEXT DEFAULT '[]'
            )
            """
            self.conn.execute(create_table_sql)
            self.conn.commit()
            self.logger.info(f"Database initialized at {self.db_path} (table recreated with new schema)")
            
        except sqlite3.Error as e:
            self.logger.error(f"Database setup error: {e}")
            raise

    def _migrate_database_schema(self):
        """Migrate database schema from old format to new simplified format."""
        try:
            # Check if old columns exist
            cursor = self.conn.execute("PRAGMA table_info(topics)")
            columns = [row[1] for row in cursor.fetchall()]

            if 'seen_dependent_terms' in columns and 'unseen_dependent_terms' in columns:
                self.logger.info("Migrating database schema to simplified format...")

                # Create new table with simplified schema
                self.conn.execute("""
                    CREATE TABLE IF NOT EXISTS topics_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        depth INTEGER NOT NULL,
                        term TEXT UNIQUE NOT NULL,
                        description TEXT,
                        dependencies TEXT DEFAULT '[]'
                    )
                """)

                # Migrate data from old table to new table
                cursor = self.conn.execute("""
                    SELECT id, depth, term, description, seen_dependent_terms, unseen_dependent_terms
                    FROM topics
                """)

                for row in cursor.fetchall():
                    topic_id, depth, term, description, seen_deps_json, unseen_deps_json = row

                    # Combine seen and unseen dependencies
                    try:
                        seen_deps = json.loads(seen_deps_json) if seen_deps_json else []
                        unseen_deps = json.loads(unseen_deps_json) if unseen_deps_json else []
                        all_deps = seen_deps + unseen_deps

                        self.conn.execute("""
                            INSERT INTO topics_new (id, depth, term, description, dependencies)
                            VALUES (?, ?, ?, ?, ?)
                        """, (topic_id, depth, term, description, json.dumps(all_deps)))
                    except (json.JSONDecodeError, TypeError):
                        # If there's an error parsing JSON, insert with empty dependencies
                        self.conn.execute("""
                            INSERT INTO topics_new (id, depth, term, description, dependencies)
                            VALUES (?, ?, ?, ?, '[]')
                        """, (topic_id, depth, term, description))

                # Replace old table with new table
                self.conn.execute("DROP TABLE topics")
                self.conn.execute("ALTER TABLE topics_new RENAME TO topics")
                self.conn.commit()

                self.logger.info("Database schema migration completed successfully")

        except sqlite3.Error as e:
            self.logger.error(f"Error during database migration: {e}")
            # Continue anyway - the table creation will handle it
    
    def fetch_term_info(self, term: str, parent_terms: List[str] = None, same_depth_terms: List[str] = None) -> Tuple[str, List[str]]:
        """
        Fetch term information using OpenAI API with caching and error handling.

        This function queries OpenAI's language model to get a description and
        list of prerequisite/dependent terms for the given topic.

        Args:
            term: The term to fetch information for

        Returns:
            Tuple of (description, list_of_dependent_terms)
        """
        # Validate input
        if not term or not term.strip():
            self.logger.warning("Empty or invalid term provided")
            return "Invalid term", []

        term = term.strip()

        # Check in-memory cache first
        if term in self.response_cache:
            self.cache_hits += 1
            self.logger.debug(f"Memory cache hit for '{term}' (cache hits: {self.cache_hits})")
            return self.response_cache[term]

        # Check database cache second (persistent cache)
        db_result = self.get_term_from_database(term)
        if db_result:
            # Cache in memory for faster subsequent access
            self.response_cache[term] = db_result
            self.cache_hits += 1
            return db_result

        # If fallback_only mode is enabled, skip OpenAI entirely
        if self.fallback_only:
            self.logger.info(f"Fallback-only mode: using enhanced stub data for '{term}'")
            result = self._get_fallback_data(term)
            # Cache the fallback result
            self.response_cache[term] = result
            return result

        try:
            # Get existing terms for deduplication
            existing_terms = self.get_all_existing_terms()

            # Construct the prompt for OpenAI with parent and same-depth terms
            prompt = self._create_single_term_analysis_prompt(term, existing_terms, parent_terms, same_depth_terms)

            # Make API call with retry logic
            response_data = self._call_openai_api(prompt)

            # Parse and validate response
            description, prerequisites = self._parse_openai_response(response_data, term)

            # Cache the result
            self.response_cache[term] = (description, prerequisites)

            self.logger.info(f"Fetched info for '{term}': {len(prerequisites)} prerequisites (API call #{self.api_call_count})")
            return description, prerequisites

        except Exception as e:
            self.logger.error(f"Error fetching info for '{term}': {e}")
            # Fall back to enhanced stub data
            return self._get_fallback_data(term)

    def fetch_batch_term_info(self, unknown_terms: List[str], known_terms: List[str] = None, parent_terms: List[str] = None) -> Dict[str, Tuple[str, List[str]]]:
        """
        Fetch information for multiple terms in a single API call.

        Args:
            unknown_terms: List of terms to analyze
            known_terms: List of terms already in database for deduplication

        Returns:
            Dictionary mapping term names to (description, prerequisites) tuples
        """
        if not unknown_terms:
            return {}

        # Check cache first for any terms
        results = {}
        remaining_terms = []

        for term in unknown_terms:
            if term in self.response_cache:
                results[term] = self.response_cache[term]
                self.cache_hits += 1
            else:
                # Check database cache
                db_result = self.get_term_from_database(term)
                if db_result:
                    results[term] = db_result
                    self.response_cache[term] = db_result
                    self.cache_hits += 1
                else:
                    remaining_terms.append(term)

        if not remaining_terms:
            return results

        # If fallback_only mode, use fallback data for remaining terms
        if self.fallback_only:
            for term in remaining_terms:
                fallback_result = self._get_fallback_data(term)
                results[term] = fallback_result
                self.response_cache[term] = fallback_result
            return results

        try:
            # Create batch prompt with parent terms to avoid circular dependencies
            prompt = self._create_batch_analysis_prompt(remaining_terms, known_terms, parent_terms)

            # Debug logging
            if parent_terms:
                self.logger.info(f"Processing batch with parent terms to avoid: {parent_terms}")
            self.logger.debug(f"Batch terms being analyzed: {remaining_terms}")

            # Make API call
            response_data = self._call_openai_api(prompt)

            # Parse batch response
            batch_results = self._parse_batch_openai_response(response_data, remaining_terms)

            # Cache all results
            for term, result in batch_results.items():
                self.response_cache[term] = result
                results[term] = result

            self.logger.info(f"Batch fetched info for {len(batch_results)} terms (API call #{self.api_call_count})")

        except Exception as e:
            self.logger.error(f"Error in batch fetch: {e}")
            # Return empty results for failed batch - let fallback handle it
            pass

        return results

    def _get_fallback_data(self, term: str) -> Tuple[str, List[str]]:
        """
        Get fallback data when OpenAI API is unavailable.
        Enhanced stub data with more comprehensive coverage.
        """
        # Enhanced stub data with broader coverage
        fallback_data = {
            # Programming Languages
            "Python Programming": (
                "A high-level, interpreted programming language known for its simplicity and readability.",
                ["Programming Fundamentals", "Computer Science", "Logic", "Mathematics"]
            ),
            "Programming Fundamentals": (
                "Basic concepts of computer programming including variables, control structures, and algorithms.",
                ["Logic", "Mathematics", "Computer Science"]
            ),
            "JavaScript": (
                "A dynamic programming language primarily used for web development.",
                ["Programming Fundamentals", "Web Development", "Computer Science"]
            ),
            "Java": (
                "An object-oriented programming language designed for platform independence.",
                ["Programming Fundamentals", "Object-Oriented Programming", "Computer Science"]
            ),

            # Computer Science
            "Computer Science": (
                "The study of algorithmic processes and computational systems.",
                ["Mathematics", "Logic", "Discrete Mathematics"]
            ),
            "Algorithms": (
                "Step-by-step procedures for solving computational problems.",
                ["Computer Science", "Mathematics", "Logic", "Data Structures"]
            ),
            "Data Structures": (
                "Ways of organizing and storing data for efficient access and modification.",
                ["Computer Science", "Programming Fundamentals", "Mathematics"]
            ),
            "Object-Oriented Programming": (
                "A programming paradigm based on objects containing data and code.",
                ["Programming Fundamentals", "Computer Science"]
            ),

            # Mathematics
            "Mathematics": (
                "The abstract science of number, quantity, and space.",
                []
            ),
            "Statistics": (
                "The discipline that concerns collection, organization, analysis, and interpretation of data.",
                ["Mathematics", "Probability Theory"]
            ),
            "Linear Algebra": (
                "The branch of mathematics concerning linear equations and linear functions.",
                ["Mathematics", "Vector Spaces"]
            ),
            "Calculus": (
                "The mathematical study of continuous change.",
                ["Mathematics", "Limits", "Functions"]
            ),
            "Probability Theory": (
                "The branch of mathematics concerned with probability and random phenomena.",
                ["Mathematics", "Set Theory"]
            ),
            "Discrete Mathematics": (
                "The study of mathematical structures that are fundamentally discrete.",
                ["Mathematics", "Logic", "Set Theory"]
            ),

            # Machine Learning & AI
            "Machine Learning": (
                "A field of artificial intelligence that uses statistical techniques for learning from data.",
                ["Statistics", "Linear Algebra", "Calculus", "Programming Fundamentals"]
            ),
            "Artificial Intelligence": (
                "The simulation of human intelligence processes by machines.",
                ["Computer Science", "Mathematics", "Logic", "Machine Learning"]
            ),
            "Deep Learning": (
                "A subset of machine learning using artificial neural networks.",
                ["Machine Learning", "Linear Algebra", "Calculus", "Statistics"]
            ),
            "Neural Networks": (
                "Computing systems inspired by biological neural networks.",
                ["Machine Learning", "Linear Algebra", "Calculus"]
            ),

            # Web Development
            "Web Development": (
                "The work involved in developing websites and web applications.",
                ["Programming Fundamentals", "HTML", "CSS", "Computer Science"]
            ),
            "HTML": (
                "The standard markup language for creating web pages.",
                ["Web Development"]
            ),
            "CSS": (
                "A style sheet language used for describing the presentation of web pages.",
                ["HTML", "Web Development"]
            ),

            # Advanced Math Topics
            "Vector Spaces": (
                "A collection of objects called vectors that can be added and scaled.",
                ["Linear Algebra", "Mathematics"]
            ),
            "Limits": (
                "A fundamental concept describing the behavior of functions near specific points.",
                ["Mathematics", "Functions"]
            ),
            "Functions": (
                "Mathematical relations that assign exactly one output to each input.",
                ["Mathematics"]
            ),
            "Set Theory": (
                "The branch of mathematical logic that studies sets.",
                ["Mathematics", "Logic"]
            ),
            "Logic": (
                "The systematic study of the principles of valid inference and reasoning.",
                ["Mathematics"]
            ),

            # Physics
            "Physics": (
                "The natural science that studies matter, motion, and behavior through space and time.",
                ["Mathematics", "Calculus", "Linear Algebra"]
            ),
            "Quantum Mechanics": (
                "The branch of physics describing the behavior of matter and energy at atomic scales.",
                ["Physics", "Linear Algebra", "Calculus", "Probability Theory"]
            )
        }

        if term in fallback_data:
            description, dependencies = fallback_data[term]
            self.logger.info(f"Using fallback data for '{term}': {len(dependencies)} prerequisites")
            return description, dependencies
        else:
            # Generate basic fallback for unknown terms
            self.logger.warning(f"No fallback data for '{term}', generating basic response")
            return f"A topic in the field of {term}", []

    def _create_single_term_analysis_prompt(self, term: str, known_terms: List[str] = None, parent_terms: List[str] = None, same_depth_terms: List[str] = None) -> str:
        """Create a structured prompt for OpenAI to analyze a single term and its prerequisites."""
        known_terms_section = ""
        if known_terms:
            # Limit to most relevant known terms to avoid prompt bloat
            relevant_terms = known_terms[:50]  # Limit to 50 terms
            known_terms_section = f"""
KNOWN TERMS IN DATABASE (prefer these if they match your prerequisites):
{', '.join(relevant_terms)}

If any of your prerequisites are similar to known terms above, use the known term instead.
"""

        # Add parent terms section to prevent circular dependencies
        parent_terms_section = ""
        if parent_terms:
            parent_terms_section = f"""

🚫 FORBIDDEN PARENT TERMS (NEVER use these as prerequisites):
{', '.join(parent_terms)}

⚠️  CRITICAL WARNING: The terms listed above are PARENT terms. Including them as prerequisites would create CIRCULAR DEPENDENCIES.
❌ NEVER include: {', '.join(parent_terms)}
✅ Only use terms that are MORE FUNDAMENTAL than '{term}'.
"""

        # Add same-depth terms section to prevent peer dependencies
        same_depth_section = ""
        if same_depth_terms:
            same_depth_section = f"""

🚫 FORBIDDEN PEER TERMS (NEVER use these as prerequisites):
{', '.join(same_depth_terms)}

⚠️  CRITICAL WARNING: The terms above are PEER terms at the same depth level as '{term}'.
❌ NEVER make them prerequisites of '{term}'
✅ Only use terms that are MORE FUNDAMENTAL than '{term}'.
"""

        return f"""Analyze this topic: {term}

INSTRUCTIONS:
- For this topic, provide a clear, concise description (under 150 characters)
- Identify 2-5 prerequisite topics or concepts needed to understand {term}
- Keep prerequisite terms SHORT (4 words max)
- Prerequisites should represent foundational knowledge needed to understand the topic
- NEVER include parent terms as prerequisites (prevents circular dependencies)
- NEVER include same-depth terms as prerequisites (prevents peer dependencies)
- Consider both technical and conceptual prerequisites{known_terms_section}{parent_terms_section}{same_depth_section}"""

    def _create_batch_analysis_prompt(self, unknown_terms: List[str], known_terms: List[str] = None, parent_terms: List[str] = None) -> str:
        """Create a structured prompt for OpenAI to analyze multiple topics and their prerequisites."""
        known_terms_section = ""
        if known_terms:
            # Limit to most relevant known terms to avoid prompt bloat
            relevant_terms = known_terms[:50]  # Limit to 50 terms
            known_terms_section = f"""
KNOWN TERMS IN DATABASE (prefer these if they match your prerequisites):
{', '.join(relevant_terms)}

If any of your prerequisites are similar to known terms above, use the known term instead.
"""

        # Add parent terms section to prevent circular dependencies
        parent_terms_section = ""
        if parent_terms:
            parent_terms_section = f"""

🚫 FORBIDDEN PARENT TERMS (NEVER use these as prerequisites):
{', '.join(parent_terms)}

⚠️  CRITICAL WARNING: The terms listed above are PARENT terms. Including them as prerequisites would create CIRCULAR DEPENDENCIES.
❌ NEVER include: {', '.join(parent_terms)}
✅ Only use terms that are MORE FUNDAMENTAL than the terms being analyzed.
"""

        # Add same-depth terms section to prevent peer dependencies
        same_depth_section = f"""

🚫 FORBIDDEN PEER TERMS (NEVER use these as prerequisites for each other):
{', '.join(unknown_terms)}

⚠️  CRITICAL WARNING: The terms above are PEER terms at the same depth level.
❌ NEVER make them prerequisites of each other (e.g., Statistics → Probability AND Probability → Statistics)
✅ Only use terms that are MORE FUNDAMENTAL than ALL the terms being analyzed.
"""

        return f"""Analyze these topics: {unknown_terms}

INSTRUCTIONS:
- For each topic, provide a clear, concise description (under 150 characters)
- Identify 2-5 prerequisite topics or concepts needed to understand each term
- Keep prerequisite terms SHORT (4 words max)
- Prerequisites should represent foundational knowledge needed to understand the topic
- NEVER include parent terms as prerequisites (prevents circular dependencies)
- NEVER include same-depth terms as prerequisites (prevents peer dependencies)
- Avoid circular dependencies (don't list a topic as its own prerequisite)
- Consider both technical and conceptual prerequisites{known_terms_section}{parent_terms_section}{same_depth_section}"""

    def _call_openai_api(self, prompt: str) -> str:
        """Make API call to OpenAI with retry logic and rate limiting."""
        max_retries = 3
        base_delay = 1.0

        for attempt in range(max_retries):
            try:
                self.api_call_count += 1

                # Define the tool schema for structured output
                tools = [
                    {
                        "type": "function",
                        "function": {
                            "name": "analyze_topics",
                            "description": "Analyze topics and their prerequisite knowledge",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "terms": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "term": {
                                                    "type": "string",
                                                    "description": "The exact term name from the list"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "description": "Clear description of what this topic or concept entails"
                                                },
                                                "prerequisites": {
                                                    "type": "array",
                                                    "items": {
                                                        "type": "string"
                                                    },
                                                    "description": "List of prerequisite knowledge or topics needed to understand this term"
                                                }
                                            },
                                            "required": ["term", "description", "prerequisites"]
                                        }
                                    }
                                },
                                "required": ["terms"]
                            }
                        }
                    }
                ]

                response = self.openai_client.chat.completions.create(
                    model="gpt-4o-2024-08-06",  # Specific model version with tool calling support
                    messages=[
                        {"role": "system", "content": "You are an expert at analyzing topics and identifying the prerequisite knowledge needed to understand them."},
                        {"role": "user", "content": prompt}
                    ],
                    tools=tools,
                    tool_choice={"type": "function", "function": {"name": "analyze_topics"}},
                    max_tokens=1000,
                    temperature=0.4,
                    timeout=30
                )

                # Extract the tool call response
                tool_call = response.choices[0].message.tool_calls[0]
                return tool_call.function.arguments

            except openai.RateLimitError as e:
                delay = base_delay * (2 ** attempt)
                self.logger.warning(f"Rate limit hit: {e}, retrying in {delay}s (attempt {attempt + 1}/{max_retries})")
                time.sleep(delay)

            except openai.APITimeoutError as e:
                delay = base_delay * (2 ** attempt)
                self.logger.warning(f"API timeout: {e}, retrying in {delay}s (attempt {attempt + 1}/{max_retries})")
                time.sleep(delay)

            except Exception as e:
                if attempt == max_retries - 1:
                    self.logger.error(f"Final API error after {max_retries} attempts: {e}")
                    raise
                delay = base_delay * (2 ** attempt)
                self.logger.warning(f"API error: {e}, retrying in {delay}s (attempt {attempt + 1}/{max_retries})")
                time.sleep(delay)

        raise Exception(f"Failed to get response after {max_retries} attempts")

    def _parse_openai_response(self, response_text: str, original_term: str) -> Tuple[str, List[str]]:
        """Parse and validate OpenAI function call response."""
        try:
            # Parse the function call response JSON
            data = json.loads(response_text)

            # Extract terms array from function call response
            if 'terms' in data and isinstance(data['terms'], list) and len(data['terms']) > 0:
                term_data = data['terms'][0]  # Get the first (and should be only) term

                description = term_data.get('description', '').strip()
                prerequisites = term_data.get('prerequisites', [])

                # Validate description
                if not description:
                    description = f"Description for {original_term}"
                    self.logger.warning(f"Empty description in response for '{original_term}'")

                # Validate prerequisites
                if not isinstance(prerequisites, list):
                    prerequisites = []
                    self.logger.warning(f"Invalid prerequisites format for '{original_term}'")
                else:
                    # Clean and validate prerequisite terms
                    prerequisites = [
                        str(prereq).strip()
                        for prereq in prerequisites
                        if prereq and str(prereq).strip() and str(prereq).strip() != original_term
                    ]
                    # Limit to reasonable number
                    prerequisites = prerequisites[:8]

                return description, prerequisites
            else:
                # Fallback if structure is unexpected
                self.logger.warning(f"Unexpected response structure for '{original_term}'")
                return f"Description for {original_term}", []

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response for '{original_term}': {e}")
            self.logger.debug(f"Raw response: {response_text}")
            return f"Description for {original_term}", []

        except Exception as e:
            self.logger.error(f"Error parsing response for '{original_term}': {e}")
            return f"Description for {original_term}", []

    def _parse_batch_openai_response(self, response_text: str, expected_terms: List[str]) -> Dict[str, Tuple[str, List[str]]]:
        """Parse OpenAI batch response and return term information."""
        results = {}

        try:
            # Clean up response text (remove markdown formatting if present)
            response_text = response_text.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()

            # Additional cleanup for common JSON issues
            # Remove comments that might break JSON
            response_text = re.sub(r'//.*?\n', '\n', response_text)
            response_text = re.sub(r'/\*.*?\*/', '', response_text, flags=re.DOTALL)

            # Fix common quote issues
            response_text = response_text.replace('"', '"').replace('"', '"')
            response_text = response_text.replace(''', "'").replace(''', "'")

            # Parse JSON
            data = json.loads(response_text)

            if 'terms' not in data:
                raise ValueError("Response missing 'terms' field")

            terms_data = data['terms']
            if not isinstance(terms_data, list):
                raise ValueError("'terms' field must be a list")

            for term_info in terms_data:
                if not isinstance(term_info, dict):
                    continue

                term = term_info.get('term', '').strip()
                description = term_info.get('description', '').strip()
                prerequisites = term_info.get('prerequisites', [])

                if not term:
                    continue

                # Validate and clean prerequisites
                if isinstance(prerequisites, list):
                    clean_prereqs = [p.strip() for p in prerequisites if isinstance(p, str) and p.strip()]
                else:
                    clean_prereqs = []

                results[term] = (description or f"Description for {term}", clean_prereqs)
                self.logger.debug(f"Parsed batch term '{term}': {len(clean_prereqs)} prerequisites")

            # Check if we got all expected terms
            missing_terms = set(expected_terms) - set(results.keys())
            if missing_terms:
                self.logger.warning(f"Missing terms in batch response: {missing_terms}")
                # Add fallback data for missing terms
                for term in missing_terms:
                    results[term] = self._get_fallback_data(term)

            return results

        except Exception as e:
            self.logger.error(f"Error parsing batch OpenAI response: {e}")
            # Return fallback data for all expected terms
            return {term: self._get_fallback_data(term) for term in expected_terms}

    def term_exists(self, term: str) -> bool:
        """
        Check if a term already exists in the database.

        Args:
            term: The term to check for existence

        Returns:
            True if the term exists in the database, False otherwise

        Raises:
            sqlite3.Error: If database query fails
        """
        if not term or not term.strip():
            return False

        try:
            cursor = self.conn.execute(
                "SELECT COUNT(*) FROM topics WHERE term = ?", (term.strip(),)
            )
            count = cursor.fetchone()[0]
            return count > 0
        except sqlite3.Error as e:
            self.logger.error(f"Error checking term existence: {e}")
            return False
    
    def insert_term(self, term: str, depth: int, description: Optional[str] = None) -> bool:
        """
        Insert a new term into the database.

        Args:
            term: The term to insert (will be stripped of whitespace)
            depth: The depth level of the term (must be >= 0)
            description: Optional description of the term

        Returns:
            True if insertion successful, False otherwise

        Raises:
            ValueError: If term is empty or depth is negative
        """
        # Input validation
        if not term or not term.strip():
            raise ValueError("Term cannot be empty")
        if depth < 0:
            raise ValueError("Depth cannot be negative")

        term = term.strip()

        try:
            self.conn.execute(
                """INSERT INTO topics (term, depth, description, prerequisites)
                   VALUES (?, ?, ?, '[]')""",
                (term, depth, description)
            )
            self.conn.commit()
            self.logger.info(f"Inserted term '{term}' at depth {depth}")
            return True
        except sqlite3.IntegrityError:
            self.logger.warning(f"Term '{term}' already exists, skipping insertion")
            return False
        except sqlite3.Error as e:
            self.logger.error(f"Error inserting term '{term}': {e}")
            return False

    def update_term_prerequisites(self, term: str, prerequisites: List[str]) -> None:
        """
        Update the prerequisites list for a term.

        Args:
            term: The term to update (must exist in database)
            prerequisites: List of all prerequisites for this term

        Raises:
            ValueError: If term is empty
            sqlite3.Error: If database update fails
        """
        if not term or not term.strip():
            raise ValueError("Term cannot be empty")

        # Validate prerequisites list
        prerequisites = [prereq.strip() for prereq in prerequisites if prereq and prereq.strip()]

        try:
            self.conn.execute(
                """UPDATE topics
                   SET prerequisites = ?
                   WHERE term = ?""",
                (json.dumps(prerequisites), term.strip())
            )
            self.conn.commit()
            self.logger.info(f"Updated prerequisites for '{term}': {len(prerequisites)} prerequisites")
        except sqlite3.Error as e:
            self.logger.error(f"Error updating prerequisites for '{term}': {e}")
            raise

    def get_terms_at_depth(self, depth: int) -> List[str]:
        """Get all terms at a specific depth level."""
        try:
            cursor = self.conn.execute(
                "SELECT term FROM topics WHERE depth = ?", (depth,)
            )
            terms = [row[0] for row in cursor.fetchall()]
            return terms
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching terms at depth {depth}: {e}")
            return []

    def build_topic_tree(self, root_term: str, max_depth: int = 5) -> None:
        """
        Build the topic tree starting from a root term.

        Args:
            root_term: The starting term for the tree
            max_depth: Maximum depth to traverse (default: 5, so depths 0-5)

        Raises:
            ValueError: If root_term is empty or max_depth is invalid
        """
        # Input validation
        if not root_term or not root_term.strip():
            raise ValueError("Root term cannot be empty")

        if max_depth < 0 or max_depth > 10:
            raise ValueError("Max depth must be between 0 and 10")

        root_term = root_term.strip()
        self.logger.info(f"Building topic tree starting from '{root_term}' (max depth: {max_depth})")

        # Insert root term if it doesn't exist
        if not self.term_exists(root_term):
            description, _ = self.fetch_term_info(root_term)  # Root has no parents or peers
            self.insert_term(root_term, 0, description)

        # Process terms level by level (breadth-first) using batch processing
        for current_depth in range(max_depth + 1):
            terms_at_depth = self.get_terms_at_depth(current_depth)

            if not terms_at_depth:
                self.logger.info(f"No terms at depth {current_depth}, stopping")
                break

            self.logger.info(f"Processing {len(terms_at_depth)} terms at depth {current_depth}")

            # Process each term individually for better control
            for term in terms_at_depth:
                self._process_term(term, current_depth, max_depth)

        self.logger.info("Topic tree building completed")

    def _process_depth_batch(self, terms_at_depth: List[str], current_depth: int, max_depth: int):
        """
        Process an entire depth level of terms in batch.

        Args:
            terms_at_depth: List of terms at the current depth
            current_depth: Current depth level
            max_depth: Maximum allowed depth
        """
        try:
            # Get all known terms for deduplication
            known_terms = self.get_all_existing_terms()

            # Get parent terms (terms at previous depth) to prevent circular dependencies
            parent_terms = []
            if current_depth > 0:
                parent_terms = self.get_terms_at_depth(current_depth - 1)

            # Batch fetch information for all terms at this depth
            batch_results = self.fetch_batch_term_info(terms_at_depth, known_terms, parent_terms)

            # Collect all new prerequisite terms that need to be added
            new_prereq_terms = set()

            # Process each term's results
            for term in terms_at_depth:
                if term in batch_results:
                    description, prerequisite_terms = batch_results[term]

                    # Update the term's prerequisites
                    self.update_term_prerequisites(term, prerequisite_terms)

                    # Collect new prerequisite terms for next depth
                    if current_depth < max_depth:
                        for prereq_term in prerequisite_terms:
                            if not self.term_exists(prereq_term):
                                new_prereq_terms.add(prereq_term)

            # Insert all new prerequisite terms for the next depth level
            if new_prereq_terms and current_depth < max_depth:
                self.logger.info(f"Adding {len(new_prereq_terms)} new terms at depth {current_depth + 1}")

                # Batch fetch descriptions for new terms (these will be at current_depth + 1)
                new_terms_list = list(new_prereq_terms)
                # Parent terms for new prerequisites are the current depth terms
                new_batch_results = self.fetch_batch_term_info(new_terms_list, known_terms, terms_at_depth)

                # Insert all new terms
                for prereq_term in new_terms_list:
                    if prereq_term in new_batch_results:
                        prereq_description, _ = new_batch_results[prereq_term]
                        self.insert_term(prereq_term, current_depth + 1, prereq_description)

        except Exception as e:
            self.logger.error(f"Error processing depth {current_depth} batch: {e}")
            # Fall back to individual processing
            for term in terms_at_depth:
                self._process_term(term, current_depth, max_depth)

    def _process_term(self, term: str, current_depth: int, max_depth: int):
        """
        Process a single term: fetch its dependencies and update the database.

        Args:
            term: The term to process
            current_depth: Current depth of the term
            max_depth: Maximum allowed depth
        """
        try:
            # Get parent terms (terms at previous depth) to prevent circular dependencies
            parent_terms = []
            if current_depth > 0:
                parent_terms = self.get_terms_at_depth(current_depth - 1)

            # Get same-depth terms to prevent peer dependencies
            same_depth_terms = self.get_terms_at_depth(current_depth)
            # Remove the current term from same-depth terms
            same_depth_terms = [t for t in same_depth_terms if t != term]

            # Fetch term information with circular dependency prevention
            description, prerequisite_terms = self.fetch_term_info(term, parent_terms, same_depth_terms)

            # Process all prerequisites
            for prereq_term in prerequisite_terms:
                if not self.term_exists(prereq_term):
                    # Insert new term if we haven't reached max depth
                    if current_depth < max_depth:
                        # For prerequisite terms, the parent is the current term
                        prereq_parent_terms = [term]
                        # Get other terms at the next depth level as peers
                        next_depth_terms = self.get_terms_at_depth(current_depth + 1)
                        prereq_description, _ = self.fetch_term_info(prereq_term, prereq_parent_terms, next_depth_terms)
                        self.insert_term(prereq_term, current_depth + 1, prereq_description)

            # Update the term's prerequisites list
            self.update_term_prerequisites(term, prerequisite_terms)

        except Exception as e:
            self.logger.error(f"Error processing term '{term}': {e}")

    def get_all_topics(self) -> List[Tuple]:
        """Get all topics from the database."""
        try:
            cursor = self.conn.execute(
                """SELECT id, depth, term, description, prerequisites
                   FROM topics ORDER BY id"""
            )
            return cursor.fetchall()
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching all topics: {e}")
            return []

    def generate_graph_visualization(self, output_file: str = "graph.html"):
        """
        Generate an interactive HTML graph visualization of the topic tree with data table.

        Args:
            output_file: Path to the output HTML file
        """
        self.logger.info(f"Generating graph visualization: {output_file}")

        # Create pyvis network with optimized settings for large graphs
        net = Network(
            height="500px",
            width="100%",
            bgcolor="#ffffff",
            font_color="black",
            directed=True
        )

        # Get all topics from database first
        topics = self.get_all_topics()

        if not topics:
            self.logger.warning("No topics found in database")
            return

        # Configure physics for better performance with large graphs
        num_topics = len(topics)
        if num_topics > 100:
            # Disable physics for large graphs to improve performance
            physics_config = """
            var options = {
              "physics": {
                "enabled": false
              },
              "layout": {
                "hierarchical": {
                  "enabled": true,
                  "direction": "UD",
                  "sortMethod": "directed"
                }
              },
              "edges": {
                "arrows": {
                  "to": {
                    "enabled": true,
                    "scaleFactor": 1.2
                  }
                },
                "smooth": {
                  "enabled": true,
                  "type": "continuous"
                }
              }
            }
            """
        else:
            # Use physics for smaller graphs
            physics_config = """
            var options = {
              "physics": {
                "enabled": true,
                "stabilization": {"iterations": 100}
              },
              "edges": {
                "arrows": {
                  "to": {
                    "enabled": true,
                    "scaleFactor": 1.2
                  }
                },
                "smooth": {
                  "enabled": true,
                  "type": "continuous"
                }
              }
            }
            """

        net.set_options(physics_config)

        # Color scheme for different depths
        depth_colors = {
            0: "#ff6b6b",  # Red for root
            1: "#4ecdc4",  # Teal for depth 1
            2: "#45b7d1",  # Blue for depth 2
            3: "#96ceb4",  # Green for depth 3
            4: "#feca57",  # Yellow for depth 4
            5: "#ff9ff3",  # Pink for depth 5
        }

        # Add nodes
        for topic_id, depth, term, description, prereqs_json in topics:
            color = depth_colors.get(depth, "#cccccc")

            # Create node without title (using hover display instead)
            net.add_node(
                term,
                label=term,
                color=color,
                size=20 + (5 - depth) * 5  # Larger nodes for shallower depths
            )

        # Add edges (prerequisites)
        for topic_id, depth, term, description, prereqs_json in topics:
            try:
                prerequisites = json.loads(prereqs_json) if prereqs_json else []

                # Add edges to all prerequisites that exist as nodes
                for prereq in prerequisites:
                    if any(t[2] == prereq for t in topics):  # Check if prerequisite exists as node
                        net.add_edge(term, prereq, color="blue", width=2)

            except json.JSONDecodeError as e:
                self.logger.error(f"Error parsing prerequisites for '{term}': {e}")

        # Generate the HTML content with both graph and table
        try:
            # Create a temporary file for the network
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as temp_file:
                temp_path = temp_file.name

            # Generate the network HTML
            net.write_html(temp_path)

            # Read the generated HTML
            with open(temp_path, 'r', encoding='utf-8') as f:
                network_html = f.read()

            # Clean up temp file
            os.unlink(temp_path)

            # Generate the complete HTML with table
            complete_html = self._generate_html_with_table(network_html, topics)

            # Write the complete HTML
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(complete_html)

            self.logger.info(f"Graph visualization with table saved to {output_file}")

        except Exception as e:
            self.logger.error(f"Error generating visualization: {e}")
            # Fallback: try the basic method
            try:
                net.write_html(output_file)
                self.logger.info(f"Graph visualization saved to {output_file} (basic version without table)")
            except Exception as e2:
                self.logger.error(f"Fallback method also failed: {e2}")

    def _generate_html_with_table(self, network_html: str, topics: List[Tuple]) -> str:
        """
        Generate complete HTML with network graph and data table.

        Args:
            network_html: The HTML content from pyvis
            topics: List of topic tuples from database

        Returns:
            Complete HTML string with graph and table
        """
        # Color scheme for different depths (same as graph)
        depth_colors = {
            0: "#ff6b6b",  # Red for root
            1: "#4ecdc4",  # Teal for depth 1
            2: "#45b7d1",  # Blue for depth 2
            3: "#96ceb4",  # Green for depth 3
            4: "#feca57",  # Yellow for depth 4
            5: "#ff9ff3",  # Pink for depth 5
        }

        # Generate table rows
        table_rows = []
        for topic_id, depth, term, description, prereqs_json in topics:
            try:
                prerequisites = json.loads(prereqs_json) if prereqs_json else []

                color = depth_colors.get(depth, "#cccccc")

                # Format prerequisites
                prereqs_str = ", ".join(prerequisites) if prerequisites else "None"

                table_rows.append(f"""
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">{topic_id}</td>
                    <td style="background-color: {color}; color: white; font-weight: bold; text-align: center;">{depth}</td>
                    <td><strong>{term}</strong></td>
                    <td>{description or 'No description'}</td>
                    <td>{prereqs_str}</td>
                </tr>
                """)
            except json.JSONDecodeError:
                table_rows.append(f"""
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">{topic_id}</td>
                    <td>{depth}</td>
                    <td><strong>{term}</strong></td>
                    <td>{description or 'No description'}</td>
                    <td>Error parsing prerequisites</td>
                </tr>
                """)

        # Extract the network div from the original HTML
        # Find the network div and its content
        network_div_match = re.search(r'<div id="[^"]*"[^>]*>.*?</div>', network_html, re.DOTALL)
        network_div = network_div_match.group(0) if network_div_match else '<div>Network visualization not found</div>'

        # Extract the script tags and modify to expose network globally
        script_matches = re.findall(r'<script[^>]*>.*?</script>', network_html, re.DOTALL)
        scripts = '\n'.join(script_matches)

        # Add global network variable assignment
        scripts += """
        <script>
        // Make network globally accessible for filtering
        setTimeout(() => {
            if (typeof network !== 'undefined') {
                window.network = network;
            }
        }, 100);
        </script>
        """

        # Generate table section
        table_section = f"""
        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th class="sortable" onclick="sortTable(0)">ID</th>
                        <th class="sortable" onclick="sortTable(1)">Depth</th>
                        <th class="sortable" onclick="sortTable(2)">Term</th>
                        <th>Description</th>
                        <th>Prerequisites</th>
                    </tr>
                </thead>
                <tbody>
                    {''.join(table_rows)}
                </tbody>
            </table>
        </div>"""

        # Generate complete HTML with expandable graph
        complete_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }}
        .section {{
            margin-bottom: 30px;
        }}
        .section h2 {{
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }}
        .graph-container {{
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }}
        .graph-container.expanded {{
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1000;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }}
        .graph-container.expanded .graph-content {{
            height: calc(100vh - 120px) !important;
        }}
        /* Target all pyvis network elements for proper expansion */
        .graph-container.expanded .graph-content > div,
        .graph-container.expanded #mynetworkid,
        .graph-container.expanded .card-body,
        .graph-container.expanded #vis-network,
        .graph-container.expanded canvas {{
            height: 100% !important;
            width: 100% !important;
        }}
        /* Ensure pyvis container divs expand properly */
        .graph-content > div,
        #mynetworkid,
        .card-body,
        #vis-network {{
            height: 100%;
            width: 100%;
        }}
        /* Make sure canvas fills its container but allows tooltips */
        canvas {{
            display: block;
            position: relative;
        }}
        /* Fix tooltip positioning */
        .vis-tooltip {{
            position: absolute !important;
            z-index: 1000 !important;
            pointer-events: none !important;
        }}
        .graph-controls {{
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 5px;
        }}
        .control-btn {{
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }}
        .control-btn:hover {{
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .depth-filter {{
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.95);
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .depth-filter label {{
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin: 0;
        }}
        .depth-filter select {{
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }}
        .depth-filter select:focus {{
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }}
        .hover-display {{
            position: absolute;
            top: 60px;
            left: 10px;
            z-index: 10;
            background: rgba(255,255,255,0.95);
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-size: 13px;
            min-width: 250px;
            max-width: 350px;
        }}
        .hover-display h4 {{
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }}
        .hover-topic {{
            font-weight: bold;
            color: #007bff;
            margin-bottom: 4px;
        }}
        .hover-depth {{
            color: #666;
            font-size: 12px;
            margin-bottom: 6px;
        }}
        .hover-description {{
            color: #333;
            line-height: 1.4;
            font-size: 12px;
        }}
        .hover-placeholder {{
            color: #999;
            font-style: italic;
            font-size: 12px;
        }}
        .graph-content {{
            height: 500px;
            transition: height 0.3s ease;
            position: relative;
            overflow: hidden;
        }}
        /* Ensure pyvis elements fill the graph-content container */
        .graph-content > div {{
            height: 100% !important;
            width: 100% !important;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }}
        th {{
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }}
        th.sortable {{
            cursor: pointer;
            user-select: none;
            position: relative;
        }}
        th.sortable:hover {{
            background-color: #e9ecef;
        }}
        th.sortable::after {{
            content: ' ↕️';
            font-size: 12px;
            opacity: 0.5;
        }}
        th.sortable.sort-asc::after {{
            content: ' ↑';
            opacity: 1;
        }}
        th.sortable.sort-desc::after {{
            content: ' ↓';
            opacity: 1;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        tr:hover {{
            background-color: #f0f0f0;
        }}
        .legend {{
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 5px;
        }}
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }}
        .stats {{
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }}
        .stat-item {{
            text-align: center;
        }}
        .stat-number {{
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }}
        .stat-label {{
            font-size: 14px;
            color: #666;
        }}
        .overlay {{
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }}
        .overlay.active {{
            display: block;
        }}
    </style>
</head>
<body>
    <div class="overlay" id="overlay" onclick="toggleExpand()"></div>

    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{len(topics)}</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{max([t[1] for t in topics]) + 1 if topics else 0}</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{sum(1 for t in topics if json.loads(t[4] or '[]'))}</div>
                <div class="stat-label">Topics with Prerequisites</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>

            <div class="graph-container" id="graphContainer">
                <div class="depth-filter">
                    <label for="depthSelect">Max Depth:</label>
                    <select id="depthSelect" onchange="filterByDepth()">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
                <div class="hover-display" id="hoverDisplay">
                    <h4>Topic Information</h4>
                    <div class="hover-placeholder">Hover over a topic to see details</div>
                </div>
                <div class="graph-controls">
                    <button class="control-btn" onclick="toggleExpand()" id="expandBtn">
                        🔍 Expand
                    </button>
                    <button class="control-btn" onclick="resetGraph()">
                        🔄 Reset View
                    </button>
                </div>
                <div class="graph-content">
                    {network_div}
                </div>
            </div>
        </div>

        {table_section}

        <div class="section">
            <h2>🎮 Graph Controls & Navigation</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                <div>
                    <h3 style="color: #555; margin-bottom: 10px;">🖱️ Mouse Controls</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Pan/Move:</strong> Click and drag on empty space</li>
                        <li><strong>Zoom:</strong> Mouse wheel or trackpad scroll</li>
                        <li><strong>Select Node:</strong> Click on any topic node</li>
                        <li><strong>Drag Node:</strong> Click and drag a node to reposition</li>
                        <li><strong>Multi-select:</strong> Ctrl+Click to select multiple nodes</li>
                        <li><strong>Box Select:</strong> Ctrl+Drag to select area</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">⌨️ Keyboard Shortcuts</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Escape:</strong> Close expanded view</li>
                        <li><strong>Delete:</strong> Remove selected nodes (visual only)</li>
                        <li><strong>Ctrl+A:</strong> Select all nodes</li>
                        <li><strong>Space:</strong> Fit graph to view</li>
                    </ul>
                </div>

                <div>
                    <h3 style="color: #555; margin-bottom: 10px;">🔧 Control Buttons</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>🔍 Expand:</strong> Enter fullscreen mode for detailed exploration</li>
                        <li><strong>🔄 Reset View:</strong> Fit all nodes to view and reset zoom</li>
                        <li><strong>Max Depth:</strong> Filter topics by maximum depth level</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">📌 Node Interactions</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Hover:</strong> View topic description and depth</li>
                        <li><strong>Pin/Unpin:</strong> Double-click to fix node position</li>
                        <li><strong>Physics:</strong> Nodes automatically arrange themselves</li>
                        <li><strong>Clustering:</strong> Related topics group together</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">🎯 Visual Features</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Color Coding:</strong> Each depth level has unique color</li>
                        <li><strong>Node Size:</strong> Larger nodes = shallower depth (more fundamental)</li>
                        <li><strong>Edge Direction:</strong> Arrows point from topic to prerequisites</li>
                        <li><strong>Smooth Animations:</strong> Physics-based movement and transitions</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 8px; border-left: 4px solid #007bff;">
                <h4 style="margin: 0 0 10px 0; color: #0056b3;">💡 Pro Tips</h4>
                <ul style="margin: 0; line-height: 1.6;">
                    <li><strong>Large Graphs:</strong> Use depth filter to focus on specific levels</li>
                    <li><strong>Exploration:</strong> Start with depth 1-2, then expand as needed</li>
                    <li><strong>Performance:</strong> Lower depths = faster rendering and interaction</li>
                    <li><strong>Fullscreen:</strong> Use expand mode for complex topic trees</li>
                    <li><strong>Navigation:</strong> Follow prerequisite arrows to understand learning paths</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function toggleExpand() {{
            const container = document.getElementById('graphContainer');
            const overlay = document.getElementById('overlay');
            const expandBtn = document.getElementById('expandBtn');

            if (container.classList.contains('expanded')) {{
                container.classList.remove('expanded');
                overlay.classList.remove('active');
                expandBtn.innerHTML = '🔍 Expand';
                document.body.style.overflow = 'auto';

                // Force resize of all network elements after collapse
                setTimeout(() => {{
                    resizeNetworkElements();
                }}, 300);
            }} else {{
                container.classList.add('expanded');
                overlay.classList.add('active');
                expandBtn.innerHTML = '❌ Close';
                document.body.style.overflow = 'hidden';

                // Force resize of all network elements after expansion
                setTimeout(() => {{
                    resizeNetworkElements();
                }}, 300);
            }}
        }}

        function resizeNetworkElements() {{
            // Force resize of all pyvis network elements
            const networkElements = [
                document.getElementById('mynetworkid'),
                document.querySelector('.card-body'),
                document.querySelector('#vis-network'),
                document.querySelector('canvas')
            ];

            networkElements.forEach(element => {{
                if (element) {{
                    // Trigger a reflow by temporarily changing display
                    const originalDisplay = element.style.display;
                    element.style.display = 'none';
                    element.offsetHeight; // Trigger reflow
                    element.style.display = originalDisplay;
                }}
            }});

            // Redraw and fit the network
            if (window.network) {{
                if (window.network.redraw) {{
                    window.network.redraw();
                }}
                if (window.network.fit) {{
                    window.network.fit();
                }}
                // Force canvas resize if available
                if (window.network.canvas && window.network.canvas.setSize) {{
                    const container = document.querySelector('.graph-content');
                    if (container) {{
                        window.network.canvas.setSize(container.offsetWidth, container.offsetHeight);
                    }}
                }}
            }}
        }}

        function resetGraph() {{
            // Reset the network view and ensure proper sizing
            resizeNetworkElements();
        }}

        // Close expanded view with Escape key
        document.addEventListener('keydown', function(event) {{
            if (event.key === 'Escape') {{
                const container = document.getElementById('graphContainer');
                if (container.classList.contains('expanded')) {{
                    toggleExpand();
                }}
            }}
        }});

        // Handle window resize
        window.addEventListener('resize', function() {{
            setTimeout(() => {{
                resizeNetworkElements();
            }}, 100);
        }});

        // Initial resize after page load
        window.addEventListener('load', function() {{
            setTimeout(() => {{
                resizeNetworkElements();
                initializeDepthFilter();
                initializeHoverDisplay();
                sortTable(0); // Sort by ID by default
            }}, 500);
        }});

        // Store original topics data for filtering
        let allTopicsData = {json.dumps(topics)};
        let currentMaxDepth = 2; // Default to depth 2

        function initializeDepthFilter() {{
            const depthSelect = document.getElementById('depthSelect');
            const maxDepth = Math.max(...allTopicsData.map(t => t[1])); // Get max depth from data

            // Populate dropdown options
            for (let i = 0; i <= maxDepth; i++) {{
                const option = document.createElement('option');
                option.value = i;
                option.textContent = i;
                if (i === currentMaxDepth) {{
                    option.selected = true;
                }}
                depthSelect.appendChild(option);
            }}

            // Apply initial filter
            filterByDepth();
        }}

        function filterByDepth() {{
            const depthSelect = document.getElementById('depthSelect');
            currentMaxDepth = parseInt(depthSelect.value);

            // Filter topics by selected max depth
            const filteredTopics = allTopicsData.filter(topic => topic[1] <= currentMaxDepth);

            // Rebuild the network with filtered data
            rebuildNetwork(filteredTopics);
        }}

        function rebuildNetwork(filteredTopics) {{
            if (!window.network) return;

            // Clear existing network
            window.network.setData({{nodes: [], edges: []}});

            // Color scheme for different depths
            const depthColors = {{
                0: "#ff6b6b",  // Red for root
                1: "#4ecdc4",  // Teal for depth 1
                2: "#45b7d1",  // Blue for depth 2
                3: "#96ceb4",  // Green for depth 3
                4: "#feca57",  // Yellow for depth 4
                5: "#ff9ff3",  // Pink for depth 5
            }};

            // Create nodes without titles (using hover display instead)
            const nodes = filteredTopics.map(topic => {{
                const [id, depth, term, description] = topic;
                const color = depthColors[depth] || "#cccccc";
                return {{
                    id: term,
                    label: term,
                    color: color,
                    size: 20 + (5 - depth) * 5
                }};
            }});

            // Create edges with arrows
            const edges = [];
            filteredTopics.forEach(topic => {{
                const [id, depth, term, description, prereqsJson] = topic;
                try {{
                    const prerequisites = JSON.parse(prereqsJson || '[]');
                    prerequisites.forEach(prereq => {{
                        // Only add edge if prerequisite is also in filtered data
                        if (filteredTopics.some(t => t[2] === prereq)) {{
                            edges.push({{
                                from: term,
                                to: prereq,
                                color: "blue",
                                width: 2,
                                arrows: {{
                                    to: {{
                                        enabled: true,
                                        scaleFactor: 1.2
                                    }}
                                }}
                            }});
                        }}
                    }});
                }} catch (e) {{
                    console.error('Error parsing prerequisites for', term, e);
                }}
            }});

            // Update network
            window.network.setData({{nodes: nodes, edges: edges}});
            window.network.fit();

            // Reinitialize hover events after rebuilding
            initializeHoverDisplay();
        }}

        function initializeHoverDisplay() {{
            // Set up hover event listeners for the network
            if (window.network) {{
                window.network.on("hoverNode", function(params) {{
                    const nodeId = params.node;
                    const topicData = allTopicsData.find(t => t[2] === nodeId);

                    if (topicData) {{
                        const [id, depth, term, description, prereqsJson] = topicData;
                        let prerequisites = [];
                        try {{
                            prerequisites = JSON.parse(prereqsJson || '[]');
                        }} catch (e) {{
                            prerequisites = [];
                        }}

                        updateHoverDisplay(term, depth, description, prerequisites);
                    }}
                }});

                window.network.on("blurNode", function(params) {{
                    resetHoverDisplay();
                }});
            }}
        }}

        function updateHoverDisplay(term, depth, description, prerequisites) {{
            const hoverDisplay = document.getElementById('hoverDisplay');
            const prereqsText = prerequisites.length > 0 ? prerequisites.join(', ') : 'None';

            hoverDisplay.innerHTML = `
                <h4>Topic Information</h4>
                <div class="hover-topic">${{term}}</div>
                <div class="hover-depth">Depth: ${{depth}}${{depth === 0 ? ' (Root)' : ''}}</div>
                <div class="hover-description">${{description || 'No description available'}}</div>
                <div style="margin-top: 8px; font-size: 11px; color: #666;">
                    <strong>Prerequisites:</strong> ${{prereqsText}}
                </div>
            `;
        }}

        function resetHoverDisplay() {{
            const hoverDisplay = document.getElementById('hoverDisplay');
            hoverDisplay.innerHTML = `
                <h4>Topic Information</h4>
                <div class="hover-placeholder">Hover over a topic to see details</div>
            `;
        }}

        let sortDirection = {{}};

        function sortTable(columnIndex) {{
            const table = document.querySelector('table tbody');
            const rows = Array.from(table.rows);
            const header = document.querySelectorAll('th.sortable')[columnIndex];

            // Toggle sort direction
            const currentDirection = sortDirection[columnIndex] || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            sortDirection[columnIndex] = newDirection;

            // Update header classes
            document.querySelectorAll('th.sortable').forEach(th => {{
                th.classList.remove('sort-asc', 'sort-desc');
            }});
            header.classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');

            // Sort rows
            rows.sort((a, b) => {{
                let aVal = a.cells[columnIndex].textContent.trim();
                let bVal = b.cells[columnIndex].textContent.trim();

                // Convert to numbers for ID and Depth columns
                if (columnIndex === 0 || columnIndex === 1) {{
                    aVal = parseInt(aVal) || 0;
                    bVal = parseInt(bVal) || 0;
                }}

                if (aVal < bVal) return newDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return newDirection === 'asc' ? 1 : -1;
                return 0;
            }});

            // Reorder table rows
            rows.forEach(row => table.appendChild(row));
        }}
    </script>

    {scripts}
</body>
</html>
        """

        return complete_html

    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get caching and API usage statistics."""
        total_requests = self.api_call_count + self.cache_hits
        cache_hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            "api_calls": self.api_call_count,
            "cache_hits": self.cache_hits,
            "total_requests": total_requests,
            "cache_hit_rate": f"{cache_hit_rate:.1f}%",
            "cached_terms": len(self.response_cache)
        }

    def clear_cache(self) -> None:
        """Clear the response cache."""
        self.response_cache.clear()
        self.cache_hits = 0
        self.logger.info("Response cache cleared")

    def clear_database_cache(self) -> None:
        """Delete all existing topic entries from the database."""
        try:
            cursor = self.conn.execute("DELETE FROM topics")
            deleted_count = cursor.rowcount
            self.conn.commit()
            self.logger.info(f"Cleared database cache: deleted {deleted_count} topic entries")
        except sqlite3.Error as e:
            self.logger.error(f"Error clearing database cache: {e}")
            raise

    def get_term_from_database(self, term: str) -> Optional[Tuple[str, List[str]]]:
        """
        Get term information from the database if it exists.

        Args:
            term: The term to look up

        Returns:
            Tuple of (description, dependencies) if found, None otherwise
        """
        try:
            cursor = self.conn.execute(
                """SELECT description, prerequisites
                   FROM topics WHERE term = ?""",
                (term.strip(),)
            )
            result = cursor.fetchone()

            if result:
                description, prereqs_json = result

                # Parse prerequisites
                prerequisites = json.loads(prereqs_json) if prereqs_json else []

                self.logger.info(f"Found cached term '{term}' in database with {len(prerequisites)} prerequisites")
                return description or f"Description for {term}", prerequisites

            return None

        except (sqlite3.Error, json.JSONDecodeError) as e:
            self.logger.error(f"Error retrieving term '{term}' from database: {e}")
            return None

    def get_all_existing_terms(self) -> List[str]:
        """
        Get all existing terms from the database for deduplication.

        Returns:
            List of all term names currently in the database
        """
        try:
            cursor = self.conn.execute("SELECT term FROM topics ORDER BY term")
            terms = [row[0] for row in cursor.fetchall()]
            return terms
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching existing terms: {e}")
            return []

    def close(self) -> None:
        """Close the database connection and log statistics."""
        if self.conn:
            self.conn.close()

        # Log final statistics
        stats = self.get_cache_statistics()
        self.logger.info(f"Session complete - API calls: {stats['api_calls']}, "
                        f"Cache hits: {stats['cache_hits']}, "
                        f"Cache hit rate: {stats['cache_hit_rate']}")
        self.logger.info("Database connection closed")


def main():
    """Main CLI interface for the topic tree builder."""
    parser = argparse.ArgumentParser(
        description="Build and visualize a topic dependency tree",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python topic_graph.py --root "Machine Learning"
  python topic_graph.py --root "Statistics" --max-depth 3
  python topic_graph.py --root "Programming" --output custom_graph.html
        """
    )

    parser.add_argument(
        "--root", "-r",
        required=True,
        help="Root term to start building the topic tree from"
    )

    parser.add_argument(
        "--max-depth", "-d",
        type=int,
        default=5,
        help="Maximum depth to traverse (default: 5, meaning depths 0-5)"
    )

    parser.add_argument(
        "--output", "-o",
        default="graph.html",
        help="Output HTML file for the graph visualization (default: graph.html)"
    )

    parser.add_argument(
        "--db-path",
        default="topics.db",
        help="Path to the SQLite database file (default: topics.db)"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    parser.add_argument(
        "--fallback-only",
        action="store_true",
        help="Use only fallback data (skip OpenAI API calls)"
    )

    parser.add_argument(
        "--clear-cache",
        action="store_true",
        help="Delete all existing topic entries from database before starting (default: use cached data)"
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create and run the topic tree builder
    builder = None
    try:
        print(f"Building topic tree starting from: {args.root}")
        print(f"Maximum depth: {args.max_depth}")
        print(f"Database: {args.db_path}")
        print(f"Output: {args.output}")
        print("-" * 50)

        builder = TopicTreeBuilder(args.db_path, args.fallback_only)

        # Clear database cache if requested
        if args.clear_cache:
            builder.clear_database_cache()

        builder.build_topic_tree(args.root, args.max_depth)
        builder.generate_graph_visualization(args.output)

        # Display statistics
        stats = builder.get_cache_statistics()

        print("-" * 50)
        print(f"✅ Topic tree building completed!")
        print(f"📊 Graph visualization saved to: {args.output}")
        print(f"💾 Database saved to: {args.db_path}")
        print(f"🔄 API calls made: {stats['api_calls']}")
        print(f"💰 Cache hit rate: {stats['cache_hit_rate']}")
        print(f"📝 Terms cached: {stats['cached_terms']}")

    except KeyboardInterrupt:
        print("\n❌ Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
    finally:
        if builder:
            builder.close()


if __name__ == "__main__":
    main()


