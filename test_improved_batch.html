
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .graph-container {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .graph-container.expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1000;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .graph-container.expanded .graph-content {
            height: calc(100vh - 120px) !important;
        }
        /* Target all pyvis network elements for proper expansion */
        .graph-container.expanded .graph-content > div,
        .graph-container.expanded #mynetworkid,
        .graph-container.expanded .card-body,
        .graph-container.expanded #vis-network,
        .graph-container.expanded canvas {
            height: 100% !important;
            width: 100% !important;
        }
        /* Ensure pyvis container divs expand properly */
        .graph-content > div,
        #mynetworkid,
        .card-body,
        #vis-network {
            height: 100%;
            width: 100%;
        }
        /* Make sure canvas fills its container but allows tooltips */
        canvas {
            display: block;
            position: relative;
        }
        /* Fix tooltip positioning */
        .vis-tooltip {
            position: absolute !important;
            z-index: 1000 !important;
            pointer-events: none !important;
        }
        .graph-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 5px;
        }
        .control-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .control-btn:hover {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .depth-filter {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.95);
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .depth-filter label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin: 0;
        }
        .depth-filter select {
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }
        .depth-filter select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .hover-display {
            position: absolute;
            top: 60px;
            left: 10px;
            z-index: 10;
            background: rgba(255,255,255,0.95);
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-size: 13px;
            min-width: 250px;
            max-width: 350px;
        }
        .hover-display h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        .hover-topic {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 4px;
        }
        .hover-depth {
            color: #666;
            font-size: 12px;
            margin-bottom: 6px;
        }
        .hover-description {
            color: #333;
            line-height: 1.4;
            font-size: 12px;
        }
        .hover-placeholder {
            color: #999;
            font-style: italic;
            font-size: 12px;
        }
        .graph-content {
            height: 500px;
            transition: height 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        /* Ensure pyvis elements fill the graph-content container */
        .graph-content > div {
            height: 100% !important;
            width: 100% !important;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        th.sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
        }
        th.sortable:hover {
            background-color: #e9ecef;
        }
        th.sortable::after {
            content: ' ↕️';
            font-size: 12px;
            opacity: 0.5;
        }
        th.sortable.sort-asc::after {
            content: ' ↑';
            opacity: 1;
        }
        th.sortable.sort-desc::after {
            content: ' ↓';
            opacity: 1;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f0f0;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="overlay" id="overlay" onclick="toggleExpand()"></div>

    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">55</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">7</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">55</div>
                <div class="stat-label">Topics with Prerequisites</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>

            <div class="graph-container" id="graphContainer">
                <div class="depth-filter">
                    <label for="depthSelect">Max Depth:</label>
                    <select id="depthSelect" onchange="filterByDepth()">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
                <div class="hover-display" id="hoverDisplay">
                    <h4>Topic Information</h4>
                    <div class="hover-placeholder">Hover over a topic to see details</div>
                </div>
                <div class="graph-controls">
                    <button class="control-btn" onclick="toggleExpand()" id="expandBtn">
                        🔍 Expand
                    </button>
                    <button class="control-btn" onclick="resetGraph()">
                        🔄 Reset View
                    </button>
                </div>
                <div class="graph-content">
                    <div id="mynetwork" class="card-body"></div>
                </div>
            </div>
        </div>

        
        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th class="sortable" onclick="sortTable(0)">ID</th>
                        <th class="sortable" onclick="sortTable(1)">Depth</th>
                        <th class="sortable" onclick="sortTable(2)">Term</th>
                        <th>Description</th>
                        <th>Prerequisites</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">1</td>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Data Science</strong></td>
                    <td>Field involving data analysis, visualization, and interpretation.</td>
                    <td>Statistics, Programming, Data Analysis, Machine Learning, Data Visualization, Mathematics, Database Management, Probability, Algorithms</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">2</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Statistics</strong></td>
                    <td>Study of data collection, analysis, interpretation, and presentation.</td>
                    <td>Mathematics, Probability, Data Analysis, Data Science, Algebra</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">3</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Probability</strong></td>
                    <td>Mathematical study of random events and uncertainty.</td>
                    <td>Mathematics, Statistics, Combinatorics, Set Theory, Algebra</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">4</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Machine Learning</strong></td>
                    <td>Algorithms that improve through experience and data.</td>
                    <td>Statistics, Probability, Programming, Algorithms, Data Science</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">5</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Data Visualization</strong></td>
                    <td>Graphical representation of data insights.</td>
                    <td>Statistics, Data Analysis, Design Principles, Programming, Data Science</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">6</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Database Management</strong></td>
                    <td>Creation and maintenance of databases.</td>
                    <td>Data Science, SQL, Data Modeling, Programming, Information Systems</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">7</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Mathematics</strong></td>
                    <td>Study of numbers, quantities, and shapes.</td>
                    <td>Arithmetic, Algebra, Geometry, Calculus, Logic</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">8</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Programming</strong></td>
                    <td>Writing code to create software applications.</td>
                    <td>Logic, Algorithms, Data Structures, Mathematics, Problem Solving</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">9</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Algorithms</strong></td>
                    <td>Step-by-step problem-solving procedures.</td>
                    <td>Programming, Mathematics, Logic, Data Structures, Problem Solving</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">10</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Data Analysis</strong></td>
                    <td>Process of inspecting and modeling data.</td>
                    <td>Statistics, Data Science, Programming, Mathematics, Data Cleaning</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">11</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Combinatorics</strong></td>
                    <td>Study of counting and arrangement of objects.</td>
                    <td>Mathematics, Set Theory, Logic, Probability, Algebra</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">12</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Arithmetic</strong></td>
                    <td>Basic operations of numbers.</td>
                    <td>Mathematics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">13</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Data Structures</strong></td>
                    <td>Organizing and storing data efficiently.</td>
                    <td>Programming, Algorithms, Mathematics, Logic, Data Analysis</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">14</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Data Modeling</strong></td>
                    <td>Designing data representations and structures.</td>
                    <td>Data Analysis, Database Management, Statistics, Mathematics, Programming</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">15</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Problem Solving</strong></td>
                    <td>Finding solutions to complex issues.</td>
                    <td>Logic, Mathematics, Algorithms, Data Analysis, Programming</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">16</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Data Cleaning</strong></td>
                    <td>Preparing data for analysis by removing errors.</td>
                    <td>Data Analysis, Statistics, Programming, Database Management, Data Science</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">17</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Information Systems</strong></td>
                    <td>Systems for managing and processing information.</td>
                    <td>Database Management, Programming, Data Analysis, Logic, Design Principles</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">18</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>SQL</strong></td>
                    <td>Language for managing and querying databases.</td>
                    <td>Database Management, Programming, Data Analysis, Logic, Mathematics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">19</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Calculus</strong></td>
                    <td>Study of change and motion.</td>
                    <td>Mathematics, Algebra, Geometry, Logic, Set Theory</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">20</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Algebra</strong></td>
                    <td>Study of mathematical symbols and rules.</td>
                    <td>Mathematics, Arithmetic, Logic, Set Theory, Geometry</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">21</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Logic</strong></td>
                    <td>Principles of valid reasoning.</td>
                    <td>Mathematics, Set Theory, Algebra, Philosophy, Programming</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">22</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Design Principles</strong></td>
                    <td>Guidelines for creating effective designs.</td>
                    <td>Logic, Mathematics, Programming, Data Analysis, Problem Solving</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">23</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Set Theory</strong></td>
                    <td>Study of sets and their properties.</td>
                    <td>Mathematics, Logic, Algebra, Philosophy, Probability</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">24</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Geometry</strong></td>
                    <td>Study of shapes, sizes, and properties of space.</td>
                    <td>Mathematics, Algebra, Logic, Set Theory, Arithmetic</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">25</td>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Philosophy</strong></td>
                    <td>Study of fundamental nature of knowledge, reality, and existence.</td>
                    <td>Logic, Critical Thinking, Ethics, History, Metaphysics, Epistemology, Language, Culture, Science, Reasoning</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">26</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Epistemology</strong></td>
                    <td>Study of knowledge and belief.</td>
                    <td>Philosophy, Logic, Reasoning, Critical Thinking, History of Ideas</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">27</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>History</strong></td>
                    <td>Study of past events.</td>
                    <td>Chronology, Critical Thinking, Geography, Culture, Historical Sources</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">28</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Culture</strong></td>
                    <td>Shared beliefs and practices.</td>
                    <td>Anthropology, Sociology, History, Language, Traditions</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">29</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Metaphysics</strong></td>
                    <td>Study of reality and existence.</td>
                    <td>Philosophy, Logic, Ontology, Epistemology, Reasoning</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">30</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Ethics</strong></td>
                    <td>Study of moral principles.</td>
                    <td>Philosophy, Moral Philosophy, Reasoning, Critical Thinking, Culture</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">31</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Language</strong></td>
                    <td>System of communication.</td>
                    <td>Linguistics, Grammar, Phonetics, Semantics, Culture</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">32</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Critical Thinking</strong></td>
                    <td>Analyzing facts to form a judgment.</td>
                    <td>Logic, Reasoning, Problem Solving, Philosophy, Cognitive Skills</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">33</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Science</strong></td>
                    <td>Systematic study of the natural world.</td>
                    <td>Mathematics, Observation, Experimentation, Data Analysis, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">34</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Reasoning</strong></td>
                    <td>Process of forming conclusions.</td>
                    <td>Logic, Critical Thinking, Problem Solving, Philosophy, Cognitive Skills</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">35</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Chronology</strong></td>
                    <td>Study of time sequences</td>
                    <td>History, Mathematics, Logic, Reasoning, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">36</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>History of Ideas</strong></td>
                    <td>Evolution of human thought</td>
                    <td>Philosophy, History, Culture, Epistemology, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">37</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Cognitive Skills</strong></td>
                    <td>Mental processes for understanding</td>
                    <td>Psychology, Neuroscience, Critical Thinking, Reasoning, Problem Solving</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">38</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Phonetics</strong></td>
                    <td>Study of speech sounds</td>
                    <td>Linguistics, Language, Biology, Acoustics, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">39</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Anthropology</strong></td>
                    <td>Study of human societies</td>
                    <td>Culture, History, Biology, Sociology, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">40</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Geography</strong></td>
                    <td>Study of Earth's features</td>
                    <td>Earth Science, History, Mathematics, Culture, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">41</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Traditions</strong></td>
                    <td>Cultural beliefs and practices</td>
                    <td>Culture, History, Anthropology, Sociology, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">42</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Moral Philosophy</strong></td>
                    <td>Study of ethics and morality</td>
                    <td>Philosophy, Ethics, Logic, Critical Thinking, Epistemology</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">43</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Observation</strong></td>
                    <td>Act of noticing details</td>
                    <td>Science, Critical Thinking, Reasoning, Biology, Psychology</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">44</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Semantics</strong></td>
                    <td>Meaning in language</td>
                    <td>Linguistics, Language, Logic, Philosophy, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">45</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Linguistics</strong></td>
                    <td>Scientific study of language</td>
                    <td>Language, Phonetics, Semantics, Critical Thinking, Philosophy</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">46</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Ontology</strong></td>
                    <td>Study of being and existence</td>
                    <td>Philosophy, Metaphysics, Logic, Epistemology, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">47</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Experimentation</strong></td>
                    <td>Testing hypotheses scientifically</td>
                    <td>Science, Mathematics, Logic, Reasoning, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">48</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Grammar</strong></td>
                    <td>Rules of language structure</td>
                    <td>Language, Linguistics, Logic, Critical Thinking, Phonetics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">49</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Historical Sources</strong></td>
                    <td>Documents from the past</td>
                    <td>History, Critical Thinking, Epistemology, Culture, Reasoning</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">50</td>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Sociology</strong></td>
                    <td>Study of social behavior</td>
                    <td>Culture, Anthropology, Psychology, Critical Thinking, History</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">51</td>
                    <td style="background-color: #cccccc; color: white; font-weight: bold; text-align: center;">6</td>
                    <td><strong>Biology</strong></td>
                    <td>Study of living organisms and life processes.</td>
                    <td>Science, Chemistry, Physics, Mathematics, Observation, Experimentation, Critical Thinking, Anatomy, Ecology, Genetics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">52</td>
                    <td style="background-color: #cccccc; color: white; font-weight: bold; text-align: center;">6</td>
                    <td><strong>Neuroscience</strong></td>
                    <td>Study of the nervous system and brain.</td>
                    <td>Biology, Chemistry, Physics, Mathematics, Anatomy, Psychology, Cognitive Skills, Observation, Experimentation, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">53</td>
                    <td style="background-color: #cccccc; color: white; font-weight: bold; text-align: center;">6</td>
                    <td><strong>Psychology</strong></td>
                    <td>Study of mind and behavior.</td>
                    <td>Biology, Cognitive Skills, Critical Thinking, Observation, Experimentation, Ethics, Sociology, Anthropology, Philosophy, Statistics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">54</td>
                    <td style="background-color: #cccccc; color: white; font-weight: bold; text-align: center;">6</td>
                    <td><strong>Acoustics</strong></td>
                    <td>Science of sound and its properties.</td>
                    <td>Physics, Mathematics, Science, Critical Thinking, Experimentation, Observation, Data Analysis, Geometry, Algebra, Statistics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">55</td>
                    <td style="background-color: #cccccc; color: white; font-weight: bold; text-align: center;">6</td>
                    <td><strong>Earth Science</strong></td>
                    <td>Study of Earth's structure and processes.</td>
                    <td>Geography, Geology, Physics, Chemistry, Biology, Mathematics, Observation, Experimentation, Critical Thinking, Data Analysis</td>
                </tr>
                
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🎮 Graph Controls & Navigation</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                <div>
                    <h3 style="color: #555; margin-bottom: 10px;">🖱️ Mouse Controls</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Pan/Move:</strong> Click and drag on empty space</li>
                        <li><strong>Zoom:</strong> Mouse wheel or trackpad scroll</li>
                        <li><strong>Select Node:</strong> Click on any topic node</li>
                        <li><strong>Drag Node:</strong> Click and drag a node to reposition</li>
                        <li><strong>Multi-select:</strong> Ctrl+Click to select multiple nodes</li>
                        <li><strong>Box Select:</strong> Ctrl+Drag to select area</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">⌨️ Keyboard Shortcuts</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Escape:</strong> Close expanded view</li>
                        <li><strong>Delete:</strong> Remove selected nodes (visual only)</li>
                        <li><strong>Ctrl+A:</strong> Select all nodes</li>
                        <li><strong>Space:</strong> Fit graph to view</li>
                    </ul>
                </div>

                <div>
                    <h3 style="color: #555; margin-bottom: 10px;">🔧 Control Buttons</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>🔍 Expand:</strong> Enter fullscreen mode for detailed exploration</li>
                        <li><strong>🔄 Reset View:</strong> Fit all nodes to view and reset zoom</li>
                        <li><strong>Max Depth:</strong> Filter topics by maximum depth level</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">📌 Node Interactions</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Hover:</strong> View topic description and depth</li>
                        <li><strong>Pin/Unpin:</strong> Double-click to fix node position</li>
                        <li><strong>Physics:</strong> Nodes automatically arrange themselves</li>
                        <li><strong>Clustering:</strong> Related topics group together</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">🎯 Visual Features</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Color Coding:</strong> Each depth level has unique color</li>
                        <li><strong>Node Size:</strong> Larger nodes = shallower depth (more fundamental)</li>
                        <li><strong>Edge Direction:</strong> Arrows point from topic to prerequisites</li>
                        <li><strong>Smooth Animations:</strong> Physics-based movement and transitions</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 8px; border-left: 4px solid #007bff;">
                <h4 style="margin: 0 0 10px 0; color: #0056b3;">💡 Pro Tips</h4>
                <ul style="margin: 0; line-height: 1.6;">
                    <li><strong>Large Graphs:</strong> Use depth filter to focus on specific levels</li>
                    <li><strong>Exploration:</strong> Start with depth 1-2, then expand as needed</li>
                    <li><strong>Performance:</strong> Lower depths = faster rendering and interaction</li>
                    <li><strong>Fullscreen:</strong> Use expand mode for complex topic trees</li>
                    <li><strong>Navigation:</strong> Follow prerequisite arrows to understand learning paths</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function toggleExpand() {
            const container = document.getElementById('graphContainer');
            const overlay = document.getElementById('overlay');
            const expandBtn = document.getElementById('expandBtn');

            if (container.classList.contains('expanded')) {
                container.classList.remove('expanded');
                overlay.classList.remove('active');
                expandBtn.innerHTML = '🔍 Expand';
                document.body.style.overflow = 'auto';

                // Force resize of all network elements after collapse
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            } else {
                container.classList.add('expanded');
                overlay.classList.add('active');
                expandBtn.innerHTML = '❌ Close';
                document.body.style.overflow = 'hidden';

                // Force resize of all network elements after expansion
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            }
        }

        function resizeNetworkElements() {
            // Force resize of all pyvis network elements
            const networkElements = [
                document.getElementById('mynetworkid'),
                document.querySelector('.card-body'),
                document.querySelector('#vis-network'),
                document.querySelector('canvas')
            ];

            networkElements.forEach(element => {
                if (element) {
                    // Trigger a reflow by temporarily changing display
                    const originalDisplay = element.style.display;
                    element.style.display = 'none';
                    element.offsetHeight; // Trigger reflow
                    element.style.display = originalDisplay;
                }
            });

            // Redraw and fit the network
            if (window.network) {
                if (window.network.redraw) {
                    window.network.redraw();
                }
                if (window.network.fit) {
                    window.network.fit();
                }
                // Force canvas resize if available
                if (window.network.canvas && window.network.canvas.setSize) {
                    const container = document.querySelector('.graph-content');
                    if (container) {
                        window.network.canvas.setSize(container.offsetWidth, container.offsetHeight);
                    }
                }
            }
        }

        function resetGraph() {
            // Reset the network view and ensure proper sizing
            resizeNetworkElements();
        }

        // Close expanded view with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const container = document.getElementById('graphContainer');
                if (container.classList.contains('expanded')) {
                    toggleExpand();
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            setTimeout(() => {
                resizeNetworkElements();
            }, 100);
        });

        // Initial resize after page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                resizeNetworkElements();
                initializeDepthFilter();
                initializeHoverDisplay();
                sortTable(0); // Sort by ID by default
            }, 500);
        });

        // Store original topics data for filtering
        let allTopicsData = [[1, 0, "Data Science", "Field involving data analysis, visualization, and interpretation.", "[\"Statistics\", \"Programming\", \"Data Analysis\", \"Machine Learning\", \"Data Visualization\", \"Mathematics\", \"Database Management\", \"Probability\", \"Algorithms\"]"], [2, 1, "Statistics", "Study of data collection, analysis, interpretation, and presentation.", "[\"Mathematics\", \"Probability\", \"Data Analysis\", \"Data Science\", \"Algebra\"]"], [3, 1, "Probability", "Mathematical study of random events and uncertainty.", "[\"Mathematics\", \"Statistics\", \"Combinatorics\", \"Set Theory\", \"Algebra\"]"], [4, 1, "Machine Learning", "Algorithms that improve through experience and data.", "[\"Statistics\", \"Probability\", \"Programming\", \"Algorithms\", \"Data Science\"]"], [5, 1, "Data Visualization", "Graphical representation of data insights.", "[\"Statistics\", \"Data Analysis\", \"Design Principles\", \"Programming\", \"Data Science\"]"], [6, 1, "Database Management", "Creation and maintenance of databases.", "[\"Data Science\", \"SQL\", \"Data Modeling\", \"Programming\", \"Information Systems\"]"], [7, 1, "Mathematics", "Study of numbers, quantities, and shapes.", "[\"Arithmetic\", \"Algebra\", \"Geometry\", \"Calculus\", \"Logic\"]"], [8, 1, "Programming", "Writing code to create software applications.", "[\"Logic\", \"Algorithms\", \"Data Structures\", \"Mathematics\", \"Problem Solving\"]"], [9, 1, "Algorithms", "Step-by-step problem-solving procedures.", "[\"Programming\", \"Mathematics\", \"Logic\", \"Data Structures\", \"Problem Solving\"]"], [10, 1, "Data Analysis", "Process of inspecting and modeling data.", "[\"Statistics\", \"Data Science\", \"Programming\", \"Mathematics\", \"Data Cleaning\"]"], [11, 2, "Combinatorics", "Study of counting and arrangement of objects.", "[\"Mathematics\", \"Set Theory\", \"Logic\", \"Probability\", \"Algebra\"]"], [12, 2, "Arithmetic", "Basic operations of numbers.", "[\"Mathematics\"]"], [13, 2, "Data Structures", "Organizing and storing data efficiently.", "[\"Programming\", \"Algorithms\", \"Mathematics\", \"Logic\", \"Data Analysis\"]"], [14, 2, "Data Modeling", "Designing data representations and structures.", "[\"Data Analysis\", \"Database Management\", \"Statistics\", \"Mathematics\", \"Programming\"]"], [15, 2, "Problem Solving", "Finding solutions to complex issues.", "[\"Logic\", \"Mathematics\", \"Algorithms\", \"Data Analysis\", \"Programming\"]"], [16, 2, "Data Cleaning", "Preparing data for analysis by removing errors.", "[\"Data Analysis\", \"Statistics\", \"Programming\", \"Database Management\", \"Data Science\"]"], [17, 2, "Information Systems", "Systems for managing and processing information.", "[\"Database Management\", \"Programming\", \"Data Analysis\", \"Logic\", \"Design Principles\"]"], [18, 2, "SQL", "Language for managing and querying databases.", "[\"Database Management\", \"Programming\", \"Data Analysis\", \"Logic\", \"Mathematics\"]"], [19, 2, "Calculus", "Study of change and motion.", "[\"Mathematics\", \"Algebra\", \"Geometry\", \"Logic\", \"Set Theory\"]"], [20, 2, "Algebra", "Study of mathematical symbols and rules.", "[\"Mathematics\", \"Arithmetic\", \"Logic\", \"Set Theory\", \"Geometry\"]"], [21, 2, "Logic", "Principles of valid reasoning.", "[\"Mathematics\", \"Set Theory\", \"Algebra\", \"Philosophy\", \"Programming\"]"], [22, 2, "Design Principles", "Guidelines for creating effective designs.", "[\"Logic\", \"Mathematics\", \"Programming\", \"Data Analysis\", \"Problem Solving\"]"], [23, 2, "Set Theory", "Study of sets and their properties.", "[\"Mathematics\", \"Logic\", \"Algebra\", \"Philosophy\", \"Probability\"]"], [24, 2, "Geometry", "Study of shapes, sizes, and properties of space.", "[\"Mathematics\", \"Algebra\", \"Logic\", \"Set Theory\", \"Arithmetic\"]"], [25, 3, "Philosophy", "Study of fundamental nature of knowledge, reality, and existence.", "[\"Logic\", \"Critical Thinking\", \"Ethics\", \"History\", \"Metaphysics\", \"Epistemology\", \"Language\", \"Culture\", \"Science\", \"Reasoning\"]"], [26, 4, "Epistemology", "Study of knowledge and belief.", "[\"Philosophy\", \"Logic\", \"Reasoning\", \"Critical Thinking\", \"History of Ideas\"]"], [27, 4, "History", "Study of past events.", "[\"Chronology\", \"Critical Thinking\", \"Geography\", \"Culture\", \"Historical Sources\"]"], [28, 4, "Culture", "Shared beliefs and practices.", "[\"Anthropology\", \"Sociology\", \"History\", \"Language\", \"Traditions\"]"], [29, 4, "Metaphysics", "Study of reality and existence.", "[\"Philosophy\", \"Logic\", \"Ontology\", \"Epistemology\", \"Reasoning\"]"], [30, 4, "Ethics", "Study of moral principles.", "[\"Philosophy\", \"Moral Philosophy\", \"Reasoning\", \"Critical Thinking\", \"Culture\"]"], [31, 4, "Language", "System of communication.", "[\"Linguistics\", \"Grammar\", \"Phonetics\", \"Semantics\", \"Culture\"]"], [32, 4, "Critical Thinking", "Analyzing facts to form a judgment.", "[\"Logic\", \"Reasoning\", \"Problem Solving\", \"Philosophy\", \"Cognitive Skills\"]"], [33, 4, "Science", "Systematic study of the natural world.", "[\"Mathematics\", \"Observation\", \"Experimentation\", \"Data Analysis\", \"Critical Thinking\"]"], [34, 4, "Reasoning", "Process of forming conclusions.", "[\"Logic\", \"Critical Thinking\", \"Problem Solving\", \"Philosophy\", \"Cognitive Skills\"]"], [35, 5, "Chronology", "Study of time sequences", "[\"History\", \"Mathematics\", \"Logic\", \"Reasoning\", \"Critical Thinking\"]"], [36, 5, "History of Ideas", "Evolution of human thought", "[\"Philosophy\", \"History\", \"Culture\", \"Epistemology\", \"Critical Thinking\"]"], [37, 5, "Cognitive Skills", "Mental processes for understanding", "[\"Psychology\", \"Neuroscience\", \"Critical Thinking\", \"Reasoning\", \"Problem Solving\"]"], [38, 5, "Phonetics", "Study of speech sounds", "[\"Linguistics\", \"Language\", \"Biology\", \"Acoustics\", \"Critical Thinking\"]"], [39, 5, "Anthropology", "Study of human societies", "[\"Culture\", \"History\", \"Biology\", \"Sociology\", \"Critical Thinking\"]"], [40, 5, "Geography", "Study of Earth's features", "[\"Earth Science\", \"History\", \"Mathematics\", \"Culture\", \"Critical Thinking\"]"], [41, 5, "Traditions", "Cultural beliefs and practices", "[\"Culture\", \"History\", \"Anthropology\", \"Sociology\", \"Critical Thinking\"]"], [42, 5, "Moral Philosophy", "Study of ethics and morality", "[\"Philosophy\", \"Ethics\", \"Logic\", \"Critical Thinking\", \"Epistemology\"]"], [43, 5, "Observation", "Act of noticing details", "[\"Science\", \"Critical Thinking\", \"Reasoning\", \"Biology\", \"Psychology\"]"], [44, 5, "Semantics", "Meaning in language", "[\"Linguistics\", \"Language\", \"Logic\", \"Philosophy\", \"Critical Thinking\"]"], [45, 5, "Linguistics", "Scientific study of language", "[\"Language\", \"Phonetics\", \"Semantics\", \"Critical Thinking\", \"Philosophy\"]"], [46, 5, "Ontology", "Study of being and existence", "[\"Philosophy\", \"Metaphysics\", \"Logic\", \"Epistemology\", \"Critical Thinking\"]"], [47, 5, "Experimentation", "Testing hypotheses scientifically", "[\"Science\", \"Mathematics\", \"Logic\", \"Reasoning\", \"Critical Thinking\"]"], [48, 5, "Grammar", "Rules of language structure", "[\"Language\", \"Linguistics\", \"Logic\", \"Critical Thinking\", \"Phonetics\"]"], [49, 5, "Historical Sources", "Documents from the past", "[\"History\", \"Critical Thinking\", \"Epistemology\", \"Culture\", \"Reasoning\"]"], [50, 5, "Sociology", "Study of social behavior", "[\"Culture\", \"Anthropology\", \"Psychology\", \"Critical Thinking\", \"History\"]"], [51, 6, "Biology", "Study of living organisms and life processes.", "[\"Science\", \"Chemistry\", \"Physics\", \"Mathematics\", \"Observation\", \"Experimentation\", \"Critical Thinking\", \"Anatomy\", \"Ecology\", \"Genetics\"]"], [52, 6, "Neuroscience", "Study of the nervous system and brain.", "[\"Biology\", \"Chemistry\", \"Physics\", \"Mathematics\", \"Anatomy\", \"Psychology\", \"Cognitive Skills\", \"Observation\", \"Experimentation\", \"Critical Thinking\"]"], [53, 6, "Psychology", "Study of mind and behavior.", "[\"Biology\", \"Cognitive Skills\", \"Critical Thinking\", \"Observation\", \"Experimentation\", \"Ethics\", \"Sociology\", \"Anthropology\", \"Philosophy\", \"Statistics\"]"], [54, 6, "Acoustics", "Science of sound and its properties.", "[\"Physics\", \"Mathematics\", \"Science\", \"Critical Thinking\", \"Experimentation\", \"Observation\", \"Data Analysis\", \"Geometry\", \"Algebra\", \"Statistics\"]"], [55, 6, "Earth Science", "Study of Earth's structure and processes.", "[\"Geography\", \"Geology\", \"Physics\", \"Chemistry\", \"Biology\", \"Mathematics\", \"Observation\", \"Experimentation\", \"Critical Thinking\", \"Data Analysis\"]"]];
        let currentMaxDepth = 2; // Default to depth 2

        function initializeDepthFilter() {
            const depthSelect = document.getElementById('depthSelect');
            const maxDepth = Math.max(...allTopicsData.map(t => t[1])); // Get max depth from data

            // Populate dropdown options
            for (let i = 0; i <= maxDepth; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = i;
                if (i === currentMaxDepth) {
                    option.selected = true;
                }
                depthSelect.appendChild(option);
            }

            // Apply initial filter
            filterByDepth();
        }

        function filterByDepth() {
            const depthSelect = document.getElementById('depthSelect');
            currentMaxDepth = parseInt(depthSelect.value);

            // Filter topics by selected max depth
            const filteredTopics = allTopicsData.filter(topic => topic[1] <= currentMaxDepth);

            // Rebuild the network with filtered data
            rebuildNetwork(filteredTopics);
        }

        function rebuildNetwork(filteredTopics) {
            if (!window.network) return;

            // Clear existing network
            window.network.setData({nodes: [], edges: []});

            // Color scheme for different depths
            const depthColors = {
                0: "#ff6b6b",  // Red for root
                1: "#4ecdc4",  // Teal for depth 1
                2: "#45b7d1",  // Blue for depth 2
                3: "#96ceb4",  // Green for depth 3
                4: "#feca57",  // Yellow for depth 4
                5: "#ff9ff3",  // Pink for depth 5
            };

            // Create nodes without titles (using hover display instead)
            const nodes = filteredTopics.map(topic => {
                const [id, depth, term, description] = topic;
                const color = depthColors[depth] || "#cccccc";
                return {
                    id: term,
                    label: term,
                    color: color,
                    size: 20 + (5 - depth) * 5
                };
            });

            // Create edges with arrows
            const edges = [];
            filteredTopics.forEach(topic => {
                const [id, depth, term, description, prereqsJson] = topic;
                try {
                    const prerequisites = JSON.parse(prereqsJson || '[]');
                    prerequisites.forEach(prereq => {
                        // Only add edge if prerequisite is also in filtered data
                        if (filteredTopics.some(t => t[2] === prereq)) {
                            edges.push({
                                from: term,
                                to: prereq,
                                color: "blue",
                                width: 2,
                                arrows: {
                                    to: {
                                        enabled: true,
                                        scaleFactor: 1.2
                                    }
                                }
                            });
                        }
                    });
                } catch (e) {
                    console.error('Error parsing prerequisites for', term, e);
                }
            });

            // Update network
            window.network.setData({nodes: nodes, edges: edges});
            window.network.fit();

            // Reinitialize hover events after rebuilding
            initializeHoverDisplay();
        }

        function initializeHoverDisplay() {
            // Set up hover event listeners for the network
            if (window.network) {
                window.network.on("hoverNode", function(params) {
                    const nodeId = params.node;
                    const topicData = allTopicsData.find(t => t[2] === nodeId);

                    if (topicData) {
                        const [id, depth, term, description, prereqsJson] = topicData;
                        let prerequisites = [];
                        try {
                            prerequisites = JSON.parse(prereqsJson || '[]');
                        } catch (e) {
                            prerequisites = [];
                        }

                        updateHoverDisplay(term, depth, description, prerequisites);
                    }
                });

                window.network.on("blurNode", function(params) {
                    resetHoverDisplay();
                });
            }
        }

        function updateHoverDisplay(term, depth, description, prerequisites) {
            const hoverDisplay = document.getElementById('hoverDisplay');
            const prereqsText = prerequisites.length > 0 ? prerequisites.join(', ') : 'None';

            hoverDisplay.innerHTML = `
                <h4>Topic Information</h4>
                <div class="hover-topic">${term}</div>
                <div class="hover-depth">Depth: ${depth}${depth === 0 ? ' (Root)' : ''}</div>
                <div class="hover-description">${description || 'No description available'}</div>
                <div style="margin-top: 8px; font-size: 11px; color: #666;">
                    <strong>Prerequisites:</strong> ${prereqsText}
                </div>
            `;
        }

        function resetHoverDisplay() {
            const hoverDisplay = document.getElementById('hoverDisplay');
            hoverDisplay.innerHTML = `
                <h4>Topic Information</h4>
                <div class="hover-placeholder">Hover over a topic to see details</div>
            `;
        }

        let sortDirection = {};

        function sortTable(columnIndex) {
            const table = document.querySelector('table tbody');
            const rows = Array.from(table.rows);
            const header = document.querySelectorAll('th.sortable')[columnIndex];

            // Toggle sort direction
            const currentDirection = sortDirection[columnIndex] || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            sortDirection[columnIndex] = newDirection;

            // Update header classes
            document.querySelectorAll('th.sortable').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc');
            });
            header.classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');

            // Sort rows
            rows.sort((a, b) => {
                let aVal = a.cells[columnIndex].textContent.trim();
                let bVal = b.cells[columnIndex].textContent.trim();

                // Convert to numbers for ID and Depth columns
                if (columnIndex === 0 || columnIndex === 1) {
                    aVal = parseInt(aVal) || 0;
                    bVal = parseInt(bVal) || 0;
                }

                if (aVal < bVal) return newDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return newDirection === 'asc' ? 1 : -1;
                return 0;
            });

            // Reorder table rows
            rows.forEach(row => table.appendChild(row));
        }
    </script>

    <script src="lib/bindings/utils.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>
<script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>
<script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "black"}, "id": "Data Science", "label": "Data Science", "shape": "dot", "size": 45}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Statistics", "label": "Statistics", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Probability", "label": "Probability", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Machine Learning", "label": "Machine Learning", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Data Visualization", "label": "Data Visualization", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Database Management", "label": "Database Management", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Mathematics", "label": "Mathematics", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Programming", "label": "Programming", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Algorithms", "label": "Algorithms", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Data Analysis", "label": "Data Analysis", "shape": "dot", "size": 40}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Combinatorics", "label": "Combinatorics", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Arithmetic", "label": "Arithmetic", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Data Structures", "label": "Data Structures", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Data Modeling", "label": "Data Modeling", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Problem Solving", "label": "Problem Solving", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Data Cleaning", "label": "Data Cleaning", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Information Systems", "label": "Information Systems", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "SQL", "label": "SQL", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Calculus", "label": "Calculus", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Algebra", "label": "Algebra", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Logic", "label": "Logic", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Design Principles", "label": "Design Principles", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Set Theory", "label": "Set Theory", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Geometry", "label": "Geometry", "shape": "dot", "size": 35}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Philosophy", "label": "Philosophy", "shape": "dot", "size": 30}, {"color": "#feca57", "font": {"color": "black"}, "id": "Epistemology", "label": "Epistemology", "shape": "dot", "size": 25}, {"color": "#feca57", "font": {"color": "black"}, "id": "History", "label": "History", "shape": "dot", "size": 25}, {"color": "#feca57", "font": {"color": "black"}, "id": "Culture", "label": "Culture", "shape": "dot", "size": 25}, {"color": "#feca57", "font": {"color": "black"}, "id": "Metaphysics", "label": "Metaphysics", "shape": "dot", "size": 25}, {"color": "#feca57", "font": {"color": "black"}, "id": "Ethics", "label": "Ethics", "shape": "dot", "size": 25}, {"color": "#feca57", "font": {"color": "black"}, "id": "Language", "label": "Language", "shape": "dot", "size": 25}, {"color": "#feca57", "font": {"color": "black"}, "id": "Critical Thinking", "label": "Critical Thinking", "shape": "dot", "size": 25}, {"color": "#feca57", "font": {"color": "black"}, "id": "Science", "label": "Science", "shape": "dot", "size": 25}, {"color": "#feca57", "font": {"color": "black"}, "id": "Reasoning", "label": "Reasoning", "shape": "dot", "size": 25}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Chronology", "label": "Chronology", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "History of Ideas", "label": "History of Ideas", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Cognitive Skills", "label": "Cognitive Skills", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Phonetics", "label": "Phonetics", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Anthropology", "label": "Anthropology", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Geography", "label": "Geography", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Traditions", "label": "Traditions", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Moral Philosophy", "label": "Moral Philosophy", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Observation", "label": "Observation", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Semantics", "label": "Semantics", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Linguistics", "label": "Linguistics", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Ontology", "label": "Ontology", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Experimentation", "label": "Experimentation", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Grammar", "label": "Grammar", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Historical Sources", "label": "Historical Sources", "shape": "dot", "size": 20}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Sociology", "label": "Sociology", "shape": "dot", "size": 20}, {"color": "#cccccc", "font": {"color": "black"}, "id": "Biology", "label": "Biology", "shape": "dot", "size": 15}, {"color": "#cccccc", "font": {"color": "black"}, "id": "Neuroscience", "label": "Neuroscience", "shape": "dot", "size": 15}, {"color": "#cccccc", "font": {"color": "black"}, "id": "Psychology", "label": "Psychology", "shape": "dot", "size": 15}, {"color": "#cccccc", "font": {"color": "black"}, "id": "Acoustics", "label": "Acoustics", "shape": "dot", "size": 15}, {"color": "#cccccc", "font": {"color": "black"}, "id": "Earth Science", "label": "Earth Science", "shape": "dot", "size": 15}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "blue", "from": "Data Science", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Machine Learning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Data Visualization", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Database Management", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Probability", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Probability", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Data Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability", "to": "Combinatorics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Probability", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Data Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Visualization", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Visualization", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Visualization", "to": "Design Principles", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Visualization", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Visualization", "to": "Data Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Database Management", "to": "Data Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Database Management", "to": "SQL", "width": 2}, {"arrows": "to", "color": "blue", "from": "Database Management", "to": "Data Modeling", "width": 2}, {"arrows": "to", "color": "blue", "from": "Database Management", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Database Management", "to": "Information Systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Data Structures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Data Structures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Data Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Data Cleaning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Combinatorics", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Combinatorics", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Combinatorics", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Combinatorics", "to": "Probability", "width": 2}, {"arrows": "to", "color": "blue", "from": "Combinatorics", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Arithmetic", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Structures", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Structures", "to": "Algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Structures", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Structures", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Structures", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Modeling", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Modeling", "to": "Database Management", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Modeling", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Modeling", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Modeling", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Cleaning", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Cleaning", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Cleaning", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Cleaning", "to": "Database Management", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Cleaning", "to": "Data Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Information Systems", "to": "Database Management", "width": 2}, {"arrows": "to", "color": "blue", "from": "Information Systems", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Information Systems", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Information Systems", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Information Systems", "to": "Design Principles", "width": 2}, {"arrows": "to", "color": "blue", "from": "SQL", "to": "Database Management", "width": 2}, {"arrows": "to", "color": "blue", "from": "SQL", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "SQL", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "SQL", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "SQL", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Design Principles", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Design Principles", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Design Principles", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Design Principles", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Design Principles", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Probability", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometry", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometry", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometry", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometry", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometry", "to": "Arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Philosophy", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Philosophy", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Philosophy", "to": "Ethics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Philosophy", "to": "History", "width": 2}, {"arrows": "to", "color": "blue", "from": "Philosophy", "to": "Metaphysics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Philosophy", "to": "Epistemology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Philosophy", "to": "Language", "width": 2}, {"arrows": "to", "color": "blue", "from": "Philosophy", "to": "Culture", "width": 2}, {"arrows": "to", "color": "blue", "from": "Philosophy", "to": "Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Philosophy", "to": "Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Epistemology", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Epistemology", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Epistemology", "to": "Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Epistemology", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Epistemology", "to": "History of Ideas", "width": 2}, {"arrows": "to", "color": "blue", "from": "History", "to": "Chronology", "width": 2}, {"arrows": "to", "color": "blue", "from": "History", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "History", "to": "Geography", "width": 2}, {"arrows": "to", "color": "blue", "from": "History", "to": "Culture", "width": 2}, {"arrows": "to", "color": "blue", "from": "History", "to": "Historical Sources", "width": 2}, {"arrows": "to", "color": "blue", "from": "Culture", "to": "Anthropology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Culture", "to": "Sociology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Culture", "to": "History", "width": 2}, {"arrows": "to", "color": "blue", "from": "Culture", "to": "Language", "width": 2}, {"arrows": "to", "color": "blue", "from": "Culture", "to": "Traditions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Metaphysics", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Metaphysics", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Metaphysics", "to": "Ontology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Metaphysics", "to": "Epistemology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Metaphysics", "to": "Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ethics", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ethics", "to": "Moral Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ethics", "to": "Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ethics", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ethics", "to": "Culture", "width": 2}, {"arrows": "to", "color": "blue", "from": "Language", "to": "Linguistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Language", "to": "Grammar", "width": 2}, {"arrows": "to", "color": "blue", "from": "Language", "to": "Phonetics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Language", "to": "Semantics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Language", "to": "Culture", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Cognitive Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Science", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Science", "to": "Observation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Science", "to": "Experimentation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Science", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Science", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Reasoning", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Reasoning", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Reasoning", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Reasoning", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Reasoning", "to": "Cognitive Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chronology", "to": "History", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chronology", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chronology", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chronology", "to": "Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chronology", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "History of Ideas", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "History of Ideas", "to": "History", "width": 2}, {"arrows": "to", "color": "blue", "from": "History of Ideas", "to": "Culture", "width": 2}, {"arrows": "to", "color": "blue", "from": "History of Ideas", "to": "Epistemology", "width": 2}, {"arrows": "to", "color": "blue", "from": "History of Ideas", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Cognitive Skills", "to": "Psychology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Cognitive Skills", "to": "Neuroscience", "width": 2}, {"arrows": "to", "color": "blue", "from": "Cognitive Skills", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Cognitive Skills", "to": "Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Cognitive Skills", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Phonetics", "to": "Linguistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Phonetics", "to": "Language", "width": 2}, {"arrows": "to", "color": "blue", "from": "Phonetics", "to": "Biology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Phonetics", "to": "Acoustics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Phonetics", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Anthropology", "to": "Culture", "width": 2}, {"arrows": "to", "color": "blue", "from": "Anthropology", "to": "History", "width": 2}, {"arrows": "to", "color": "blue", "from": "Anthropology", "to": "Biology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Anthropology", "to": "Sociology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Anthropology", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geography", "to": "Earth Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geography", "to": "History", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geography", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geography", "to": "Culture", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geography", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Traditions", "to": "Culture", "width": 2}, {"arrows": "to", "color": "blue", "from": "Traditions", "to": "History", "width": 2}, {"arrows": "to", "color": "blue", "from": "Traditions", "to": "Anthropology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Traditions", "to": "Sociology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Traditions", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Moral Philosophy", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Moral Philosophy", "to": "Ethics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Moral Philosophy", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Moral Philosophy", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Moral Philosophy", "to": "Epistemology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Observation", "to": "Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Observation", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Observation", "to": "Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Observation", "to": "Biology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Observation", "to": "Psychology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Semantics", "to": "Linguistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Semantics", "to": "Language", "width": 2}, {"arrows": "to", "color": "blue", "from": "Semantics", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Semantics", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Semantics", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linguistics", "to": "Language", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linguistics", "to": "Phonetics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linguistics", "to": "Semantics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linguistics", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linguistics", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ontology", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ontology", "to": "Metaphysics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ontology", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ontology", "to": "Epistemology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ontology", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Experimentation", "to": "Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Experimentation", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Experimentation", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Experimentation", "to": "Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Experimentation", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Grammar", "to": "Language", "width": 2}, {"arrows": "to", "color": "blue", "from": "Grammar", "to": "Linguistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Grammar", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Grammar", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Grammar", "to": "Phonetics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Historical Sources", "to": "History", "width": 2}, {"arrows": "to", "color": "blue", "from": "Historical Sources", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Historical Sources", "to": "Epistemology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Historical Sources", "to": "Culture", "width": 2}, {"arrows": "to", "color": "blue", "from": "Historical Sources", "to": "Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sociology", "to": "Culture", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sociology", "to": "Anthropology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sociology", "to": "Psychology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sociology", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sociology", "to": "History", "width": 2}, {"arrows": "to", "color": "blue", "from": "Biology", "to": "Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Biology", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Biology", "to": "Observation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Biology", "to": "Experimentation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Biology", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Neuroscience", "to": "Biology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Neuroscience", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Neuroscience", "to": "Psychology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Neuroscience", "to": "Cognitive Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Neuroscience", "to": "Observation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Neuroscience", "to": "Experimentation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Neuroscience", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Psychology", "to": "Biology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Psychology", "to": "Cognitive Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Psychology", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Psychology", "to": "Observation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Psychology", "to": "Experimentation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Psychology", "to": "Ethics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Psychology", "to": "Sociology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Psychology", "to": "Anthropology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Psychology", "to": "Philosophy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Psychology", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Acoustics", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Acoustics", "to": "Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Acoustics", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Acoustics", "to": "Experimentation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Acoustics", "to": "Observation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Acoustics", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Acoustics", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Acoustics", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Acoustics", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Earth Science", "to": "Geography", "width": 2}, {"arrows": "to", "color": "blue", "from": "Earth Science", "to": "Biology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Earth Science", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Earth Science", "to": "Observation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Earth Science", "to": "Experimentation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Earth Science", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Earth Science", "to": "Data Analysis", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 100}}, "edges": {"arrows": {"to": {"enabled": true, "scaleFactor": 1.2}}, "smooth": {"enabled": true, "type": "continuous"}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
        <script>
        // Make network globally accessible for filtering
        setTimeout(() => {
            if (typeof network !== 'undefined') {
                window.network = network;
            }
        }, 100);
        </script>
        
</body>
</html>
        