
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .graph-container {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .graph-container.expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1000;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .graph-container.expanded .graph-content {
            height: calc(100vh - 120px) !important;
        }
        /* Target all pyvis network elements for proper expansion */
        .graph-container.expanded .graph-content > div,
        .graph-container.expanded #mynetworkid,
        .graph-container.expanded .card-body,
        .graph-container.expanded #vis-network,
        .graph-container.expanded canvas {
            height: 100% !important;
            width: 100% !important;
        }
        /* Ensure pyvis container divs expand properly */
        .graph-content > div,
        #mynetworkid,
        .card-body,
        #vis-network {
            height: 100%;
            width: 100%;
        }
        /* Make sure canvas fills its container but allows tooltips */
        canvas {
            display: block;
            position: relative;
        }
        /* Fix tooltip positioning */
        .vis-tooltip {
            position: absolute !important;
            z-index: 1000 !important;
            pointer-events: none !important;
        }
        .graph-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 5px;
        }
        .control-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .control-btn:hover {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .depth-filter {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.95);
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .depth-filter label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin: 0;
        }
        .depth-filter select {
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }
        .depth-filter select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .graph-content {
            height: 500px;
            transition: height 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        /* Ensure pyvis elements fill the graph-content container */
        .graph-content > div {
            height: 100% !important;
            width: 100% !important;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f0f0;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="overlay" id="overlay" onclick="toggleExpand()"></div>

    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">11</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">3</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">10</div>
                <div class="stat-label">Topics with Prerequisites</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>

            <div class="graph-container" id="graphContainer">
                <div class="depth-filter">
                    <label for="depthSelect">Max Depth:</label>
                    <select id="depthSelect" onchange="filterByDepth()">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
                <div class="graph-controls">
                    <button class="control-btn" onclick="toggleExpand()" id="expandBtn">
                        🔍 Expand
                    </button>
                    <button class="control-btn" onclick="resetGraph()">
                        🔄 Reset View
                    </button>
                </div>
                <div class="graph-content">
                    <div id="mynetwork" class="card-body"></div>
                </div>
            </div>
        </div>

        
        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Depth</th>
                        <th>Term</th>
                        <th>Description</th>
                        <th>Prerequisites</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">1</td>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Deep Learning</strong></td>
                    <td>A subset of machine learning using artificial neural networks.</td>
                    <td>Machine Learning, Linear Algebra, Calculus, Statistics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">4</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Calculus</strong></td>
                    <td>The mathematical study of continuous change.</td>
                    <td>Mathematics, Limits, Functions</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">3</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Linear Algebra</strong></td>
                    <td>The branch of mathematics concerning linear equations and linear functions.</td>
                    <td>Mathematics, Vector Spaces</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">2</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Machine Learning</strong></td>
                    <td>A field of artificial intelligence that uses statistical techniques for learning from data.</td>
                    <td>Statistics, Linear Algebra, Calculus, Programming Fundamentals</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">5</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Statistics</strong></td>
                    <td>The discipline that concerns collection, organization, analysis, and interpretation of data.</td>
                    <td>Mathematics, Probability Theory</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">10</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Functions</strong></td>
                    <td>Mathematical relations that assign exactly one output to each input.</td>
                    <td>Mathematics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">9</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Limits</strong></td>
                    <td>A fundamental concept describing the behavior of functions near specific points.</td>
                    <td>Mathematics, Functions</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">7</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Mathematics</strong></td>
                    <td>The abstract science of number, quantity, and space.</td>
                    <td>None</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">11</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Probability Theory</strong></td>
                    <td>The branch of mathematics concerned with probability and random phenomena.</td>
                    <td>Mathematics, Set Theory</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">6</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Programming Fundamentals</strong></td>
                    <td>Basic concepts of computer programming including variables, control structures, and algorithms.</td>
                    <td>Logic, Mathematics, Computer Science</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">8</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Vector Spaces</strong></td>
                    <td>A collection of objects called vectors that can be added and scaled.</td>
                    <td>Linear Algebra, Mathematics</td>
                </tr>
                
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🎮 Graph Controls & Navigation</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                <div>
                    <h3 style="color: #555; margin-bottom: 10px;">🖱️ Mouse Controls</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Pan/Move:</strong> Click and drag on empty space</li>
                        <li><strong>Zoom:</strong> Mouse wheel or trackpad scroll</li>
                        <li><strong>Select Node:</strong> Click on any topic node</li>
                        <li><strong>Drag Node:</strong> Click and drag a node to reposition</li>
                        <li><strong>Multi-select:</strong> Ctrl+Click to select multiple nodes</li>
                        <li><strong>Box Select:</strong> Ctrl+Drag to select area</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">⌨️ Keyboard Shortcuts</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Escape:</strong> Close expanded view</li>
                        <li><strong>Delete:</strong> Remove selected nodes (visual only)</li>
                        <li><strong>Ctrl+A:</strong> Select all nodes</li>
                        <li><strong>Space:</strong> Fit graph to view</li>
                    </ul>
                </div>

                <div>
                    <h3 style="color: #555; margin-bottom: 10px;">🔧 Control Buttons</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>🔍 Expand:</strong> Enter fullscreen mode for detailed exploration</li>
                        <li><strong>🔄 Reset View:</strong> Fit all nodes to view and reset zoom</li>
                        <li><strong>Max Depth:</strong> Filter topics by maximum depth level</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">📌 Node Interactions</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Hover:</strong> View topic description and depth</li>
                        <li><strong>Pin/Unpin:</strong> Double-click to fix node position</li>
                        <li><strong>Physics:</strong> Nodes automatically arrange themselves</li>
                        <li><strong>Clustering:</strong> Related topics group together</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">🎯 Visual Features</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Color Coding:</strong> Each depth level has unique color</li>
                        <li><strong>Node Size:</strong> Larger nodes = shallower depth (more fundamental)</li>
                        <li><strong>Edge Direction:</strong> Arrows point from topic to prerequisites</li>
                        <li><strong>Smooth Animations:</strong> Physics-based movement and transitions</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 8px; border-left: 4px solid #007bff;">
                <h4 style="margin: 0 0 10px 0; color: #0056b3;">💡 Pro Tips</h4>
                <ul style="margin: 0; line-height: 1.6;">
                    <li><strong>Large Graphs:</strong> Use depth filter to focus on specific levels</li>
                    <li><strong>Exploration:</strong> Start with depth 1-2, then expand as needed</li>
                    <li><strong>Performance:</strong> Lower depths = faster rendering and interaction</li>
                    <li><strong>Fullscreen:</strong> Use expand mode for complex topic trees</li>
                    <li><strong>Navigation:</strong> Follow prerequisite arrows to understand learning paths</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function toggleExpand() {
            const container = document.getElementById('graphContainer');
            const overlay = document.getElementById('overlay');
            const expandBtn = document.getElementById('expandBtn');

            if (container.classList.contains('expanded')) {
                container.classList.remove('expanded');
                overlay.classList.remove('active');
                expandBtn.innerHTML = '🔍 Expand';
                document.body.style.overflow = 'auto';

                // Force resize of all network elements after collapse
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            } else {
                container.classList.add('expanded');
                overlay.classList.add('active');
                expandBtn.innerHTML = '❌ Close';
                document.body.style.overflow = 'hidden';

                // Force resize of all network elements after expansion
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            }
        }

        function resizeNetworkElements() {
            // Force resize of all pyvis network elements
            const networkElements = [
                document.getElementById('mynetworkid'),
                document.querySelector('.card-body'),
                document.querySelector('#vis-network'),
                document.querySelector('canvas')
            ];

            networkElements.forEach(element => {
                if (element) {
                    // Trigger a reflow by temporarily changing display
                    const originalDisplay = element.style.display;
                    element.style.display = 'none';
                    element.offsetHeight; // Trigger reflow
                    element.style.display = originalDisplay;
                }
            });

            // Redraw and fit the network
            if (window.network) {
                if (window.network.redraw) {
                    window.network.redraw();
                }
                if (window.network.fit) {
                    window.network.fit();
                }
                // Force canvas resize if available
                if (window.network.canvas && window.network.canvas.setSize) {
                    const container = document.querySelector('.graph-content');
                    if (container) {
                        window.network.canvas.setSize(container.offsetWidth, container.offsetHeight);
                    }
                }
            }
        }

        function resetGraph() {
            // Reset the network view and ensure proper sizing
            resizeNetworkElements();
        }

        // Close expanded view with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const container = document.getElementById('graphContainer');
                if (container.classList.contains('expanded')) {
                    toggleExpand();
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            setTimeout(() => {
                resizeNetworkElements();
            }, 100);
        });

        // Initial resize after page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                resizeNetworkElements();
                initializeDepthFilter();
            }, 500);
        });

        // Store original topics data for filtering
        let allTopicsData = [[1, 0, "Deep Learning", "A subset of machine learning using artificial neural networks.", "[\"Machine Learning\", \"Linear Algebra\", \"Calculus\", \"Statistics\"]"], [4, 1, "Calculus", "The mathematical study of continuous change.", "[\"Mathematics\", \"Limits\", \"Functions\"]"], [3, 1, "Linear Algebra", "The branch of mathematics concerning linear equations and linear functions.", "[\"Mathematics\", \"Vector Spaces\"]"], [2, 1, "Machine Learning", "A field of artificial intelligence that uses statistical techniques for learning from data.", "[\"Statistics\", \"Linear Algebra\", \"Calculus\", \"Programming Fundamentals\"]"], [5, 1, "Statistics", "The discipline that concerns collection, organization, analysis, and interpretation of data.", "[\"Mathematics\", \"Probability Theory\"]"], [10, 2, "Functions", "Mathematical relations that assign exactly one output to each input.", "[\"Mathematics\"]"], [9, 2, "Limits", "A fundamental concept describing the behavior of functions near specific points.", "[\"Mathematics\", \"Functions\"]"], [7, 2, "Mathematics", "The abstract science of number, quantity, and space.", "[]"], [11, 2, "Probability Theory", "The branch of mathematics concerned with probability and random phenomena.", "[\"Mathematics\", \"Set Theory\"]"], [6, 2, "Programming Fundamentals", "Basic concepts of computer programming including variables, control structures, and algorithms.", "[\"Logic\", \"Mathematics\", \"Computer Science\"]"], [8, 2, "Vector Spaces", "A collection of objects called vectors that can be added and scaled.", "[\"Linear Algebra\", \"Mathematics\"]"]];
        let currentMaxDepth = 2; // Default to depth 2

        function initializeDepthFilter() {
            const depthSelect = document.getElementById('depthSelect');
            const maxDepth = Math.max(...allTopicsData.map(t => t[1])); // Get max depth from data

            // Populate dropdown options
            for (let i = 0; i <= maxDepth; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = i;
                if (i === currentMaxDepth) {
                    option.selected = true;
                }
                depthSelect.appendChild(option);
            }

            // Apply initial filter
            filterByDepth();
        }

        function filterByDepth() {
            const depthSelect = document.getElementById('depthSelect');
            currentMaxDepth = parseInt(depthSelect.value);

            // Filter topics by selected max depth
            const filteredTopics = allTopicsData.filter(topic => topic[1] <= currentMaxDepth);

            // Rebuild the network with filtered data
            rebuildNetwork(filteredTopics);
        }

        function rebuildNetwork(filteredTopics) {
            if (!window.network) return;

            // Clear existing network
            window.network.setData({nodes: [], edges: []});

            // Color scheme for different depths
            const depthColors = {
                0: "#ff6b6b",  // Red for root
                1: "#4ecdc4",  // Teal for depth 1
                2: "#45b7d1",  // Blue for depth 2
                3: "#96ceb4",  // Green for depth 3
                4: "#feca57",  // Yellow for depth 4
                5: "#ff9ff3",  // Pink for depth 5
            };

            // Create nodes
            const nodes = filteredTopics.map(topic => {
                const [id, depth, term, description] = topic;
                const color = depthColors[depth] || "#cccccc";
                return {
                    id: term,
                    label: term,
                    color: color,
                    title: `Topic: ${term}\nDepth: ${depth}\nDescription: ${description || 'No description'}`,
                    size: 20 + (5 - depth) * 5
                };
            });

            // Create edges with arrows
            const edges = [];
            filteredTopics.forEach(topic => {
                const [id, depth, term, description, prereqsJson] = topic;
                try {
                    const prerequisites = JSON.parse(prereqsJson || '[]');
                    prerequisites.forEach(prereq => {
                        // Only add edge if prerequisite is also in filtered data
                        if (filteredTopics.some(t => t[2] === prereq)) {
                            edges.push({
                                from: term,
                                to: prereq,
                                color: "blue",
                                width: 2,
                                arrows: {
                                    to: {
                                        enabled: true,
                                        scaleFactor: 1.2
                                    }
                                }
                            });
                        }
                    });
                } catch (e) {
                    console.error('Error parsing prerequisites for', term, e);
                }
            });

            // Update network
            window.network.setData({nodes: nodes, edges: edges});
            window.network.fit();
        }
    </script>

    <script src="lib/bindings/utils.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>
<script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>
<script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "black"}, "id": "Deep Learning", "label": "Deep Learning", "shape": "dot", "size": 45, "title": "Topic: Deep Learning\nDepth: 0\nDescription: A subset of machine learning using artificial neural networks."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Calculus", "label": "Calculus", "shape": "dot", "size": 40, "title": "Topic: Calculus\nDepth: 1\nDescription: The mathematical study of continuous change."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Linear Algebra", "label": "Linear Algebra", "shape": "dot", "size": 40, "title": "Topic: Linear Algebra\nDepth: 1\nDescription: The branch of mathematics concerning linear equations and linear functions."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Machine Learning", "label": "Machine Learning", "shape": "dot", "size": 40, "title": "Topic: Machine Learning\nDepth: 1\nDescription: A field of artificial intelligence that uses statistical techniques for learning from data."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Statistics", "label": "Statistics", "shape": "dot", "size": 40, "title": "Topic: Statistics\nDepth: 1\nDescription: The discipline that concerns collection, organization, analysis, and interpretation of data."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Functions", "label": "Functions", "shape": "dot", "size": 35, "title": "Topic: Functions\nDepth: 2\nDescription: Mathematical relations that assign exactly one output to each input."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Limits", "label": "Limits", "shape": "dot", "size": 35, "title": "Topic: Limits\nDepth: 2\nDescription: A fundamental concept describing the behavior of functions near specific points."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Mathematics", "label": "Mathematics", "shape": "dot", "size": 35, "title": "Topic: Mathematics\nDepth: 2\nDescription: The abstract science of number, quantity, and space."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Probability Theory", "label": "Probability Theory", "shape": "dot", "size": 35, "title": "Topic: Probability Theory\nDepth: 2\nDescription: The branch of mathematics concerned with probability and random phenomena."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Programming Fundamentals", "label": "Programming Fundamentals", "shape": "dot", "size": 35, "title": "Topic: Programming Fundamentals\nDepth: 2\nDescription: Basic concepts of computer programming including variables, control structures, and algorithms."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Vector Spaces", "label": "Vector Spaces", "shape": "dot", "size": 35, "title": "Topic: Vector Spaces\nDepth: 2\nDescription: A collection of objects called vectors that can be added and scaled."}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "blue", "from": "Deep Learning", "to": "Machine Learning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Deep Learning", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Deep Learning", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Deep Learning", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Limits", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Vector Spaces", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Programming Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Fundamentals", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Spaces", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Spaces", "to": "Mathematics", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 100}}, "edges": {"arrows": {"to": {"enabled": true, "scaleFactor": 1.2}}, "smooth": {"enabled": true, "type": "continuous"}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
        <script>
        // Make network globally accessible for filtering
        setTimeout(() => {
            if (typeof network !== 'undefined') {
                window.network = network;
            }
        }, 100);
        </script>
        
</body>
</html>
        