
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .graph-container {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .graph-container.expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1000;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .graph-container.expanded .graph-content {
            height: calc(100vh - 120px) !important;
        }
        /* Target all pyvis network elements for proper expansion */
        .graph-container.expanded .graph-content > div,
        .graph-container.expanded #mynetworkid,
        .graph-container.expanded .card-body,
        .graph-container.expanded #vis-network,
        .graph-container.expanded canvas {
            height: 100% !important;
            width: 100% !important;
        }
        /* Ensure pyvis container divs expand properly */
        .graph-content > div,
        #mynetworkid,
        .card-body,
        #vis-network {
            height: 100%;
            width: 100%;
        }
        /* Make sure canvas fills its container but allows tooltips */
        canvas {
            display: block;
            position: relative;
        }
        /* Fix tooltip positioning */
        .vis-tooltip {
            position: absolute !important;
            z-index: 1000 !important;
            pointer-events: none !important;
        }
        .graph-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 5px;
        }
        .control-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .control-btn:hover {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .depth-filter {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.95);
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .depth-filter label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin: 0;
        }
        .depth-filter select {
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }
        .depth-filter select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .hover-display {
            position: absolute;
            top: 60px;
            left: 10px;
            z-index: 10;
            background: rgba(255,255,255,0.95);
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-size: 13px;
            min-width: 250px;
            max-width: 350px;
        }
        .hover-display h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        .hover-topic {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 4px;
        }
        .hover-depth {
            color: #666;
            font-size: 12px;
            margin-bottom: 6px;
        }
        .hover-description {
            color: #333;
            line-height: 1.4;
            font-size: 12px;
        }
        .hover-placeholder {
            color: #999;
            font-style: italic;
            font-size: 12px;
        }
        .graph-content {
            height: 500px;
            transition: height 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        /* Ensure pyvis elements fill the graph-content container */
        .graph-content > div {
            height: 100% !important;
            width: 100% !important;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        th.sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
        }
        th.sortable:hover {
            background-color: #e9ecef;
        }
        th.sortable::after {
            content: ' ↕️';
            font-size: 12px;
            opacity: 0.5;
        }
        th.sortable.sort-asc::after {
            content: ' ↑';
            opacity: 1;
        }
        th.sortable.sort-desc::after {
            content: ' ↓';
            opacity: 1;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f0f0;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="overlay" id="overlay" onclick="toggleExpand()"></div>

    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">25</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">25</div>
                <div class="stat-label">Topics with Prerequisites</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>

            <div class="graph-container" id="graphContainer">
                <div class="depth-filter">
                    <label for="depthSelect">Max Depth:</label>
                    <select id="depthSelect" onchange="filterByDepth()">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
                <div class="hover-display" id="hoverDisplay">
                    <h4>Topic Information</h4>
                    <div class="hover-placeholder">Hover over a topic to see details</div>
                </div>
                <div class="graph-controls">
                    <button class="control-btn" onclick="toggleExpand()" id="expandBtn">
                        🔍 Expand
                    </button>
                    <button class="control-btn" onclick="resetGraph()">
                        🔄 Reset View
                    </button>
                </div>
                <div class="graph-content">
                    <div id="mynetwork" class="card-body"></div>
                </div>
            </div>
        </div>

        
        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th class="sortable" onclick="sortTable(0)">ID</th>
                        <th class="sortable" onclick="sortTable(1)">Depth</th>
                        <th class="sortable" onclick="sortTable(2)">Term</th>
                        <th>Description</th>
                        <th>Prerequisites</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">1</td>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Data Science</strong></td>
                    <td>Analyzing data to extract insights.</td>
                    <td>Statistics, Programming, Data Analysis, Machine Learning</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">2</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Machine Learning</strong></td>
                    <td>Algorithms that learn from data.</td>
                    <td>Linear Algebra, Probability, Calculus, Algorithms</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">3</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Statistics</strong></td>
                    <td>Study of data collection and analysis.</td>
                    <td>Probability, Data Collection, Mathematics, Hypothesis Testing</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">4</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Data Analysis</strong></td>
                    <td>Process of inspecting data sets.</td>
                    <td>Data Collection, Visualization, Statistical Methods, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">5</td>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Programming</strong></td>
                    <td>Writing instructions for computers.</td>
                    <td>Logic, Syntax, Algorithms, Problem Solving</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">6</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Hypothesis Testing</strong></td>
                    <td>Statistical method to test assumptions.</td>
                    <td>Statistics, Data Analysis</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">7</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Linear Algebra</strong></td>
                    <td>Study of vectors and matrices.</td>
                    <td>Mathematics, Algebra</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">8</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Logic</strong></td>
                    <td>Principles of valid reasoning.</td>
                    <td>Mathematics, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">9</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Data Collection</strong></td>
                    <td>Gathering information for analysis.</td>
                    <td>Data Analysis, Statistics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">10</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Algorithms</strong></td>
                    <td>Step-by-step problem-solving procedures.</td>
                    <td>Mathematics, Logic</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">11</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Statistical Methods</strong></td>
                    <td>Techniques for data analysis.</td>
                    <td>Statistics, Probability</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">12</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Mathematics</strong></td>
                    <td>Study of numbers and structures.</td>
                    <td>Arithmetic, Algebra</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">13</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Calculus</strong></td>
                    <td>Study of change and motion.</td>
                    <td>Mathematics, Algebra</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">14</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Critical Thinking</strong></td>
                    <td>Analyzing facts for decision making.</td>
                    <td>Logic, Problem Solving</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">15</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Visualization</strong></td>
                    <td>Graphical representation of data.</td>
                    <td>Data Analysis, Statistics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">16</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Probability</strong></td>
                    <td>Study of chance and events.</td>
                    <td>Mathematics, Statistics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">17</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Syntax</strong></td>
                    <td>Rules for sentence structure.</td>
                    <td>Grammar, Linguistics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">18</td>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Problem Solving</strong></td>
                    <td>Finding solutions to complex issues.</td>
                    <td>Logic, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">19</td>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Algebra</strong></td>
                    <td>Mathematical study of symbols and rules.</td>
                    <td>Arithmetic, Logic</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">20</td>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Arithmetic</strong></td>
                    <td>Basic operations with numbers.</td>
                    <td>Numbers, Counting</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">21</td>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Linguistics</strong></td>
                    <td>Scientific study of language.</td>
                    <td>Syntax, Phonetics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">22</td>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Grammar</strong></td>
                    <td>Rules governing language structure.</td>
                    <td>Syntax, Linguistics</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">23</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Numbers</strong></td>
                    <td>Symbols representing quantities or values.</td>
                    <td>Logic, Mathematics, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">24</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Phonetics</strong></td>
                    <td>Study of speech sounds and their production.</td>
                    <td>Linguistics, Critical Thinking, Syntax</td>
                </tr>
                
                <tr>
                    <td style="text-align: center; font-weight: bold; color: #666;">25</td>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Counting</strong></td>
                    <td>Process of determining the total number of items.</td>
                    <td>Logic, Mathematics, Problem Solving</td>
                </tr>
                
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🎮 Graph Controls & Navigation</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                <div>
                    <h3 style="color: #555; margin-bottom: 10px;">🖱️ Mouse Controls</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Pan/Move:</strong> Click and drag on empty space</li>
                        <li><strong>Zoom:</strong> Mouse wheel or trackpad scroll</li>
                        <li><strong>Select Node:</strong> Click on any topic node</li>
                        <li><strong>Drag Node:</strong> Click and drag a node to reposition</li>
                        <li><strong>Multi-select:</strong> Ctrl+Click to select multiple nodes</li>
                        <li><strong>Box Select:</strong> Ctrl+Drag to select area</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">⌨️ Keyboard Shortcuts</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Escape:</strong> Close expanded view</li>
                        <li><strong>Delete:</strong> Remove selected nodes (visual only)</li>
                        <li><strong>Ctrl+A:</strong> Select all nodes</li>
                        <li><strong>Space:</strong> Fit graph to view</li>
                    </ul>
                </div>

                <div>
                    <h3 style="color: #555; margin-bottom: 10px;">🔧 Control Buttons</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>🔍 Expand:</strong> Enter fullscreen mode for detailed exploration</li>
                        <li><strong>🔄 Reset View:</strong> Fit all nodes to view and reset zoom</li>
                        <li><strong>Max Depth:</strong> Filter topics by maximum depth level</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">📌 Node Interactions</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Hover:</strong> View topic description and depth</li>
                        <li><strong>Pin/Unpin:</strong> Double-click to fix node position</li>
                        <li><strong>Physics:</strong> Nodes automatically arrange themselves</li>
                        <li><strong>Clustering:</strong> Related topics group together</li>
                    </ul>

                    <h3 style="color: #555; margin: 15px 0 10px 0;">🎯 Visual Features</h3>
                    <ul style="line-height: 1.6;">
                        <li><strong>Color Coding:</strong> Each depth level has unique color</li>
                        <li><strong>Node Size:</strong> Larger nodes = shallower depth (more fundamental)</li>
                        <li><strong>Edge Direction:</strong> Arrows point from topic to prerequisites</li>
                        <li><strong>Smooth Animations:</strong> Physics-based movement and transitions</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 8px; border-left: 4px solid #007bff;">
                <h4 style="margin: 0 0 10px 0; color: #0056b3;">💡 Pro Tips</h4>
                <ul style="margin: 0; line-height: 1.6;">
                    <li><strong>Large Graphs:</strong> Use depth filter to focus on specific levels</li>
                    <li><strong>Exploration:</strong> Start with depth 1-2, then expand as needed</li>
                    <li><strong>Performance:</strong> Lower depths = faster rendering and interaction</li>
                    <li><strong>Fullscreen:</strong> Use expand mode for complex topic trees</li>
                    <li><strong>Navigation:</strong> Follow prerequisite arrows to understand learning paths</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function toggleExpand() {
            const container = document.getElementById('graphContainer');
            const overlay = document.getElementById('overlay');
            const expandBtn = document.getElementById('expandBtn');

            if (container.classList.contains('expanded')) {
                container.classList.remove('expanded');
                overlay.classList.remove('active');
                expandBtn.innerHTML = '🔍 Expand';
                document.body.style.overflow = 'auto';

                // Force resize of all network elements after collapse
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            } else {
                container.classList.add('expanded');
                overlay.classList.add('active');
                expandBtn.innerHTML = '❌ Close';
                document.body.style.overflow = 'hidden';

                // Force resize of all network elements after expansion
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            }
        }

        function resizeNetworkElements() {
            // Force resize of all pyvis network elements
            const networkElements = [
                document.getElementById('mynetworkid'),
                document.querySelector('.card-body'),
                document.querySelector('#vis-network'),
                document.querySelector('canvas')
            ];

            networkElements.forEach(element => {
                if (element) {
                    // Trigger a reflow by temporarily changing display
                    const originalDisplay = element.style.display;
                    element.style.display = 'none';
                    element.offsetHeight; // Trigger reflow
                    element.style.display = originalDisplay;
                }
            });

            // Redraw and fit the network
            if (window.network) {
                if (window.network.redraw) {
                    window.network.redraw();
                }
                if (window.network.fit) {
                    window.network.fit();
                }
                // Force canvas resize if available
                if (window.network.canvas && window.network.canvas.setSize) {
                    const container = document.querySelector('.graph-content');
                    if (container) {
                        window.network.canvas.setSize(container.offsetWidth, container.offsetHeight);
                    }
                }
            }
        }

        function resetGraph() {
            // Reset the network view and ensure proper sizing
            resizeNetworkElements();
        }

        // Close expanded view with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const container = document.getElementById('graphContainer');
                if (container.classList.contains('expanded')) {
                    toggleExpand();
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            setTimeout(() => {
                resizeNetworkElements();
            }, 100);
        });

        // Initial resize after page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                resizeNetworkElements();
                initializeDepthFilter();
                initializeHoverDisplay();
                sortTable(0); // Sort by ID by default
            }, 500);
        });

        // Store original topics data for filtering
        let allTopicsData = [[1, 0, "Data Science", "Analyzing data to extract insights.", "[\"Statistics\", \"Programming\", \"Data Analysis\", \"Machine Learning\"]"], [2, 1, "Machine Learning", "Algorithms that learn from data.", "[\"Linear Algebra\", \"Probability\", \"Calculus\", \"Algorithms\"]"], [3, 1, "Statistics", "Study of data collection and analysis.", "[\"Probability\", \"Data Collection\", \"Mathematics\", \"Hypothesis Testing\"]"], [4, 1, "Data Analysis", "Process of inspecting data sets.", "[\"Data Collection\", \"Visualization\", \"Statistical Methods\", \"Critical Thinking\"]"], [5, 1, "Programming", "Writing instructions for computers.", "[\"Logic\", \"Syntax\", \"Algorithms\", \"Problem Solving\"]"], [6, 2, "Hypothesis Testing", "Statistical method to test assumptions.", "[\"Statistics\", \"Data Analysis\"]"], [7, 2, "Linear Algebra", "Study of vectors and matrices.", "[\"Mathematics\", \"Algebra\"]"], [8, 2, "Logic", "Principles of valid reasoning.", "[\"Mathematics\", \"Critical Thinking\"]"], [9, 2, "Data Collection", "Gathering information for analysis.", "[\"Data Analysis\", \"Statistics\"]"], [10, 2, "Algorithms", "Step-by-step problem-solving procedures.", "[\"Mathematics\", \"Logic\"]"], [11, 2, "Statistical Methods", "Techniques for data analysis.", "[\"Statistics\", \"Probability\"]"], [12, 2, "Mathematics", "Study of numbers and structures.", "[\"Arithmetic\", \"Algebra\"]"], [13, 2, "Calculus", "Study of change and motion.", "[\"Mathematics\", \"Algebra\"]"], [14, 2, "Critical Thinking", "Analyzing facts for decision making.", "[\"Logic\", \"Problem Solving\"]"], [15, 2, "Visualization", "Graphical representation of data.", "[\"Data Analysis\", \"Statistics\"]"], [16, 2, "Probability", "Study of chance and events.", "[\"Mathematics\", \"Statistics\"]"], [17, 2, "Syntax", "Rules for sentence structure.", "[\"Grammar\", \"Linguistics\"]"], [18, 2, "Problem Solving", "Finding solutions to complex issues.", "[\"Logic\", \"Critical Thinking\"]"], [19, 3, "Algebra", "Mathematical study of symbols and rules.", "[\"Arithmetic\", \"Logic\"]"], [20, 3, "Arithmetic", "Basic operations with numbers.", "[\"Numbers\", \"Counting\"]"], [21, 3, "Linguistics", "Scientific study of language.", "[\"Syntax\", \"Phonetics\"]"], [22, 3, "Grammar", "Rules governing language structure.", "[\"Syntax\", \"Linguistics\"]"], [23, 4, "Numbers", "Symbols representing quantities or values.", "[\"Logic\", \"Mathematics\", \"Critical Thinking\"]"], [24, 4, "Phonetics", "Study of speech sounds and their production.", "[\"Linguistics\", \"Critical Thinking\", \"Syntax\"]"], [25, 4, "Counting", "Process of determining the total number of items.", "[\"Logic\", \"Mathematics\", \"Problem Solving\"]"]];
        let currentMaxDepth = 2; // Default to depth 2

        function initializeDepthFilter() {
            const depthSelect = document.getElementById('depthSelect');
            const maxDepth = Math.max(...allTopicsData.map(t => t[1])); // Get max depth from data

            // Populate dropdown options
            for (let i = 0; i <= maxDepth; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = i;
                if (i === currentMaxDepth) {
                    option.selected = true;
                }
                depthSelect.appendChild(option);
            }

            // Apply initial filter
            filterByDepth();
        }

        function filterByDepth() {
            const depthSelect = document.getElementById('depthSelect');
            currentMaxDepth = parseInt(depthSelect.value);

            // Filter topics by selected max depth
            const filteredTopics = allTopicsData.filter(topic => topic[1] <= currentMaxDepth);

            // Rebuild the network with filtered data
            rebuildNetwork(filteredTopics);
        }

        function rebuildNetwork(filteredTopics) {
            if (!window.network) return;

            // Clear existing network
            window.network.setData({nodes: [], edges: []});

            // Color scheme for different depths
            const depthColors = {
                0: "#ff6b6b",  // Red for root
                1: "#4ecdc4",  // Teal for depth 1
                2: "#45b7d1",  // Blue for depth 2
                3: "#96ceb4",  // Green for depth 3
                4: "#feca57",  // Yellow for depth 4
                5: "#ff9ff3",  // Pink for depth 5
            };

            // Create nodes without titles (using hover display instead)
            const nodes = filteredTopics.map(topic => {
                const [id, depth, term, description] = topic;
                const color = depthColors[depth] || "#cccccc";
                return {
                    id: term,
                    label: term,
                    color: color,
                    size: 20 + (5 - depth) * 5
                };
            });

            // Create edges with arrows
            const edges = [];
            filteredTopics.forEach(topic => {
                const [id, depth, term, description, prereqsJson] = topic;
                try {
                    const prerequisites = JSON.parse(prereqsJson || '[]');
                    prerequisites.forEach(prereq => {
                        // Only add edge if prerequisite is also in filtered data
                        if (filteredTopics.some(t => t[2] === prereq)) {
                            edges.push({
                                from: term,
                                to: prereq,
                                color: "blue",
                                width: 2,
                                arrows: {
                                    to: {
                                        enabled: true,
                                        scaleFactor: 1.2
                                    }
                                }
                            });
                        }
                    });
                } catch (e) {
                    console.error('Error parsing prerequisites for', term, e);
                }
            });

            // Update network
            window.network.setData({nodes: nodes, edges: edges});
            window.network.fit();

            // Reinitialize hover events after rebuilding
            initializeHoverDisplay();
        }

        function initializeHoverDisplay() {
            // Set up hover event listeners for the network
            if (window.network) {
                window.network.on("hoverNode", function(params) {
                    const nodeId = params.node;
                    const topicData = allTopicsData.find(t => t[2] === nodeId);

                    if (topicData) {
                        const [id, depth, term, description, prereqsJson] = topicData;
                        let prerequisites = [];
                        try {
                            prerequisites = JSON.parse(prereqsJson || '[]');
                        } catch (e) {
                            prerequisites = [];
                        }

                        updateHoverDisplay(term, depth, description, prerequisites);
                    }
                });

                window.network.on("blurNode", function(params) {
                    resetHoverDisplay();
                });
            }
        }

        function updateHoverDisplay(term, depth, description, prerequisites) {
            const hoverDisplay = document.getElementById('hoverDisplay');
            const prereqsText = prerequisites.length > 0 ? prerequisites.join(', ') : 'None';

            hoverDisplay.innerHTML = `
                <h4>Topic Information</h4>
                <div class="hover-topic">${term}</div>
                <div class="hover-depth">Depth: ${depth}${depth === 0 ? ' (Root)' : ''}</div>
                <div class="hover-description">${description || 'No description available'}</div>
                <div style="margin-top: 8px; font-size: 11px; color: #666;">
                    <strong>Prerequisites:</strong> ${prereqsText}
                </div>
            `;
        }

        function resetHoverDisplay() {
            const hoverDisplay = document.getElementById('hoverDisplay');
            hoverDisplay.innerHTML = `
                <h4>Topic Information</h4>
                <div class="hover-placeholder">Hover over a topic to see details</div>
            `;
        }

        let sortDirection = {};

        function sortTable(columnIndex) {
            const table = document.querySelector('table tbody');
            const rows = Array.from(table.rows);
            const header = document.querySelectorAll('th.sortable')[columnIndex];

            // Toggle sort direction
            const currentDirection = sortDirection[columnIndex] || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            sortDirection[columnIndex] = newDirection;

            // Update header classes
            document.querySelectorAll('th.sortable').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc');
            });
            header.classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');

            // Sort rows
            rows.sort((a, b) => {
                let aVal = a.cells[columnIndex].textContent.trim();
                let bVal = b.cells[columnIndex].textContent.trim();

                // Convert to numbers for ID and Depth columns
                if (columnIndex === 0 || columnIndex === 1) {
                    aVal = parseInt(aVal) || 0;
                    bVal = parseInt(bVal) || 0;
                }

                if (aVal < bVal) return newDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return newDirection === 'asc' ? 1 : -1;
                return 0;
            });

            // Reorder table rows
            rows.forEach(row => table.appendChild(row));
        }
    </script>

    <script src="lib/bindings/utils.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>
<script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>
<script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "black"}, "id": "Data Science", "label": "Data Science", "shape": "dot", "size": 45}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Machine Learning", "label": "Machine Learning", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Statistics", "label": "Statistics", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Data Analysis", "label": "Data Analysis", "shape": "dot", "size": 40}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Programming", "label": "Programming", "shape": "dot", "size": 40}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Hypothesis Testing", "label": "Hypothesis Testing", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Linear Algebra", "label": "Linear Algebra", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Logic", "label": "Logic", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Data Collection", "label": "Data Collection", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Algorithms", "label": "Algorithms", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Statistical Methods", "label": "Statistical Methods", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Mathematics", "label": "Mathematics", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Calculus", "label": "Calculus", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Critical Thinking", "label": "Critical Thinking", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Visualization", "label": "Visualization", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Probability", "label": "Probability", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Syntax", "label": "Syntax", "shape": "dot", "size": 35}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Problem Solving", "label": "Problem Solving", "shape": "dot", "size": 35}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Algebra", "label": "Algebra", "shape": "dot", "size": 30}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Arithmetic", "label": "Arithmetic", "shape": "dot", "size": 30}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Linguistics", "label": "Linguistics", "shape": "dot", "size": 30}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Grammar", "label": "Grammar", "shape": "dot", "size": 30}, {"color": "#feca57", "font": {"color": "black"}, "id": "Numbers", "label": "Numbers", "shape": "dot", "size": 25}, {"color": "#feca57", "font": {"color": "black"}, "id": "Phonetics", "label": "Phonetics", "shape": "dot", "size": 25}, {"color": "#feca57", "font": {"color": "black"}, "id": "Counting", "label": "Counting", "shape": "dot", "size": 25}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "blue", "from": "Data Science", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Programming", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Science", "to": "Machine Learning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Probability", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Probability", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Data Collection", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Hypothesis Testing", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Data Collection", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Visualization", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Statistical Methods", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Syntax", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Algorithms", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Hypothesis Testing", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Hypothesis Testing", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logic", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Collection", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Collection", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algorithms", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Methods", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Methods", "to": "Probability", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Visualization", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Visualization", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Syntax", "to": "Grammar", "width": 2}, {"arrows": "to", "color": "blue", "from": "Syntax", "to": "Linguistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Arithmetic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Arithmetic", "to": "Numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Arithmetic", "to": "Counting", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linguistics", "to": "Syntax", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linguistics", "to": "Phonetics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Grammar", "to": "Syntax", "width": 2}, {"arrows": "to", "color": "blue", "from": "Grammar", "to": "Linguistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Numbers", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Numbers", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Numbers", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Phonetics", "to": "Linguistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Phonetics", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Phonetics", "to": "Syntax", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting", "to": "Logic", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting", "to": "Problem Solving", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 100}}, "edges": {"arrows": {"to": {"enabled": true, "scaleFactor": 1.2}}, "smooth": {"enabled": true, "type": "continuous"}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
        <script>
        // Make network globally accessible for filtering
        setTimeout(() => {
            if (typeof network !== 'undefined') {
                window.network = network;
            }
        }, 100);
        </script>
        
</body>
</html>
        