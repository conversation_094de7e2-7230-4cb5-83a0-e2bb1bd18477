
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .graph-container {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .graph-container.expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1000;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .graph-container.expanded .graph-content {
            height: calc(100vh - 120px) !important;
        }
        /* Target all pyvis network elements for proper expansion */
        .graph-container.expanded .graph-content > div,
        .graph-container.expanded #mynetworkid,
        .graph-container.expanded .card-body,
        .graph-container.expanded #vis-network,
        .graph-container.expanded canvas {
            height: 100% !important;
            width: 100% !important;
        }
        /* Ensure pyvis container divs expand properly */
        .graph-content > div,
        #mynetworkid,
        .card-body,
        #vis-network {
            height: 100%;
            width: 100%;
        }
        /* Make sure canvas fills its container */
        canvas {
            display: block;
        }
        .graph-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 5px;
        }
        .control-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .control-btn:hover {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .graph-content {
            height: 500px;
            transition: height 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        /* Ensure pyvis elements fill the graph-content container */
        .graph-content > div {
            height: 100% !important;
            width: 100% !important;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f0f0;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="overlay" id="overlay" onclick="toggleExpand()"></div>

    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">178</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">6</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">178</div>
                <div class="stat-label">Topics with Dependencies</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>

            <div class="graph-container" id="graphContainer">
                <div class="graph-controls">
                    <button class="control-btn" onclick="toggleExpand()" id="expandBtn">
                        🔍 Expand
                    </button>
                    <button class="control-btn" onclick="resetGraph()">
                        🔄 Reset View
                    </button>
                </div>
                <div class="graph-content">
                    <div id="mynetwork" class="card-body"></div>
                </div>
            </div>
        </div>

        
        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th>Depth</th>
                        <th>Term</th>
                        <th>Description</th>
                        <th>Dependencies</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Battery Electric Vehicles</strong></td>
                    <td>Battery Electric Vehicles (BEVs) are automobiles that are powered entirely by electric energy stored in batteries, utilizing electric motors for propulsion. They are a key component in the transition to sustainable transportation and reducing greenhouse gas emissions.</td>
                    <td>Electric Circuits, Energy Storage, Motor Technology, Battery Chemistry</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Battery Chemistry</strong></td>
                    <td>Battery chemistry involves the study of the chemical processes and materials that enable the storage and release of electrical energy in batteries. It encompasses various types of batteries, including lithium-ion, lead-acid, and nickel-metal hydride, focusing on their electrochemical reactions and performance characteristics.</td>
                    <td>Basic Chemistry, Electrochemistry, Material Science, Thermodynamics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Electric Circuits</strong></td>
                    <td>Electric circuits are pathways through which electric current flows, consisting of various components such as resistors, capacitors, and power sources. Understanding electric circuits is essential for analyzing and designing electrical systems.</td>
                    <td>Basic Mathematics, Ohm's Law, Circuit Components, Voltage and Current</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Energy Storage</strong></td>
                    <td>Energy storage refers to the methods and technologies used to store energy for later use, enabling a balance between energy supply and demand. It plays a crucial role in renewable energy systems, grid stability, and enhancing energy efficiency.</td>
                    <td>Basic Physics, Thermodynamics, Electrical Circuits, Energy Conversion, Material Science</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Motor Technology</strong></td>
                    <td>Motor Technology encompasses the study and application of various types of motors, including electric, hydraulic, and pneumatic systems, focusing on their design, operation, and control mechanisms.</td>
                    <td>Electrical Fundamentals, Mechanical Principles, Control Systems, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic Chemistry</strong></td>
                    <td>Basic Chemistry introduces fundamental concepts of matter, its properties, and the interactions between different substances. It serves as a foundation for understanding chemical reactions, atomic structure, and the periodic table.</td>
                    <td>Basic Mathematics, Scientific Notation, Measurement Units, Atomic Structure</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic Mathematics</strong></td>
                    <td>Basic Mathematics encompasses fundamental concepts such as arithmetic, number theory, and basic algebra, forming the foundation for more advanced mathematical studies. It is essential for everyday problem-solving and various academic disciplines.</td>
                    <td>Number Recognition, Arithmetic Operations, Basic Geometry</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic Physics</strong></td>
                    <td>Basic Physics is the study of fundamental principles governing the behavior of matter and energy, focusing on concepts such as motion, forces, energy, and waves. It serves as a foundation for understanding more complex physical phenomena and applications.</td>
                    <td>Basic Mathematics, Algebra, Geometry, Scientific Method</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Circuit Components</strong></td>
                    <td>Circuit components are the fundamental elements used in electrical circuits, including resistors, capacitors, inductors, and diodes, which interact to perform specific functions. Understanding these components is essential for designing and analyzing electronic circuits.</td>
                    <td>Basic Electricity, Ohm's Law, Circuit Theory, Soldering Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Control Systems</strong></td>
                    <td>Control Systems is a branch of engineering that deals with the behavior of dynamic systems and the design of controllers to regulate their behavior. It encompasses both theoretical and practical aspects, including feedback mechanisms and stability analysis.</td>
                    <td>Basic Mathematics, Differential Equations, Linear Algebra, Signal Processing, Systems Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Electrical Circuits</strong></td>
                    <td>Electrical circuits are pathways that allow electric current to flow, consisting of components such as resistors, capacitors, and inductors. Understanding these circuits is essential for analyzing and designing electrical systems.</td>
                    <td>Basic Mathematics, Ohm's Law, Circuit Components, Voltage and Current</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Electrical Fundamentals</strong></td>
                    <td>Electrical Fundamentals covers the basic principles of electricity, including concepts such as voltage, current, resistance, and power. It serves as the foundation for understanding more complex electrical systems and circuits.</td>
                    <td>Basic Mathematics, Physics Basics, Circuit Theory, Ohm's Law</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Electrochemistry</strong></td>
                    <td>Electrochemistry is the branch of chemistry that deals with the relationship between electrical energy and chemical reactions, particularly the processes of oxidation and reduction. It encompasses the study of electrochemical cells, reactions, and their applications in various fields such as batteries and corrosion.</td>
                    <td>General Chemistry, Basic Physics, Chemical Kinetics, Thermodynamics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Energy Conversion</strong></td>
                    <td>Energy conversion refers to the process of changing energy from one form to another, such as converting kinetic energy to electrical energy or thermal energy to mechanical energy. This topic is fundamental in understanding various applications in physics, engineering, and environmental science.</td>
                    <td>Basic Physics, Thermodynamics, Energy Types, Mathematics Fundamentals</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Material Science</strong></td>
                    <td>Material Science is the study of the properties, performance, and applications of materials, integrating principles from physics, chemistry, and engineering to innovate and improve material design and functionality.</td>
                    <td>Basic Chemistry, Solid Mechanics, Thermodynamics, Materials Properties, Crystal Structures</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Mechanical Principles</strong></td>
                    <td>Mechanical Principles encompass the fundamental concepts and laws governing the behavior of physical systems in motion, including forces, energy, and mechanics. This topic is essential for understanding how machines and structures operate in engineering and physics.</td>
                    <td>Basic Mathematics, Physics Fundamentals, Vector Analysis, Static Equilibrium, Kinematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Ohm's Law</strong></td>
                    <td>Ohm's Law is a fundamental principle in electronics that states the relationship between voltage, current, and resistance in an electrical circuit, expressed as V = IR. It is essential for understanding how electrical circuits operate and how to calculate the values of voltage, current, and resistance.</td>
                    <td>Basic Electricity, Circuit Theory, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Thermodynamics</strong></td>
                    <td>Thermodynamics is the branch of physics that deals with the relationships between heat, work, temperature, and energy. It provides fundamental principles that govern the behavior of physical systems in terms of energy transfer and transformation.</td>
                    <td>Basic Mathematics, Physics Fundamentals, Calculus, Statistical Mechanics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Voltage and Current</strong></td>
                    <td>Voltage and current are fundamental electrical concepts where voltage represents the electric potential difference between two points, and current is the flow of electric charge through a conductor. Understanding these concepts is essential for analyzing and designing electrical circuits.</td>
                    <td>Basic Mathematics, Ohm's Law, Circuit Theory, Electric Charge</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Algebra</strong></td>
                    <td>Algebra is a branch of mathematics dealing with symbols and the rules for manipulating those symbols to solve equations and understand relationships between quantities. It serves as a foundational tool for higher mathematics and various applications in science and engineering.</td>
                    <td>Basic Mathematics, Arithmetic Operations, Number Properties, Equation Solving</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Arithmetic Operations</strong></td>
                    <td>Arithmetic operations are fundamental mathematical processes that include addition, subtraction, multiplication, and division, used to manipulate numerical values. Mastery of these operations is essential for solving mathematical problems and performing calculations in various fields.</td>
                    <td>Basic Mathematics, Number Recognition, Understanding of Symbols</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Atomic Structure</strong></td>
                    <td>Atomic structure refers to the arrangement of protons, neutrons, and electrons within an atom, which determines its chemical properties and behavior. Understanding atomic structure is fundamental to the study of chemistry and physics.</td>
                    <td>Basic Chemistry, Atomic Theory, Periodic Table, Electron Configuration</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic Electricity</strong></td>
                    <td>Basic Electricity covers fundamental concepts of electrical principles, including voltage, current, resistance, and circuit theory, essential for understanding how electrical systems operate.</td>
                    <td>Basic Mathematics, Scientific Notation, Ohm's Law, Circuit Components</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic Geometry</strong></td>
                    <td>Basic Geometry involves the study of shapes, sizes, and the properties of space. It serves as a foundation for understanding more complex geometric concepts and applications.</td>
                    <td>Basic Mathematics, Algebra Basics, Spatial Reasoning</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Calculus</strong></td>
                    <td>Calculus is a branch of mathematics that studies continuous change, focusing on concepts such as derivatives and integrals. It provides tools for modeling and analyzing dynamic systems in various fields, including physics, engineering, and economics.</td>
                    <td>Algebra, Geometry, Trigonometry, Functions, Limits</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Chemical Kinetics</strong></td>
                    <td>Chemical kinetics is the study of the rates of chemical reactions and the factors that influence these rates. It involves understanding how different conditions affect the speed of reactions and the mechanisms by which they occur.</td>
                    <td>Chemical Reactions, Stoichiometry, Basic Mathematics, Thermodynamics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Circuit Theory</strong></td>
                    <td>Circuit Theory is the study of electrical circuits, focusing on the behavior of voltage, current, and resistance in various configurations. It provides the foundational principles for analyzing and designing electrical systems.</td>
                    <td>Basic Mathematics, Ohm's Law, AC/DC Fundamentals, Complex Numbers, Linear Algebra</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Crystal Structures</strong></td>
                    <td>Crystal structures refer to the ordered arrangement of atoms, ions, or molecules in a crystalline material, which determines its physical properties and behavior. Understanding these structures is essential for fields such as materials science, chemistry, and solid-state physics.</td>
                    <td>Atomic Structure, Chemical Bonding, Unit Cells, Symmetry Operations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Differential Equations</strong></td>
                    <td>Differential equations are mathematical equations that relate a function to its derivatives, used to model various phenomena in physics, engineering, and other fields. They are essential for understanding dynamic systems and change over time.</td>
                    <td>Calculus, Algebra, Functions, Graphing, Limits</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Electric Charge</strong></td>
                    <td>Electric charge is a fundamental property of matter that causes it to experience a force when placed in an electromagnetic field. It exists in two types, positive and negative, and is quantized in discrete amounts.</td>
                    <td>Basic Mathematics, Newton's Laws, Electromagnetic Theory, Atomic Structure</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Energy Types</strong></td>
                    <td>Energy types refer to the various forms of energy, including kinetic, potential, thermal, chemical, and more, each with distinct characteristics and applications. Understanding these types is essential for studying energy transfer and conservation in physical systems.</td>
                    <td>Basic Physics, Energy Conservation, Thermodynamics, Mechanical Energy</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>General Chemistry</strong></td>
                    <td>General Chemistry is an introductory course that covers the fundamental principles of chemistry, including atomic structure, chemical bonding, stoichiometry, and the behavior of gases, liquids, and solids. It serves as a foundation for more advanced studies in chemistry and related fields.</td>
                    <td>Basic Mathematics, Scientific Notation, Measurement Units, Atomic Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Geometry</strong></td>
                    <td>Geometry is a branch of mathematics that deals with the properties and relationships of points, lines, surfaces, and solids. It involves the study of shapes, sizes, and the relative position of figures in space.</td>
                    <td>Basic Mathematics, Algebra, Logical Reasoning, Measurement Concepts</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Kinematics</strong></td>
                    <td>Kinematics is the branch of mechanics that deals with the motion of objects without considering the forces that cause this motion. It involves the study of concepts such as displacement, velocity, and acceleration.</td>
                    <td>Basic Mathematics, Algebra, Graph Interpretation, Physics Fundamentals</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Linear Algebra</strong></td>
                    <td>Linear Algebra is a branch of mathematics that deals with vectors, vector spaces, and linear transformations, focusing on the study of systems of linear equations and their properties. It is foundational for various fields including engineering, physics, computer science, and economics.</td>
                    <td>Basic Mathematics, Algebra, Geometry, Functions, Matrix Operations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Materials Properties</strong></td>
                    <td>Materials properties refer to the characteristics and behaviors of materials under various conditions, including mechanical, thermal, electrical, and chemical properties. Understanding these properties is essential for selecting materials for specific applications in engineering and design.</td>
                    <td>Basic Chemistry, Physics Fundamentals, Material Science, Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Mathematics Fundamentals</strong></td>
                    <td>Mathematics Fundamentals encompasses the basic concepts and principles of mathematics, including arithmetic, algebra, geometry, and number theory. It serves as the foundation for more advanced mathematical studies and applications.</td>
                    <td>Basic Mathematics, Arithmetic Operations, Algebraic Expressions, Geometric Shapes</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Measurement Units</strong></td>
                    <td>Measurement units are standardized quantities used to express and compare physical properties, such as length, mass, and volume. Understanding measurement units is essential for accurate data interpretation and scientific communication.</td>
                    <td>Basic Mathematics, Unit Conversion, Measurement Concepts</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Number Recognition</strong></td>
                    <td>Number recognition is the ability to identify and understand numerical symbols and their corresponding values. It is a foundational skill in mathematics that supports further learning in arithmetic and number theory.</td>
                    <td>Counting Skills, Basic Mathematics, Symbol Recognition</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Physics Basics</strong></td>
                    <td>Physics Basics covers fundamental concepts and principles that govern the behavior of matter and energy in the universe. It serves as an introduction to key topics such as motion, forces, energy, and waves.</td>
                    <td>Basic Mathematics, Scientific Method, Measurement Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Physics Fundamentals</strong></td>
                    <td>Physics Fundamentals covers the basic principles and concepts of physics, including motion, forces, energy, and the laws governing physical phenomena. It serves as a foundation for further study in various fields of science and engineering.</td>
                    <td>Basic Mathematics, Algebra, Geometry, Scientific Method</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Scientific Method</strong></td>
                    <td>The scientific method is a systematic process used for investigating phenomena, acquiring new knowledge, or correcting and integrating previous knowledge through observation, experimentation, and analysis.</td>
                    <td>Observation Skills, Critical Thinking, Basic Mathematics, Data Analysis</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Scientific Notation</strong></td>
                    <td>Scientific notation is a method of expressing numbers that are too large or too small to be conveniently written in decimal form, using powers of ten. It simplifies calculations and comparisons by representing numbers in the format a × 10^n, where 1 ≤ a < 10 and n is an integer.</td>
                    <td>Basic Mathematics, Exponents, Decimal System</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Signal Processing</strong></td>
                    <td>Signal Processing involves the analysis, manipulation, and interpretation of signals to extract useful information or improve signal quality. It is widely used in various fields such as telecommunications, audio processing, and image analysis.</td>
                    <td>Basic Mathematics, Linear Algebra, Calculus, Probability Theory, Programming Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Soldering Skills</strong></td>
                    <td>Soldering skills involve the techniques and practices used to join electronic components together using solder, a fusible metal alloy. Mastery of these skills is essential for electronics assembly, repair, and prototyping.</td>
                    <td>Basic Electronics, Hand Tool Use, Safety Practices, Circuit Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Solid Mechanics</strong></td>
                    <td>Solid Mechanics is the branch of mechanics that deals with the behavior of solid materials under external forces, including stress, strain, and deformation. It is essential for understanding material properties and structural analysis in engineering applications.</td>
                    <td>Basic Mathematics, Physics Fundamentals, Calculus, Linear Algebra</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Static Equilibrium</strong></td>
                    <td>Static equilibrium refers to the state of an object at rest where the sum of all forces and the sum of all moments acting on it are zero, resulting in no net movement. This concept is fundamental in mechanics and is essential for analyzing structures and systems in engineering.</td>
                    <td>Force Analysis, Newton's Laws, Basic Geometry, Vector Addition</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Statistical Mechanics</strong></td>
                    <td>Statistical Mechanics is a branch of physics that applies statistical methods to study the behavior of systems composed of a large number of particles, linking microscopic properties to macroscopic observables. It provides a framework for understanding thermodynamic phenomena through the statistical distribution of states.</td>
                    <td>Thermodynamics, Classical Mechanics, Calculus, Linear Algebra, Probability Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Systems Theory</strong></td>
                    <td>Systems Theory is an interdisciplinary study that examines complex systems in various fields, focusing on the relationships and interactions between components within a whole. It emphasizes understanding how systems function, adapt, and evolve over time.</td>
                    <td>Systems Thinking, Basic Mathematics, Feedback Loops, Modeling Techniques</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Vector Analysis</strong></td>
                    <td>Vector Analysis is a branch of mathematics that deals with vector fields and operations on vectors, including differentiation and integration in multiple dimensions. It is essential for understanding physical phenomena in fields such as physics and engineering.</td>
                    <td>Calculus, Linear Algebra, Multivariable Functions, Differential Equations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>AC/DC Fundamentals</strong></td>
                    <td>AC/DC Fundamentals covers the principles of alternating current (AC) and direct current (DC) electrical systems, including their characteristics, applications, and differences. This topic is essential for understanding electrical engineering and circuit design.</td>
                    <td>Basic Electricity, Circuit Theory, Ohm's Law, Waveform Analysis</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Algebra Basics</strong></td>
                    <td>Algebra Basics introduces fundamental concepts of algebra, including variables, expressions, and equations, which are essential for solving mathematical problems. It serves as a foundation for more advanced topics in mathematics.</td>
                    <td>Basic Mathematics, Arithmetic Operations, Number Properties</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Algebraic Expressions</strong></td>
                    <td>Algebraic expressions are mathematical phrases that include numbers, variables, and operators, used to represent relationships and calculations. They form the foundation for more complex algebraic concepts and problem-solving techniques.</td>
                    <td>Basic Mathematics, Variables, Arithmetic Operations, Order of Operations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Atomic Theory</strong></td>
                    <td>Atomic Theory is a scientific framework that describes the nature of matter, stating that all matter is composed of atoms, which are the fundamental building blocks of chemical substances. It encompasses the structure, behavior, and interactions of atoms as well as their role in chemical reactions.</td>
                    <td>Basic Chemistry, Basic Physics, Scientific Method, Mathematics Fundamentals</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Basic Electronics</strong></td>
                    <td>Basic Electronics covers fundamental concepts of electrical circuits, components, and principles, including voltage, current, resistance, and the behavior of passive and active components. It serves as a foundation for understanding more complex electronic systems and devices.</td>
                    <td>Basic Mathematics, Physics Fundamentals, Circuit Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Chemical Bonding</strong></td>
                    <td>Chemical bonding refers to the attractive forces that hold atoms together in molecules and compounds, primarily through ionic, covalent, and metallic bonds. Understanding these interactions is essential for explaining the properties and behaviors of substances in chemistry.</td>
                    <td>Atomic Structure, Periodic Table, Chemical Reactions, Electronegativity</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Chemical Reactions</strong></td>
                    <td>Chemical reactions are processes in which substances (reactants) undergo a transformation to form new substances (products) through the breaking and forming of chemical bonds. Understanding these reactions is fundamental to the study of chemistry and various scientific applications.</td>
                    <td>Atomic Structure, Chemical Bonds, Stoichiometry, Balancing Equations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Classical Mechanics</strong></td>
                    <td>Classical Mechanics is a branch of physics that deals with the motion of objects and the forces acting on them, providing the foundational principles that govern the behavior of macroscopic systems. It encompasses concepts such as Newton's laws of motion, energy, and momentum.</td>
                    <td>Basic Mathematics, Algebra, Trigonometry, Calculus, Physics Fundamentals</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Complex Numbers</strong></td>
                    <td>Complex numbers are numbers that comprise a real part and an imaginary part, typically expressed in the form a + bi, where 'a' is the real part and 'b' is the imaginary part. They are used in various fields such as engineering, physics, and applied mathematics to solve equations that have no real solutions.</td>
                    <td>Basic Mathematics, Algebra, Imaginary Numbers, Coordinate Systems</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Counting Skills</strong></td>
                    <td>Counting skills involve the ability to recognize, understand, and manipulate numbers in a sequential manner, forming the foundation for more advanced mathematical concepts. Mastery of counting is essential for developing numeracy and problem-solving abilities in various contexts.</td>
                    <td>Number Recognition, Basic Mathematics, Ordinal Numbers, One-to-One Correspondence</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Critical Thinking</strong></td>
                    <td>Critical thinking is the ability to analyze information, evaluate evidence, and form reasoned conclusions. It involves questioning assumptions and considering alternative viewpoints to enhance decision-making and problem-solving skills.</td>
                    <td>Logical Reasoning, Analytical Skills, Argument Evaluation, Problem Solving</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Data Analysis</strong></td>
                    <td>Data Analysis involves inspecting, cleansing, transforming, and modeling data to discover useful information, inform conclusions, and support decision-making. It encompasses various techniques and tools to interpret data effectively.</td>
                    <td>Statistics, Basic Mathematics, Data Visualization, Programming Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Decimal System</strong></td>
                    <td>The decimal system is a base-10 numeral system that uses ten digits (0-9) to represent numbers. It is the most widely used number system in the world, facilitating arithmetic operations and numerical representation.</td>
                    <td>Basic Mathematics, Number Recognition, Place Value</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Electromagnetic Theory</strong></td>
                    <td>Electromagnetic Theory is the branch of physics that studies the interactions between electric charges and magnetic fields, encompassing the principles of electricity, magnetism, and light. It provides a framework for understanding how electric and magnetic fields propagate and interact with matter.</td>
                    <td>Classical Mechanics, Vector Calculus, Differential Equations, Basic Electricity, Wave Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Electron Configuration</strong></td>
                    <td>Electron configuration refers to the distribution of electrons in an atom's orbitals, which determines its chemical properties and reactivity. It is represented using a notation that indicates the energy levels and sublevels occupied by electrons.</td>
                    <td>Atomic Structure, Quantum Mechanics, Periodic Table, Chemical Bonding</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Energy Conservation</strong></td>
                    <td>Energy conservation refers to the practice of reducing energy consumption through using less energy service or using energy more efficiently. It plays a crucial role in sustainability and environmental protection by minimizing waste and lowering carbon footprints.</td>
                    <td>Basic Physics, Thermodynamics, Energy Types, Environmental Science</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Equation Solving</strong></td>
                    <td>Equation solving is the process of finding the values of variables that satisfy a given mathematical equation. It involves applying various techniques and methods to manipulate and simplify equations to isolate the variable of interest.</td>
                    <td>Basic Mathematics, Algebraic Expressions, Inequalities, Functions</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Exponents</strong></td>
                    <td>Exponents represent the number of times a base is multiplied by itself, providing a concise way to express large numbers and perform calculations involving powers. Understanding exponents is essential for advanced topics in algebra and mathematics.</td>
                    <td>Basic Mathematics, Multiplication Skills, Order of Operations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Feedback Loops</strong></td>
                    <td>Feedback loops are processes in which the output of a system is circled back and used as input, influencing future outputs. They are essential in understanding dynamic systems in various fields, including biology, engineering, and social sciences.</td>
                    <td>Systems Theory, Dynamic Systems, Basic Mathematics, Statistical Analysis</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Force Analysis</strong></td>
                    <td>Force Analysis is the study of forces acting on objects, focusing on their magnitudes, directions, and effects on motion. It is a fundamental concept in physics and engineering that helps in understanding static and dynamic systems.</td>
                    <td>Newton's Laws, Vector Analysis, Basic Mathematics, Static Equilibrium</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Functions</strong></td>
                    <td>Functions are mathematical entities that relate an input to a single output, often represented as f(x), and are fundamental in both mathematics and programming for structuring code and solving problems.</td>
                    <td>Basic Mathematics, Algebra, Graphing Skills, Logical Operators</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Geometric Shapes</strong></td>
                    <td>Geometric shapes are fundamental figures in mathematics defined by their boundaries and dimensions, including points, lines, angles, and various polygons. Understanding these shapes is essential for exploring more complex mathematical concepts and applications in geometry.</td>
                    <td>Basic Mathematics, Understanding of Angles, Introduction to Geometry</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Graph Interpretation</strong></td>
                    <td>Graph interpretation involves analyzing and extracting meaningful information from graphical representations of data, such as charts and plots. It is essential for making informed decisions based on visual data insights.</td>
                    <td>Data Visualization, Basic Statistics, Graph Theory, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Graphing</strong></td>
                    <td>Graphing is the visual representation of data or mathematical functions on a coordinate system, allowing for the analysis of relationships and trends. It is a fundamental skill in mathematics and data analysis that aids in interpreting quantitative information.</td>
                    <td>Coordinate Systems, Basic Mathematics, Algebraic Functions, Data Interpretation</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Hand Tool Use</strong></td>
                    <td>Hand tool use involves the practical application and manipulation of tools designed for manual operation, emphasizing skills in precision, safety, and technique. Mastery of hand tools is essential for various trades and crafts, including woodworking, metalworking, and construction.</td>
                    <td>Tool Safety, Basic Measurements, Material Properties, Hand-Eye Coordination</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Limits</strong></td>
                    <td>Limits are a fundamental concept in calculus that describe the behavior of a function as its input approaches a certain value. They are essential for understanding continuity, derivatives, and integrals.</td>
                    <td>Functions, Algebra, Graphing, Inequalities</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Logical Reasoning</strong></td>
                    <td>Logical reasoning is the process of using structured thinking to analyze arguments, identify valid conclusions, and solve problems based on given premises. It is essential for critical thinking and decision-making in various academic and real-world contexts.</td>
                    <td>Critical Thinking, Basic Mathematics, Argument Analysis, Logical Operators</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Mathematics</strong></td>
                    <td>Mathematics is the study of numbers, shapes, and patterns, encompassing various branches such as arithmetic, algebra, geometry, and calculus. It serves as a foundational discipline that supports numerous fields including science, engineering, and economics.</td>
                    <td>Basic Mathematics, Logical Operators, Problem Solving, Numerical Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Matrix Operations</strong></td>
                    <td>Matrix operations involve mathematical procedures such as addition, subtraction, and multiplication of matrices, which are rectangular arrays of numbers. Understanding these operations is essential for various applications in linear algebra, computer science, and engineering.</td>
                    <td>Basic Mathematics, Algebra, Linear Equations, Functions</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Measurement Concepts</strong></td>
                    <td>Measurement concepts encompass the principles and methods used to quantify physical properties, enabling the comparison and analysis of data across various fields. This topic includes understanding units, scales, and the significance of accuracy and precision in measurements.</td>
                    <td>Basic Mathematics, Data Analysis, Measurement Units, Scientific Notation</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Measurement Skills</strong></td>
                    <td>Measurement skills involve the ability to accurately assess and quantify physical attributes such as length, weight, volume, and time using appropriate tools and techniques. These skills are essential for various fields, including science, engineering, and everyday life.</td>
                    <td>Basic Mathematics, Unit Conversion, Data Interpretation, Measurement Tools</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Mechanical Energy</strong></td>
                    <td>Mechanical energy is the sum of potential energy and kinetic energy in a system, representing the energy associated with the motion and position of an object. It is a key concept in physics that helps explain how energy is conserved and transformed in mechanical systems.</td>
                    <td>Kinematics, Dynamics, Work-Energy Principle, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Modeling Techniques</strong></td>
                    <td>Modeling techniques involve the use of mathematical and computational methods to represent real-world systems and processes, enabling analysis and prediction. These techniques are essential in fields such as engineering, computer science, and data science.</td>
                    <td>Basic Mathematics, Statistics, Programming Basics, Data Analysis</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Multivariable Functions</strong></td>
                    <td>Multivariable functions are mathematical functions that take two or more variables as inputs and produce a single output, often represented as f(x, y, z). They are essential in fields such as calculus, physics, and engineering for modeling complex systems.</td>
                    <td>Single Variable Calculus, Basic Algebra, Coordinate Geometry, Limits and Continuity</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Newton's Laws</strong></td>
                    <td>Newton's Laws are three fundamental principles that describe the relationship between the motion of an object and the forces acting on it. They form the foundation of classical mechanics and are essential for understanding how objects behave in various physical situations.</td>
                    <td>Basic Mathematics, Force Concepts, Motion Principles, Vector Understanding</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Number Properties</strong></td>
                    <td>Number properties refer to the fundamental characteristics and rules that govern numbers, including concepts such as commutativity, associativity, distributivity, and the properties of integers, rational numbers, and real numbers.</td>
                    <td>Basic Mathematics, Arithmetic Operations, Understanding of Integers, Rational Numbers</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Observation Skills</strong></td>
                    <td>Observation skills involve the ability to notice and interpret details in various environments, enhancing understanding and learning. These skills are essential for effective communication, critical thinking, and problem-solving.</td>
                    <td>Attention to Detail, Critical Thinking, Analytical Skills, Communication Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Periodic Table</strong></td>
                    <td>The Periodic Table is a systematic arrangement of chemical elements, organized by increasing atomic number, which highlights the relationships and properties of the elements. It serves as a fundamental tool in chemistry for understanding element behavior and interactions.</td>
                    <td>Atomic Structure, Chemical Bonds, Element Properties, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Probability Theory</strong></td>
                    <td>Probability Theory is the branch of mathematics that deals with the analysis of random phenomena and the quantification of uncertainty. It provides the foundational framework for statistical inference and decision-making under uncertainty.</td>
                    <td>Basic Mathematics, Algebra, Set Theory, Calculus</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Programming Basics</strong></td>
                    <td>Programming Basics introduces fundamental concepts of coding, including syntax, data types, and control structures, enabling learners to write simple programs. It serves as the foundation for more advanced programming topics and languages.</td>
                    <td>Basic Mathematics, Logical Operators, Problem-Solving Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Safety Practices</strong></td>
                    <td>Safety practices encompass the guidelines and procedures designed to minimize risks and ensure the well-being of individuals in various environments, including workplaces, homes, and public spaces.</td>
                    <td>Risk Assessment, Emergency Procedures, First Aid Basics, Health Regulations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Spatial Reasoning</strong></td>
                    <td>Spatial reasoning is the ability to visualize and manipulate objects in a three-dimensional space, crucial for problem-solving in fields such as mathematics, engineering, and architecture.</td>
                    <td>Geometry, Basic Mathematics, Logical Thinking, Visualization Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Stoichiometry</strong></td>
                    <td>Stoichiometry is the branch of chemistry that deals with the calculation of reactants and products in chemical reactions based on the conservation of mass. It involves using balanced chemical equations to determine the quantitative relationships between substances involved in reactions.</td>
                    <td>Chemical Equations, Mole Concept, Basic Mathematics, Balancing Reactions</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Symbol Recognition</strong></td>
                    <td>Symbol recognition is the process of identifying and interpreting symbols, such as letters, numbers, or graphical representations, in various contexts. It plays a crucial role in fields like computer vision, natural language processing, and cognitive psychology.</td>
                    <td>Pattern Recognition, Image Processing, Machine Learning, Cognitive Psychology</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Symmetry Operations</strong></td>
                    <td>Symmetry operations are mathematical transformations that leave an object invariant under certain conditions, commonly used in fields such as chemistry and physics to analyze molecular structures and crystal lattices. These operations include translations, rotations, reflections, and inversions, which help in understanding the symmetry properties of objects.</td>
                    <td>Basic Geometry, Linear Algebra, Group Theory, Mathematical Transformations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Systems Thinking</strong></td>
                    <td>Systems Thinking is an approach to understanding complex systems by examining the interrelationships and interactions among their components, rather than viewing them in isolation. It emphasizes holistic analysis and the dynamic nature of systems over time.</td>
                    <td>Systems Theory, Critical Thinking, Basic Mathematics, Problem Solving</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Trigonometry</strong></td>
                    <td>Trigonometry is a branch of mathematics that studies the relationships between the angles and sides of triangles, particularly right triangles. It is essential for understanding concepts in geometry, physics, engineering, and various applied sciences.</td>
                    <td>Basic Mathematics, Geometry, Algebra</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Understanding of Symbols</strong></td>
                    <td>Understanding of symbols involves recognizing and interpreting various signs, characters, and representations used in communication, mathematics, and logic. This foundational skill is essential for effective reasoning and problem-solving across multiple disciplines.</td>
                    <td>Basic Literacy, Mathematical Symbols, Logical Reasoning, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Unit Cells</strong></td>
                    <td>Unit cells are the smallest repeating units in a crystal lattice that define the structure and symmetry of a crystal. They are fundamental in understanding crystallography and material properties.</td>
                    <td>Crystallography, Atomic Structure, Symmetry Principles, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Unit Conversion</strong></td>
                    <td>Unit conversion is the process of converting a quantity expressed in one set of units to another, ensuring accurate measurement and communication in various fields such as science, engineering, and everyday life.</td>
                    <td>Basic Mathematics, Measurement Concepts, Dimensional Analysis</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Vector Addition</strong></td>
                    <td>Vector addition is the process of combining two or more vectors to produce a resultant vector, taking into account both their magnitudes and directions. It is a fundamental operation in physics and engineering, essential for analyzing forces, velocities, and other vector quantities.</td>
                    <td>Vector Basics, Coordinate Systems, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Algebraic Functions</strong></td>
                    <td>Algebraic functions are mathematical expressions that involve variables and constants combined using algebraic operations such as addition, subtraction, multiplication, division, and exponentiation. They can be represented in various forms, including polynomial, rational, and radical functions.</td>
                    <td>Basic Mathematics, Algebraic Expressions, Function Concepts, Graphing Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Analytical Skills</strong></td>
                    <td>Analytical skills refer to the ability to collect, process, and interpret data to make informed decisions or solve problems. These skills involve critical thinking, logical reasoning, and the ability to evaluate information effectively.</td>
                    <td>Critical Thinking, Logical Reasoning, Data Interpretation, Problem Solving</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Argument Analysis</strong></td>
                    <td>Argument Analysis involves evaluating the structure and validity of arguments, identifying premises and conclusions, and assessing the strength of reasoning. It is a critical skill in logic, philosophy, and effective communication.</td>
                    <td>Critical Thinking, Logical Operators, Basic Logic, Reading Comprehension</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Argument Evaluation</strong></td>
                    <td>Argument Evaluation involves assessing the validity and soundness of arguments by analyzing their structure, premises, and conclusions. It is a critical skill in logic, debate, and critical thinking.</td>
                    <td>Logical Operators, Critical Thinking, Basic Logic, Argument Structure</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Attention to Detail</strong></td>
                    <td>Attention to Detail refers to the ability to notice and accurately process small elements within a larger context, which is crucial for tasks requiring precision and thoroughness. This skill enhances overall quality and effectiveness in various academic and professional settings.</td>
                    <td>Critical Thinking, Observation Skills, Time Management, Organizational Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Balancing Equations</strong></td>
                    <td>Balancing equations is the process of ensuring that the number of atoms of each element is the same on both sides of a chemical equation, reflecting the law of conservation of mass. This skill is essential for understanding chemical reactions and stoichiometry.</td>
                    <td>Chemical Symbols, Basic Mathematics, Understanding of Reactions, Stoichiometry Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Balancing Reactions</strong></td>
                    <td>Balancing reactions involves ensuring that the number of atoms for each element is the same on both sides of a chemical equation, adhering to the law of conservation of mass. This process is essential for accurately representing chemical reactions in stoichiometry.</td>
                    <td>Chemical Equations, Stoichiometry, Basic Chemistry, Atomic Structure</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Basic Algebra</strong></td>
                    <td>Basic Algebra involves the study of mathematical symbols and the rules for manipulating these symbols to solve equations and inequalities. It serves as a foundational skill for higher-level mathematics and various real-world applications.</td>
                    <td>Basic Mathematics, Arithmetic Operations, Understanding of Variables</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Basic Literacy</strong></td>
                    <td>Basic literacy refers to the ability to read and write at a fundamental level, enabling individuals to understand and communicate effectively through written language. It serves as a foundational skill necessary for further education and daily functioning in society.</td>
                    <td>Phonemic Awareness, Vocabulary Skills, Reading Comprehension, Writing Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Basic Measurements</strong></td>
                    <td>Basic measurements involve understanding and applying fundamental concepts of quantifying physical properties such as length, mass, and volume using standard units. This topic serves as a foundation for more advanced studies in science, engineering, and mathematics.</td>
                    <td>Basic Mathematics, Unit Conversion, Measurement Tools</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Basic Statistics</strong></td>
                    <td>Basic Statistics involves the collection, analysis, interpretation, presentation, and organization of data. It provides foundational tools for understanding data distributions, central tendencies, and variability.</td>
                    <td>Basic Mathematics, Data Interpretation, Graphical Representation</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Chemical Bonds</strong></td>
                    <td>Chemical bonds are the forces that hold atoms together in molecules and compounds, primarily through ionic, covalent, and metallic interactions. Understanding these bonds is crucial for studying chemical reactions and material properties.</td>
                    <td>Atomic Structure, Periodic Table, Chemical Reactions, Electronegativity</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Chemical Equations</strong></td>
                    <td>Chemical equations are symbolic representations of chemical reactions, showing the reactants and products along with their respective quantities. They are essential for understanding the stoichiometry and conservation of mass in chemical processes.</td>
                    <td>Chemical Symbols, Balancing Equations, Stoichiometry, Basic Chemistry</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Cognitive Psychology</strong></td>
                    <td>Cognitive Psychology is the scientific study of mental processes such as perception, memory, reasoning, and decision-making. It explores how people understand, think, and remember information.</td>
                    <td>Psychology Basics, Research Methods, Statistics, Neuroscience Fundamentals</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Communication Skills</strong></td>
                    <td>Communication skills encompass the ability to convey information effectively and efficiently through verbal, non-verbal, and written means. Mastery of these skills is essential for personal and professional interactions.</td>
                    <td>Active Listening, Emotional Intelligence, Public Speaking, Interpersonal Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Coordinate Geometry</strong></td>
                    <td>Coordinate Geometry, also known as analytic geometry, is the study of geometric figures using a coordinate system, allowing for the representation of shapes and the calculation of distances and angles. It combines algebra and geometry to analyze and solve geometric problems in a numerical format.</td>
                    <td>Basic Mathematics, Algebra, Geometry, Graphing Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Coordinate Systems</strong></td>
                    <td>Coordinate systems are frameworks that use numerical values to define the position of points in space, typically represented in two or three dimensions. They are essential for various fields such as mathematics, physics, and computer graphics.</td>
                    <td>Basic Mathematics, Geometry, Graphing Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Crystallography</strong></td>
                    <td>Crystallography is the study of crystal structures and their properties, focusing on the arrangement of atoms within crystalline materials. It plays a crucial role in fields such as chemistry, physics, and materials science.</td>
                    <td>Basic Chemistry, Solid State Physics, Mathematics, Geometry, X-ray Diffraction</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Data Interpretation</strong></td>
                    <td>Data Interpretation involves analyzing and making sense of data presented in various formats, such as graphs, tables, and charts, to draw meaningful conclusions and insights. It is a critical skill in fields like statistics, research, and business analytics.</td>
                    <td>Basic Statistics, Graph Literacy, Mathematical Reasoning, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Data Visualization</strong></td>
                    <td>Data Visualization is the graphical representation of information and data, using visual elements like charts, graphs, and maps to make complex data more accessible and understandable. It helps in identifying patterns, trends, and insights from data sets.</td>
                    <td>Data Analysis, Statistical Concepts, Basic Mathematics, Graphic Design Principles</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Dimensional Analysis</strong></td>
                    <td>Dimensional Analysis is a mathematical technique used to convert between different units of measurement by analyzing the dimensions of physical quantities. It helps ensure that equations are dimensionally consistent and can be used to derive relationships between variables.</td>
                    <td>Basic Mathematics, Algebra, Units of Measurement, Physical Quantities</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Dynamic Systems</strong></td>
                    <td>Dynamic Systems is the study of systems that evolve over time according to specific rules, often described by differential equations. It encompasses the analysis of stability, control, and behavior of these systems in various fields such as engineering, physics, and biology.</td>
                    <td>Differential Equations, Linear Algebra, Calculus, Basic Physics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Dynamics</strong></td>
                    <td>Dynamics is the branch of mechanics that deals with the motion of objects and the forces that affect this motion. It encompasses the study of how forces influence the behavior of physical systems over time.</td>
                    <td>Newton's Laws, Basic Mathematics, Vector Analysis, Kinematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Electronegativity</strong></td>
                    <td>Electronegativity is a measure of an atom's ability to attract and hold onto electrons in a chemical bond. It plays a crucial role in determining the nature of chemical bonds and molecular interactions.</td>
                    <td>Atomic Structure, Chemical Bonding, Periodic Table, Valence Electrons</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Element Properties</strong></td>
                    <td>Element properties refer to the characteristics and behaviors of chemical elements, including their atomic structure, reactivity, and physical properties such as melting and boiling points. Understanding these properties is essential for studying chemical reactions and material science.</td>
                    <td>Atomic Structure, Periodic Table, Chemical Bonds, Basic Chemistry</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Emergency Procedures</strong></td>
                    <td>Emergency Procedures encompass the protocols and actions that individuals and organizations must follow during emergencies to ensure safety and minimize harm. This includes preparation, response, and recovery strategies tailored to various emergency scenarios.</td>
                    <td>Safety Awareness, Risk Assessment, First Aid Basics, Crisis Communication</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Environmental Science</strong></td>
                    <td>Environmental Science is the study of interactions between the physical, chemical, and biological components of the environment, focusing on issues such as pollution, resource management, and sustainability. It integrates knowledge from various disciplines to address environmental challenges and promote ecological health.</td>
                    <td>Biology, Chemistry, Ecology, Geology, Statistics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>First Aid Basics</strong></td>
                    <td>First Aid Basics covers essential techniques and procedures for providing immediate care to individuals experiencing medical emergencies. It includes skills such as CPR, wound care, and recognizing signs of serious conditions.</td>
                    <td>Basic Health Knowledge, Emergency Response Awareness, Anatomy Fundamentals</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Force Concepts</strong></td>
                    <td>Force concepts encompass the fundamental principles of force, including its definition, types, and effects on motion and equilibrium. Understanding these concepts is essential for analyzing physical systems in mechanics.</td>
                    <td>Newton's Laws, Basic Mathematics, Vector Analysis, Kinematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Graph Theory</strong></td>
                    <td>Graph Theory is a branch of mathematics that studies the properties and relationships of graphs, which are structures made up of vertices (nodes) connected by edges (lines). It has applications in computer science, biology, social sciences, and more.</td>
                    <td>Set Theory, Basic Mathematics, Discrete Mathematics, Logic</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Graphing Skills</strong></td>
                    <td>Graphing skills involve the ability to create, interpret, and analyze visual representations of data, such as charts and graphs. These skills are essential for effectively communicating quantitative information in various academic and professional contexts.</td>
                    <td>Basic Mathematics, Data Interpretation, Coordinate Systems, Algebra Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Group Theory</strong></td>
                    <td>Group Theory is a branch of mathematics that studies algebraic structures known as groups, which consist of a set equipped with an operation that satisfies certain axioms. It is fundamental in various fields such as abstract algebra, geometry, and physics.</td>
                    <td>Set Theory, Abstract Algebra, Mathematical Proofs, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Hand-Eye Coordination</strong></td>
                    <td>Hand-eye coordination is the ability to synchronize visual input with hand movements, enabling precise actions such as writing, typing, or playing sports. It is crucial for various daily activities and skill development.</td>
                    <td>Visual Perception, Motor Skills, Spatial Awareness</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Health Regulations</strong></td>
                    <td>Health regulations are laws and guidelines established to protect public health by ensuring the safety and efficacy of health services, products, and environments. They encompass a wide range of areas including food safety, pharmaceuticals, and environmental health standards.</td>
                    <td>Public Health Principles, Health Policy, Legal Frameworks, Ethics in Healthcare</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Image Processing</strong></td>
                    <td>Image Processing involves the manipulation and analysis of digital images using algorithms to enhance, transform, or extract information from them. It is widely used in various fields such as computer vision, medical imaging, and remote sensing.</td>
                    <td>Linear Algebra, Calculus, Programming Basics, Signal Processing</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Imaginary Numbers</strong></td>
                    <td>Imaginary numbers are a class of numbers that extend the real number system, defined as multiples of the imaginary unit 'i', where i is the square root of -1. They are used in various fields, including engineering and physics, to solve equations that do not have real solutions.</td>
                    <td>Complex Numbers, Algebra, Quadratic Equations, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Inequalities</strong></td>
                    <td>Inequalities are mathematical expressions that describe the relationship between two values, indicating that one is greater than, less than, or not equal to the other. They are fundamental in algebra and are used to solve problems involving ranges and limits.</td>
                    <td>Basic Mathematics, Algebra Fundamentals, Number Line Concepts, Graphing Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Introduction to Geometry</strong></td>
                    <td>Introduction to Geometry covers the fundamental concepts of shapes, sizes, relative positions of figures, and the properties of space. It serves as a foundation for more advanced studies in mathematics and related fields.</td>
                    <td>Basic Mathematics, Algebra Basics, Logical Reasoning</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Limits and Continuity</strong></td>
                    <td>Limits and Continuity are fundamental concepts in calculus that describe the behavior of functions as they approach specific points or values, helping to determine whether a function is continuous at those points.</td>
                    <td>Function Basics, Graphing Functions, Algebraic Manipulation, Inequalities</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Linear Equations</strong></td>
                    <td>Linear equations are mathematical statements that express the relationship between two variables using a straight line on a graph, typically in the form y = mx + b. They are foundational in algebra and are used to model real-world situations involving constant rates of change.</td>
                    <td>Basic Mathematics, Algebraic Expressions, Graphing Skills, Arithmetic Operations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Logical Operators</strong></td>
                    <td>Logical operators are symbols or words used to connect two or more expressions in logic, enabling the formation of complex logical statements. Common logical operators include AND, OR, and NOT, which are fundamental in programming and mathematical logic.</td>
                    <td>Boolean Algebra, Basic Mathematics, Programming Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Logical Thinking</strong></td>
                    <td>Logical thinking is the ability to reason systematically and make connections between concepts, enabling individuals to solve problems and make informed decisions. It involves analyzing information, identifying patterns, and drawing conclusions based on evidence.</td>
                    <td>Critical Thinking, Basic Mathematics, Problem Solving, Analytical Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Machine Learning</strong></td>
                    <td>Machine Learning is a subset of artificial intelligence that focuses on the development of algorithms that allow computers to learn from and make predictions based on data. It involves the use of statistical techniques to enable machines to improve their performance on tasks through experience.</td>
                    <td>Statistics, Linear Algebra, Programming Basics, Data Analysis, Calculus</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Material Properties</strong></td>
                    <td>Material properties refer to the characteristics of materials that define their behavior under various conditions, including mechanical, thermal, electrical, and chemical properties. Understanding these properties is essential for selecting appropriate materials for engineering and design applications.</td>
                    <td>Basic Chemistry, Physics Fundamentals, Mathematics, Engineering Principles</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Mathematical Symbols</strong></td>
                    <td>Mathematical symbols are standardized notations used to represent numbers, operations, relations, and functions in mathematics. They provide a concise way to express mathematical ideas and facilitate communication among mathematicians and students.</td>
                    <td>Basic Mathematics, Algebra Fundamentals, Logical Operators, Set Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Mathematical Transformations</strong></td>
                    <td>Mathematical transformations are operations that alter the position, size, or shape of a function or geometric figure, often represented through functions like translation, rotation, reflection, and scaling. These transformations are essential in various fields such as geometry, algebra, and calculus.</td>
                    <td>Basic Mathematics, Algebra, Geometry, Functions</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Measurement Tools</strong></td>
                    <td>Measurement tools are instruments or methods used to quantify physical quantities, such as length, mass, volume, and time, enabling accurate data collection and analysis in various fields. They are essential for scientific experiments, engineering applications, and quality control processes.</td>
                    <td>Basic Mathematics, Data Analysis, Scientific Method, Measurement Concepts</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Mole Concept</strong></td>
                    <td>The mole concept is a fundamental principle in chemistry that relates the amount of substance to its mass and the number of particles, allowing for quantitative analysis in chemical reactions. It provides a bridge between the atomic scale and macroscopic measurements.</td>
                    <td>Atomic Structure, Chemical Reactions, Basic Mathematics, Stoichiometry</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Motion Principles</strong></td>
                    <td>Motion Principles encompass the fundamental concepts and laws that describe the behavior of objects in motion, including the effects of forces, acceleration, and the relationship between distance and time.</td>
                    <td>Newton's Laws, Kinematics, Basic Mathematics, Vector Analysis</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Multiplication Skills</strong></td>
                    <td>Multiplication skills involve the ability to efficiently and accurately perform multiplication operations, which are fundamental to various mathematical concepts and real-world applications. Mastery of these skills is essential for advancing in mathematics and problem-solving.</td>
                    <td>Addition Skills, Number Recognition, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Numerical Skills</strong></td>
                    <td>Numerical skills refer to the ability to understand, interpret, and work with numbers effectively. These skills are essential for problem-solving in various academic and real-world contexts.</td>
                    <td>Basic Mathematics, Arithmetic Operations, Data Interpretation, Algebra Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>One-to-One Correspondence</strong></td>
                    <td>One-to-one correspondence is a mathematical concept that describes a relationship between two sets where each element of one set is paired with exactly one element of the other set, and vice versa. This concept is fundamental in understanding functions, cardinality, and equivalence between sets.</td>
                    <td>Set Theory, Functions, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Order of Operations</strong></td>
                    <td>The order of operations is a mathematical rule that dictates the sequence in which different operations should be performed to accurately solve expressions. It is commonly remembered by the acronym PEMDAS, which stands for Parentheses, Exponents, Multiplication and Division (from left to right), Addition and Subtraction (from left to right).</td>
                    <td>Basic Mathematics, Arithmetic Operations, Understanding of Variables</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Ordinal Numbers</strong></td>
                    <td>Ordinal numbers are numbers that represent the position or rank of an item in a sequence, such as first, second, or third. They are used to indicate order rather than quantity.</td>
                    <td>Natural Numbers, Basic Counting, Set Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Pattern Recognition</strong></td>
                    <td>Pattern recognition is the process of identifying and classifying patterns in data, often using algorithms and statistical techniques. It is widely applied in fields such as machine learning, computer vision, and artificial intelligence.</td>
                    <td>Statistics, Linear Algebra, Calculus, Programming Basics, Machine Learning Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Place Value</strong></td>
                    <td>Place value is a numerical system that assigns a value to a digit based on its position within a number, which is essential for understanding the magnitude of numbers in our base-10 system.</td>
                    <td>Number Recognition, Basic Counting, Understanding of Digits, Decimal System</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Problem Solving</strong></td>
                    <td>Problem solving is the process of identifying a challenge or opportunity, analyzing it, and developing effective strategies to address it. This skill is essential across various disciplines and is fundamental to critical thinking and decision-making.</td>
                    <td>Critical Thinking, Analytical Skills, Basic Mathematics, Logical Operators</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Problem-Solving Skills</strong></td>
                    <td>Problem-solving skills refer to the ability to identify, analyze, and resolve challenges effectively and efficiently. These skills are essential for critical thinking and decision-making in various contexts.</td>
                    <td>Critical Thinking, Analytical Skills, Creativity, Basic Mathematics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Quantum Mechanics</strong></td>
                    <td>Quantum Mechanics is a fundamental branch of physics that deals with the behavior of matter and energy at the smallest scales, such as atoms and subatomic particles. It introduces concepts such as wave-particle duality, quantization, and the uncertainty principle.</td>
                    <td>Classical Mechanics, Linear Algebra, Calculus, Probability Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Rational Numbers</strong></td>
                    <td>Rational numbers are numbers that can be expressed as the quotient or fraction of two integers, where the denominator is not zero. They include integers, fractions, and finite or repeating decimals.</td>
                    <td>Basic Mathematics, Understanding of Fractions, Integer Concepts</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Risk Assessment</strong></td>
                    <td>Risk Assessment is the systematic process of identifying, analyzing, and evaluating potential risks that could negatively impact an organization's operations or objectives. It involves assessing the likelihood and consequences of adverse events to inform decision-making and risk management strategies.</td>
                    <td>Risk Management, Statistical Analysis, Data Interpretation, Critical Thinking</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Set Theory</strong></td>
                    <td>Set Theory is a branch of mathematical logic that studies sets, which are collections of objects. It provides the foundational framework for various areas of mathematics, including functions, relations, and cardinality.</td>
                    <td>Basic Mathematics, Logical Operators, Functions, Relations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Single Variable Calculus</strong></td>
                    <td>Single Variable Calculus is the branch of mathematics that deals with the differentiation and integration of functions with a single variable. It focuses on understanding rates of change and the accumulation of quantities.</td>
                    <td>Algebra, Geometry, Trigonometry, Functions</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Statistical Analysis</strong></td>
                    <td>Statistical Analysis involves the collection, examination, interpretation, presentation, and organization of data to uncover patterns and insights. It is essential for making informed decisions based on quantitative information.</td>
                    <td>Basic Mathematics, Probability Theory, Data Interpretation, Graphing Techniques</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Statistics</strong></td>
                    <td>Statistics is the branch of mathematics that deals with collecting, analyzing, interpreting, presenting, and organizing data. It provides tools for making inferences and decisions based on data sets.</td>
                    <td>Basic Mathematics, Algebra, Data Interpretation, Probability Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Symmetry Principles</strong></td>
                    <td>Symmetry principles are fundamental concepts in various fields such as physics, mathematics, and art, which describe the invariance of systems under certain transformations. They play a crucial role in understanding conservation laws and the structure of physical theories.</td>
                    <td>Basic Mathematics, Geometry, Linear Algebra, Group Theory</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Tool Safety</strong></td>
                    <td>Tool safety encompasses the practices and precautions necessary to prevent accidents and injuries while using hand and power tools. It includes understanding proper usage, maintenance, and protective measures to ensure a safe working environment.</td>
                    <td>Basic Tool Knowledge, Safety Procedures, Risk Assessment, Personal Protective Equipment</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Understanding of Angles</strong></td>
                    <td>Understanding of angles involves recognizing, measuring, and applying the properties of angles in various contexts, including geometry and real-world applications. This foundational concept is essential for further studies in mathematics and related fields.</td>
                    <td>Basic Geometry, Measurement Skills, Spatial Awareness</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Understanding of Integers</strong></td>
                    <td>Understanding of integers involves recognizing and working with whole numbers, both positive and negative, including zero. This foundational concept is essential for more advanced mathematical operations and number theory.</td>
                    <td>Whole Numbers, Basic Addition, Basic Subtraction, Number Line</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Variables</strong></td>
                    <td>Variables are symbolic names that represent data values in programming and mathematics, allowing for the storage and manipulation of information. They serve as placeholders for data that can change during program execution or mathematical calculations.</td>
                    <td>Data Types, Basic Mathematics, Programming Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Vector Basics</strong></td>
                    <td>Vector Basics covers the fundamental concepts of vectors, including their representation, operations such as addition and scalar multiplication, and applications in various fields like physics and computer graphics.</td>
                    <td>Basic Mathematics, Coordinate Systems, Algebra Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Vector Calculus</strong></td>
                    <td>Vector Calculus is a branch of mathematics that deals with vector fields and the differentiation and integration of vector functions. It is essential for understanding physical phenomena in fields such as physics and engineering.</td>
                    <td>Calculus, Linear Algebra, Multivariable Functions, Analytic Geometry</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Vector Understanding</strong></td>
                    <td>Vector Understanding refers to the comprehension of vectors as mathematical entities that have both magnitude and direction, and their applications in various fields such as physics and engineering.</td>
                    <td>Basic Mathematics, Algebra, Geometry, Trigonometry</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Visualization Skills</strong></td>
                    <td>Visualization skills refer to the ability to create mental images and interpret visual information effectively, which is essential for problem-solving and understanding complex concepts in various fields such as mathematics, science, and design.</td>
                    <td>Spatial Awareness, Critical Thinking, Basic Mathematics, Data Interpretation</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Wave Theory</strong></td>
                    <td>Wave Theory is a scientific framework that describes the behavior of waves, including their properties, interactions, and propagation in various media. It encompasses concepts from physics that explain phenomena such as sound, light, and water waves.</td>
                    <td>Basic Mathematics, Physics Fundamentals, Trigonometry, Graphing Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Waveform Analysis</strong></td>
                    <td>Waveform Analysis involves the examination and interpretation of waveforms to extract meaningful information about signals in various fields such as engineering, physics, and audio processing. It encompasses techniques for analyzing frequency, amplitude, and phase characteristics of signals.</td>
                    <td>Signal Processing, Fourier Transform, Basic Mathematics, Graph Interpretation</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Work-Energy Principle</strong></td>
                    <td>The Work-Energy Principle states that the work done on an object is equal to the change in its kinetic energy. This principle is fundamental in understanding the relationship between force, motion, and energy in physics.</td>
                    <td>Newton's Laws, Kinematics, Basic Mathematics, Energy Concepts</td>
                </tr>
                
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function toggleExpand() {
            const container = document.getElementById('graphContainer');
            const overlay = document.getElementById('overlay');
            const expandBtn = document.getElementById('expandBtn');

            if (container.classList.contains('expanded')) {
                container.classList.remove('expanded');
                overlay.classList.remove('active');
                expandBtn.innerHTML = '🔍 Expand';
                document.body.style.overflow = 'auto';

                // Force resize of all network elements after collapse
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            } else {
                container.classList.add('expanded');
                overlay.classList.add('active');
                expandBtn.innerHTML = '❌ Close';
                document.body.style.overflow = 'hidden';

                // Force resize of all network elements after expansion
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            }
        }

        function resizeNetworkElements() {
            // Force resize of all pyvis network elements
            const networkElements = [
                document.getElementById('mynetworkid'),
                document.querySelector('.card-body'),
                document.querySelector('#vis-network'),
                document.querySelector('canvas')
            ];

            networkElements.forEach(element => {
                if (element) {
                    // Trigger a reflow by temporarily changing display
                    const originalDisplay = element.style.display;
                    element.style.display = 'none';
                    element.offsetHeight; // Trigger reflow
                    element.style.display = originalDisplay;
                }
            });

            // Redraw and fit the network
            if (window.network) {
                if (window.network.redraw) {
                    window.network.redraw();
                }
                if (window.network.fit) {
                    window.network.fit();
                }
                // Force canvas resize if available
                if (window.network.canvas && window.network.canvas.setSize) {
                    const container = document.querySelector('.graph-content');
                    if (container) {
                        window.network.canvas.setSize(container.offsetWidth, container.offsetHeight);
                    }
                }
            }
        }

        function resetGraph() {
            // Reset the network view and ensure proper sizing
            resizeNetworkElements();
        }

        // Close expanded view with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const container = document.getElementById('graphContainer');
                if (container.classList.contains('expanded')) {
                    toggleExpand();
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            setTimeout(() => {
                resizeNetworkElements();
            }, 100);
        });

        // Initial resize after page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                resizeNetworkElements();
            }, 500);
        });
    </script>

    <script src="lib/bindings/utils.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>
<script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>
<script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "black"}, "id": "Battery Electric Vehicles", "label": "Battery Electric Vehicles", "shape": "dot", "size": 45, "title": "Depth: 0\nDescription: Battery Electric Vehicles (BEVs) are automobiles that are powered entirely by electric energy stored in batteries, utilizing electric motors for propulsion. They are a key component in the transition to sustainable transportation and reducing greenhouse gas emissions."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Battery Chemistry", "label": "Battery Chemistry", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Battery chemistry involves the study of the chemical processes and materials that enable the storage and release of electrical energy in batteries. It encompasses various types of batteries, including lithium-ion, lead-acid, and nickel-metal hydride, focusing on their electrochemical reactions and performance characteristics."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Electric Circuits", "label": "Electric Circuits", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Electric circuits are pathways through which electric current flows, consisting of various components such as resistors, capacitors, and power sources. Understanding electric circuits is essential for analyzing and designing electrical systems."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Energy Storage", "label": "Energy Storage", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Energy storage refers to the methods and technologies used to store energy for later use, enabling a balance between energy supply and demand. It plays a crucial role in renewable energy systems, grid stability, and enhancing energy efficiency."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Motor Technology", "label": "Motor Technology", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Motor Technology encompasses the study and application of various types of motors, including electric, hydraulic, and pneumatic systems, focusing on their design, operation, and control mechanisms."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic Chemistry", "label": "Basic Chemistry", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic Chemistry introduces fundamental concepts of matter, its properties, and the interactions between different substances. It serves as a foundation for understanding chemical reactions, atomic structure, and the periodic table."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic Mathematics", "label": "Basic Mathematics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic Mathematics encompasses fundamental concepts such as arithmetic, number theory, and basic algebra, forming the foundation for more advanced mathematical studies. It is essential for everyday problem-solving and various academic disciplines."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic Physics", "label": "Basic Physics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic Physics is the study of fundamental principles governing the behavior of matter and energy, focusing on concepts such as motion, forces, energy, and waves. It serves as a foundation for understanding more complex physical phenomena and applications."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Circuit Components", "label": "Circuit Components", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Circuit components are the fundamental elements used in electrical circuits, including resistors, capacitors, inductors, and diodes, which interact to perform specific functions. Understanding these components is essential for designing and analyzing electronic circuits."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Control Systems", "label": "Control Systems", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Control Systems is a branch of engineering that deals with the behavior of dynamic systems and the design of controllers to regulate their behavior. It encompasses both theoretical and practical aspects, including feedback mechanisms and stability analysis."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Electrical Circuits", "label": "Electrical Circuits", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Electrical circuits are pathways that allow electric current to flow, consisting of components such as resistors, capacitors, and inductors. Understanding these circuits is essential for analyzing and designing electrical systems."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Electrical Fundamentals", "label": "Electrical Fundamentals", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Electrical Fundamentals covers the basic principles of electricity, including concepts such as voltage, current, resistance, and power. It serves as the foundation for understanding more complex electrical systems and circuits."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Electrochemistry", "label": "Electrochemistry", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Electrochemistry is the branch of chemistry that deals with the relationship between electrical energy and chemical reactions, particularly the processes of oxidation and reduction. It encompasses the study of electrochemical cells, reactions, and their applications in various fields such as batteries and corrosion."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Energy Conversion", "label": "Energy Conversion", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Energy conversion refers to the process of changing energy from one form to another, such as converting kinetic energy to electrical energy or thermal energy to mechanical energy. This topic is fundamental in understanding various applications in physics, engineering, and environmental science."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Material Science", "label": "Material Science", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Material Science is the study of the properties, performance, and applications of materials, integrating principles from physics, chemistry, and engineering to innovate and improve material design and functionality."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Mechanical Principles", "label": "Mechanical Principles", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Mechanical Principles encompass the fundamental concepts and laws governing the behavior of physical systems in motion, including forces, energy, and mechanics. This topic is essential for understanding how machines and structures operate in engineering and physics."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Ohm\u0027s Law", "label": "Ohm\u0027s Law", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Ohm\u0027s Law is a fundamental principle in electronics that states the relationship between voltage, current, and resistance in an electrical circuit, expressed as V = IR. It is essential for understanding how electrical circuits operate and how to calculate the values of voltage, current, and resistance."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Thermodynamics", "label": "Thermodynamics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Thermodynamics is the branch of physics that deals with the relationships between heat, work, temperature, and energy. It provides fundamental principles that govern the behavior of physical systems in terms of energy transfer and transformation."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Voltage and Current", "label": "Voltage and Current", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Voltage and current are fundamental electrical concepts where voltage represents the electric potential difference between two points, and current is the flow of electric charge through a conductor. Understanding these concepts is essential for analyzing and designing electrical circuits."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Algebra", "label": "Algebra", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Algebra is a branch of mathematics dealing with symbols and the rules for manipulating those symbols to solve equations and understand relationships between quantities. It serves as a foundational tool for higher mathematics and various applications in science and engineering."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Arithmetic Operations", "label": "Arithmetic Operations", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Arithmetic operations are fundamental mathematical processes that include addition, subtraction, multiplication, and division, used to manipulate numerical values. Mastery of these operations is essential for solving mathematical problems and performing calculations in various fields."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Atomic Structure", "label": "Atomic Structure", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Atomic structure refers to the arrangement of protons, neutrons, and electrons within an atom, which determines its chemical properties and behavior. Understanding atomic structure is fundamental to the study of chemistry and physics."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic Electricity", "label": "Basic Electricity", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic Electricity covers fundamental concepts of electrical principles, including voltage, current, resistance, and circuit theory, essential for understanding how electrical systems operate."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic Geometry", "label": "Basic Geometry", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic Geometry involves the study of shapes, sizes, and the properties of space. It serves as a foundation for understanding more complex geometric concepts and applications."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Calculus", "label": "Calculus", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Calculus is a branch of mathematics that studies continuous change, focusing on concepts such as derivatives and integrals. It provides tools for modeling and analyzing dynamic systems in various fields, including physics, engineering, and economics."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Chemical Kinetics", "label": "Chemical Kinetics", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Chemical kinetics is the study of the rates of chemical reactions and the factors that influence these rates. It involves understanding how different conditions affect the speed of reactions and the mechanisms by which they occur."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Circuit Theory", "label": "Circuit Theory", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Circuit Theory is the study of electrical circuits, focusing on the behavior of voltage, current, and resistance in various configurations. It provides the foundational principles for analyzing and designing electrical systems."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Crystal Structures", "label": "Crystal Structures", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Crystal structures refer to the ordered arrangement of atoms, ions, or molecules in a crystalline material, which determines its physical properties and behavior. Understanding these structures is essential for fields such as materials science, chemistry, and solid-state physics."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Differential Equations", "label": "Differential Equations", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Differential equations are mathematical equations that relate a function to its derivatives, used to model various phenomena in physics, engineering, and other fields. They are essential for understanding dynamic systems and change over time."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Electric Charge", "label": "Electric Charge", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Electric charge is a fundamental property of matter that causes it to experience a force when placed in an electromagnetic field. It exists in two types, positive and negative, and is quantized in discrete amounts."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Energy Types", "label": "Energy Types", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Energy types refer to the various forms of energy, including kinetic, potential, thermal, chemical, and more, each with distinct characteristics and applications. Understanding these types is essential for studying energy transfer and conservation in physical systems."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "General Chemistry", "label": "General Chemistry", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: General Chemistry is an introductory course that covers the fundamental principles of chemistry, including atomic structure, chemical bonding, stoichiometry, and the behavior of gases, liquids, and solids. It serves as a foundation for more advanced studies in chemistry and related fields."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Geometry", "label": "Geometry", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Geometry is a branch of mathematics that deals with the properties and relationships of points, lines, surfaces, and solids. It involves the study of shapes, sizes, and the relative position of figures in space."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Kinematics", "label": "Kinematics", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Kinematics is the branch of mechanics that deals with the motion of objects without considering the forces that cause this motion. It involves the study of concepts such as displacement, velocity, and acceleration."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Linear Algebra", "label": "Linear Algebra", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Linear Algebra is a branch of mathematics that deals with vectors, vector spaces, and linear transformations, focusing on the study of systems of linear equations and their properties. It is foundational for various fields including engineering, physics, computer science, and economics."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Materials Properties", "label": "Materials Properties", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Materials properties refer to the characteristics and behaviors of materials under various conditions, including mechanical, thermal, electrical, and chemical properties. Understanding these properties is essential for selecting materials for specific applications in engineering and design."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Mathematics Fundamentals", "label": "Mathematics Fundamentals", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Mathematics Fundamentals encompasses the basic concepts and principles of mathematics, including arithmetic, algebra, geometry, and number theory. It serves as the foundation for more advanced mathematical studies and applications."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Measurement Units", "label": "Measurement Units", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Measurement units are standardized quantities used to express and compare physical properties, such as length, mass, and volume. Understanding measurement units is essential for accurate data interpretation and scientific communication."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Number Recognition", "label": "Number Recognition", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Number recognition is the ability to identify and understand numerical symbols and their corresponding values. It is a foundational skill in mathematics that supports further learning in arithmetic and number theory."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Physics Basics", "label": "Physics Basics", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Physics Basics covers fundamental concepts and principles that govern the behavior of matter and energy in the universe. It serves as an introduction to key topics such as motion, forces, energy, and waves."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Physics Fundamentals", "label": "Physics Fundamentals", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Physics Fundamentals covers the basic principles and concepts of physics, including motion, forces, energy, and the laws governing physical phenomena. It serves as a foundation for further study in various fields of science and engineering."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Scientific Method", "label": "Scientific Method", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: The scientific method is a systematic process used for investigating phenomena, acquiring new knowledge, or correcting and integrating previous knowledge through observation, experimentation, and analysis."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Scientific Notation", "label": "Scientific Notation", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Scientific notation is a method of expressing numbers that are too large or too small to be conveniently written in decimal form, using powers of ten. It simplifies calculations and comparisons by representing numbers in the format a \u00d7 10^n, where 1 \u2264 a \u003c 10 and n is an integer."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Signal Processing", "label": "Signal Processing", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Signal Processing involves the analysis, manipulation, and interpretation of signals to extract useful information or improve signal quality. It is widely used in various fields such as telecommunications, audio processing, and image analysis."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Soldering Skills", "label": "Soldering Skills", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Soldering skills involve the techniques and practices used to join electronic components together using solder, a fusible metal alloy. Mastery of these skills is essential for electronics assembly, repair, and prototyping."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Solid Mechanics", "label": "Solid Mechanics", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Solid Mechanics is the branch of mechanics that deals with the behavior of solid materials under external forces, including stress, strain, and deformation. It is essential for understanding material properties and structural analysis in engineering applications."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Static Equilibrium", "label": "Static Equilibrium", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Static equilibrium refers to the state of an object at rest where the sum of all forces and the sum of all moments acting on it are zero, resulting in no net movement. This concept is fundamental in mechanics and is essential for analyzing structures and systems in engineering."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Statistical Mechanics", "label": "Statistical Mechanics", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Statistical Mechanics is a branch of physics that applies statistical methods to study the behavior of systems composed of a large number of particles, linking microscopic properties to macroscopic observables. It provides a framework for understanding thermodynamic phenomena through the statistical distribution of states."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Systems Theory", "label": "Systems Theory", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Systems Theory is an interdisciplinary study that examines complex systems in various fields, focusing on the relationships and interactions between components within a whole. It emphasizes understanding how systems function, adapt, and evolve over time."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Vector Analysis", "label": "Vector Analysis", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Vector Analysis is a branch of mathematics that deals with vector fields and operations on vectors, including differentiation and integration in multiple dimensions. It is essential for understanding physical phenomena in fields such as physics and engineering."}, {"color": "#feca57", "font": {"color": "black"}, "id": "AC/DC Fundamentals", "label": "AC/DC Fundamentals", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: AC/DC Fundamentals covers the principles of alternating current (AC) and direct current (DC) electrical systems, including their characteristics, applications, and differences. This topic is essential for understanding electrical engineering and circuit design."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Algebra Basics", "label": "Algebra Basics", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Algebra Basics introduces fundamental concepts of algebra, including variables, expressions, and equations, which are essential for solving mathematical problems. It serves as a foundation for more advanced topics in mathematics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Algebraic Expressions", "label": "Algebraic Expressions", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Algebraic expressions are mathematical phrases that include numbers, variables, and operators, used to represent relationships and calculations. They form the foundation for more complex algebraic concepts and problem-solving techniques."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Atomic Theory", "label": "Atomic Theory", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Atomic Theory is a scientific framework that describes the nature of matter, stating that all matter is composed of atoms, which are the fundamental building blocks of chemical substances. It encompasses the structure, behavior, and interactions of atoms as well as their role in chemical reactions."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Basic Electronics", "label": "Basic Electronics", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Basic Electronics covers fundamental concepts of electrical circuits, components, and principles, including voltage, current, resistance, and the behavior of passive and active components. It serves as a foundation for understanding more complex electronic systems and devices."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Chemical Bonding", "label": "Chemical Bonding", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Chemical bonding refers to the attractive forces that hold atoms together in molecules and compounds, primarily through ionic, covalent, and metallic bonds. Understanding these interactions is essential for explaining the properties and behaviors of substances in chemistry."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Chemical Reactions", "label": "Chemical Reactions", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Chemical reactions are processes in which substances (reactants) undergo a transformation to form new substances (products) through the breaking and forming of chemical bonds. Understanding these reactions is fundamental to the study of chemistry and various scientific applications."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Classical Mechanics", "label": "Classical Mechanics", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Classical Mechanics is a branch of physics that deals with the motion of objects and the forces acting on them, providing the foundational principles that govern the behavior of macroscopic systems. It encompasses concepts such as Newton\u0027s laws of motion, energy, and momentum."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Complex Numbers", "label": "Complex Numbers", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Complex numbers are numbers that comprise a real part and an imaginary part, typically expressed in the form a + bi, where \u0027a\u0027 is the real part and \u0027b\u0027 is the imaginary part. They are used in various fields such as engineering, physics, and applied mathematics to solve equations that have no real solutions."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Counting Skills", "label": "Counting Skills", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Counting skills involve the ability to recognize, understand, and manipulate numbers in a sequential manner, forming the foundation for more advanced mathematical concepts. Mastery of counting is essential for developing numeracy and problem-solving abilities in various contexts."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Critical Thinking", "label": "Critical Thinking", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Critical thinking is the ability to analyze information, evaluate evidence, and form reasoned conclusions. It involves questioning assumptions and considering alternative viewpoints to enhance decision-making and problem-solving skills."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Data Analysis", "label": "Data Analysis", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Data Analysis involves inspecting, cleansing, transforming, and modeling data to discover useful information, inform conclusions, and support decision-making. It encompasses various techniques and tools to interpret data effectively."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Decimal System", "label": "Decimal System", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The decimal system is a base-10 numeral system that uses ten digits (0-9) to represent numbers. It is the most widely used number system in the world, facilitating arithmetic operations and numerical representation."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Electromagnetic Theory", "label": "Electromagnetic Theory", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Electromagnetic Theory is the branch of physics that studies the interactions between electric charges and magnetic fields, encompassing the principles of electricity, magnetism, and light. It provides a framework for understanding how electric and magnetic fields propagate and interact with matter."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Electron Configuration", "label": "Electron Configuration", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Electron configuration refers to the distribution of electrons in an atom\u0027s orbitals, which determines its chemical properties and reactivity. It is represented using a notation that indicates the energy levels and sublevels occupied by electrons."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Energy Conservation", "label": "Energy Conservation", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Energy conservation refers to the practice of reducing energy consumption through using less energy service or using energy more efficiently. It plays a crucial role in sustainability and environmental protection by minimizing waste and lowering carbon footprints."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Equation Solving", "label": "Equation Solving", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Equation solving is the process of finding the values of variables that satisfy a given mathematical equation. It involves applying various techniques and methods to manipulate and simplify equations to isolate the variable of interest."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Exponents", "label": "Exponents", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Exponents represent the number of times a base is multiplied by itself, providing a concise way to express large numbers and perform calculations involving powers. Understanding exponents is essential for advanced topics in algebra and mathematics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Feedback Loops", "label": "Feedback Loops", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Feedback loops are processes in which the output of a system is circled back and used as input, influencing future outputs. They are essential in understanding dynamic systems in various fields, including biology, engineering, and social sciences."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Force Analysis", "label": "Force Analysis", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Force Analysis is the study of forces acting on objects, focusing on their magnitudes, directions, and effects on motion. It is a fundamental concept in physics and engineering that helps in understanding static and dynamic systems."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Functions", "label": "Functions", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Functions are mathematical entities that relate an input to a single output, often represented as f(x), and are fundamental in both mathematics and programming for structuring code and solving problems."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Geometric Shapes", "label": "Geometric Shapes", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Geometric shapes are fundamental figures in mathematics defined by their boundaries and dimensions, including points, lines, angles, and various polygons. Understanding these shapes is essential for exploring more complex mathematical concepts and applications in geometry."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Graph Interpretation", "label": "Graph Interpretation", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Graph interpretation involves analyzing and extracting meaningful information from graphical representations of data, such as charts and plots. It is essential for making informed decisions based on visual data insights."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Graphing", "label": "Graphing", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Graphing is the visual representation of data or mathematical functions on a coordinate system, allowing for the analysis of relationships and trends. It is a fundamental skill in mathematics and data analysis that aids in interpreting quantitative information."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Hand Tool Use", "label": "Hand Tool Use", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Hand tool use involves the practical application and manipulation of tools designed for manual operation, emphasizing skills in precision, safety, and technique. Mastery of hand tools is essential for various trades and crafts, including woodworking, metalworking, and construction."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Limits", "label": "Limits", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Limits are a fundamental concept in calculus that describe the behavior of a function as its input approaches a certain value. They are essential for understanding continuity, derivatives, and integrals."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Logical Reasoning", "label": "Logical Reasoning", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Logical reasoning is the process of using structured thinking to analyze arguments, identify valid conclusions, and solve problems based on given premises. It is essential for critical thinking and decision-making in various academic and real-world contexts."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Mathematics", "label": "Mathematics", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Mathematics is the study of numbers, shapes, and patterns, encompassing various branches such as arithmetic, algebra, geometry, and calculus. It serves as a foundational discipline that supports numerous fields including science, engineering, and economics."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Matrix Operations", "label": "Matrix Operations", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Matrix operations involve mathematical procedures such as addition, subtraction, and multiplication of matrices, which are rectangular arrays of numbers. Understanding these operations is essential for various applications in linear algebra, computer science, and engineering."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Measurement Concepts", "label": "Measurement Concepts", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Measurement concepts encompass the principles and methods used to quantify physical properties, enabling the comparison and analysis of data across various fields. This topic includes understanding units, scales, and the significance of accuracy and precision in measurements."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Measurement Skills", "label": "Measurement Skills", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Measurement skills involve the ability to accurately assess and quantify physical attributes such as length, weight, volume, and time using appropriate tools and techniques. These skills are essential for various fields, including science, engineering, and everyday life."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Mechanical Energy", "label": "Mechanical Energy", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Mechanical energy is the sum of potential energy and kinetic energy in a system, representing the energy associated with the motion and position of an object. It is a key concept in physics that helps explain how energy is conserved and transformed in mechanical systems."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Modeling Techniques", "label": "Modeling Techniques", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Modeling techniques involve the use of mathematical and computational methods to represent real-world systems and processes, enabling analysis and prediction. These techniques are essential in fields such as engineering, computer science, and data science."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Multivariable Functions", "label": "Multivariable Functions", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Multivariable functions are mathematical functions that take two or more variables as inputs and produce a single output, often represented as f(x, y, z). They are essential in fields such as calculus, physics, and engineering for modeling complex systems."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Newton\u0027s Laws", "label": "Newton\u0027s Laws", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Newton\u0027s Laws are three fundamental principles that describe the relationship between the motion of an object and the forces acting on it. They form the foundation of classical mechanics and are essential for understanding how objects behave in various physical situations."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Number Properties", "label": "Number Properties", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Number properties refer to the fundamental characteristics and rules that govern numbers, including concepts such as commutativity, associativity, distributivity, and the properties of integers, rational numbers, and real numbers."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Observation Skills", "label": "Observation Skills", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Observation skills involve the ability to notice and interpret details in various environments, enhancing understanding and learning. These skills are essential for effective communication, critical thinking, and problem-solving."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Periodic Table", "label": "Periodic Table", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: The Periodic Table is a systematic arrangement of chemical elements, organized by increasing atomic number, which highlights the relationships and properties of the elements. It serves as a fundamental tool in chemistry for understanding element behavior and interactions."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Probability Theory", "label": "Probability Theory", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Probability Theory is the branch of mathematics that deals with the analysis of random phenomena and the quantification of uncertainty. It provides the foundational framework for statistical inference and decision-making under uncertainty."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Programming Basics", "label": "Programming Basics", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Programming Basics introduces fundamental concepts of coding, including syntax, data types, and control structures, enabling learners to write simple programs. It serves as the foundation for more advanced programming topics and languages."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Safety Practices", "label": "Safety Practices", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Safety practices encompass the guidelines and procedures designed to minimize risks and ensure the well-being of individuals in various environments, including workplaces, homes, and public spaces."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Spatial Reasoning", "label": "Spatial Reasoning", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Spatial reasoning is the ability to visualize and manipulate objects in a three-dimensional space, crucial for problem-solving in fields such as mathematics, engineering, and architecture."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Stoichiometry", "label": "Stoichiometry", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Stoichiometry is the branch of chemistry that deals with the calculation of reactants and products in chemical reactions based on the conservation of mass. It involves using balanced chemical equations to determine the quantitative relationships between substances involved in reactions."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Symbol Recognition", "label": "Symbol Recognition", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Symbol recognition is the process of identifying and interpreting symbols, such as letters, numbers, or graphical representations, in various contexts. It plays a crucial role in fields like computer vision, natural language processing, and cognitive psychology."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Symmetry Operations", "label": "Symmetry Operations", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Symmetry operations are mathematical transformations that leave an object invariant under certain conditions, commonly used in fields such as chemistry and physics to analyze molecular structures and crystal lattices. These operations include translations, rotations, reflections, and inversions, which help in understanding the symmetry properties of objects."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Systems Thinking", "label": "Systems Thinking", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Systems Thinking is an approach to understanding complex systems by examining the interrelationships and interactions among their components, rather than viewing them in isolation. It emphasizes holistic analysis and the dynamic nature of systems over time."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Trigonometry", "label": "Trigonometry", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Trigonometry is a branch of mathematics that studies the relationships between the angles and sides of triangles, particularly right triangles. It is essential for understanding concepts in geometry, physics, engineering, and various applied sciences."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Understanding of Symbols", "label": "Understanding of Symbols", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Understanding of symbols involves recognizing and interpreting various signs, characters, and representations used in communication, mathematics, and logic. This foundational skill is essential for effective reasoning and problem-solving across multiple disciplines."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Unit Cells", "label": "Unit Cells", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Unit cells are the smallest repeating units in a crystal lattice that define the structure and symmetry of a crystal. They are fundamental in understanding crystallography and material properties."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Unit Conversion", "label": "Unit Conversion", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Unit conversion is the process of converting a quantity expressed in one set of units to another, ensuring accurate measurement and communication in various fields such as science, engineering, and everyday life."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Vector Addition", "label": "Vector Addition", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Vector addition is the process of combining two or more vectors to produce a resultant vector, taking into account both their magnitudes and directions. It is a fundamental operation in physics and engineering, essential for analyzing forces, velocities, and other vector quantities."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Algebraic Functions", "label": "Algebraic Functions", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Algebraic functions are mathematical expressions that involve variables and constants combined using algebraic operations such as addition, subtraction, multiplication, division, and exponentiation. They can be represented in various forms, including polynomial, rational, and radical functions."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Analytical Skills", "label": "Analytical Skills", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Analytical skills refer to the ability to collect, process, and interpret data to make informed decisions or solve problems. These skills involve critical thinking, logical reasoning, and the ability to evaluate information effectively."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Argument Analysis", "label": "Argument Analysis", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Argument Analysis involves evaluating the structure and validity of arguments, identifying premises and conclusions, and assessing the strength of reasoning. It is a critical skill in logic, philosophy, and effective communication."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Argument Evaluation", "label": "Argument Evaluation", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Argument Evaluation involves assessing the validity and soundness of arguments by analyzing their structure, premises, and conclusions. It is a critical skill in logic, debate, and critical thinking."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Attention to Detail", "label": "Attention to Detail", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Attention to Detail refers to the ability to notice and accurately process small elements within a larger context, which is crucial for tasks requiring precision and thoroughness. This skill enhances overall quality and effectiveness in various academic and professional settings."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Balancing Equations", "label": "Balancing Equations", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Balancing equations is the process of ensuring that the number of atoms of each element is the same on both sides of a chemical equation, reflecting the law of conservation of mass. This skill is essential for understanding chemical reactions and stoichiometry."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Balancing Reactions", "label": "Balancing Reactions", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Balancing reactions involves ensuring that the number of atoms for each element is the same on both sides of a chemical equation, adhering to the law of conservation of mass. This process is essential for accurately representing chemical reactions in stoichiometry."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Basic Algebra", "label": "Basic Algebra", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Basic Algebra involves the study of mathematical symbols and the rules for manipulating these symbols to solve equations and inequalities. It serves as a foundational skill for higher-level mathematics and various real-world applications."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Basic Literacy", "label": "Basic Literacy", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Basic literacy refers to the ability to read and write at a fundamental level, enabling individuals to understand and communicate effectively through written language. It serves as a foundational skill necessary for further education and daily functioning in society."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Basic Measurements", "label": "Basic Measurements", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Basic measurements involve understanding and applying fundamental concepts of quantifying physical properties such as length, mass, and volume using standard units. This topic serves as a foundation for more advanced studies in science, engineering, and mathematics."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Basic Statistics", "label": "Basic Statistics", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Basic Statistics involves the collection, analysis, interpretation, presentation, and organization of data. It provides foundational tools for understanding data distributions, central tendencies, and variability."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Chemical Bonds", "label": "Chemical Bonds", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Chemical bonds are the forces that hold atoms together in molecules and compounds, primarily through ionic, covalent, and metallic interactions. Understanding these bonds is crucial for studying chemical reactions and material properties."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Chemical Equations", "label": "Chemical Equations", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Chemical equations are symbolic representations of chemical reactions, showing the reactants and products along with their respective quantities. They are essential for understanding the stoichiometry and conservation of mass in chemical processes."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Cognitive Psychology", "label": "Cognitive Psychology", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Cognitive Psychology is the scientific study of mental processes such as perception, memory, reasoning, and decision-making. It explores how people understand, think, and remember information."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Communication Skills", "label": "Communication Skills", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Communication skills encompass the ability to convey information effectively and efficiently through verbal, non-verbal, and written means. Mastery of these skills is essential for personal and professional interactions."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Coordinate Geometry", "label": "Coordinate Geometry", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Coordinate Geometry, also known as analytic geometry, is the study of geometric figures using a coordinate system, allowing for the representation of shapes and the calculation of distances and angles. It combines algebra and geometry to analyze and solve geometric problems in a numerical format."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Coordinate Systems", "label": "Coordinate Systems", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Coordinate systems are frameworks that use numerical values to define the position of points in space, typically represented in two or three dimensions. They are essential for various fields such as mathematics, physics, and computer graphics."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Crystallography", "label": "Crystallography", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Crystallography is the study of crystal structures and their properties, focusing on the arrangement of atoms within crystalline materials. It plays a crucial role in fields such as chemistry, physics, and materials science."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Data Interpretation", "label": "Data Interpretation", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Data Interpretation involves analyzing and making sense of data presented in various formats, such as graphs, tables, and charts, to draw meaningful conclusions and insights. It is a critical skill in fields like statistics, research, and business analytics."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Data Visualization", "label": "Data Visualization", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Data Visualization is the graphical representation of information and data, using visual elements like charts, graphs, and maps to make complex data more accessible and understandable. It helps in identifying patterns, trends, and insights from data sets."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Dimensional Analysis", "label": "Dimensional Analysis", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Dimensional Analysis is a mathematical technique used to convert between different units of measurement by analyzing the dimensions of physical quantities. It helps ensure that equations are dimensionally consistent and can be used to derive relationships between variables."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Dynamic Systems", "label": "Dynamic Systems", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Dynamic Systems is the study of systems that evolve over time according to specific rules, often described by differential equations. It encompasses the analysis of stability, control, and behavior of these systems in various fields such as engineering, physics, and biology."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Dynamics", "label": "Dynamics", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Dynamics is the branch of mechanics that deals with the motion of objects and the forces that affect this motion. It encompasses the study of how forces influence the behavior of physical systems over time."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Electronegativity", "label": "Electronegativity", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Electronegativity is a measure of an atom\u0027s ability to attract and hold onto electrons in a chemical bond. It plays a crucial role in determining the nature of chemical bonds and molecular interactions."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Element Properties", "label": "Element Properties", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Element properties refer to the characteristics and behaviors of chemical elements, including their atomic structure, reactivity, and physical properties such as melting and boiling points. Understanding these properties is essential for studying chemical reactions and material science."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Emergency Procedures", "label": "Emergency Procedures", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Emergency Procedures encompass the protocols and actions that individuals and organizations must follow during emergencies to ensure safety and minimize harm. This includes preparation, response, and recovery strategies tailored to various emergency scenarios."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Environmental Science", "label": "Environmental Science", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Environmental Science is the study of interactions between the physical, chemical, and biological components of the environment, focusing on issues such as pollution, resource management, and sustainability. It integrates knowledge from various disciplines to address environmental challenges and promote ecological health."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "First Aid Basics", "label": "First Aid Basics", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: First Aid Basics covers essential techniques and procedures for providing immediate care to individuals experiencing medical emergencies. It includes skills such as CPR, wound care, and recognizing signs of serious conditions."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Force Concepts", "label": "Force Concepts", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Force concepts encompass the fundamental principles of force, including its definition, types, and effects on motion and equilibrium. Understanding these concepts is essential for analyzing physical systems in mechanics."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Graph Theory", "label": "Graph Theory", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Graph Theory is a branch of mathematics that studies the properties and relationships of graphs, which are structures made up of vertices (nodes) connected by edges (lines). It has applications in computer science, biology, social sciences, and more."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Graphing Skills", "label": "Graphing Skills", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Graphing skills involve the ability to create, interpret, and analyze visual representations of data, such as charts and graphs. These skills are essential for effectively communicating quantitative information in various academic and professional contexts."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Group Theory", "label": "Group Theory", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Group Theory is a branch of mathematics that studies algebraic structures known as groups, which consist of a set equipped with an operation that satisfies certain axioms. It is fundamental in various fields such as abstract algebra, geometry, and physics."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Hand-Eye Coordination", "label": "Hand-Eye Coordination", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Hand-eye coordination is the ability to synchronize visual input with hand movements, enabling precise actions such as writing, typing, or playing sports. It is crucial for various daily activities and skill development."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Health Regulations", "label": "Health Regulations", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Health regulations are laws and guidelines established to protect public health by ensuring the safety and efficacy of health services, products, and environments. They encompass a wide range of areas including food safety, pharmaceuticals, and environmental health standards."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Image Processing", "label": "Image Processing", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Image Processing involves the manipulation and analysis of digital images using algorithms to enhance, transform, or extract information from them. It is widely used in various fields such as computer vision, medical imaging, and remote sensing."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Imaginary Numbers", "label": "Imaginary Numbers", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Imaginary numbers are a class of numbers that extend the real number system, defined as multiples of the imaginary unit \u0027i\u0027, where i is the square root of -1. They are used in various fields, including engineering and physics, to solve equations that do not have real solutions."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Inequalities", "label": "Inequalities", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Inequalities are mathematical expressions that describe the relationship between two values, indicating that one is greater than, less than, or not equal to the other. They are fundamental in algebra and are used to solve problems involving ranges and limits."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Introduction to Geometry", "label": "Introduction to Geometry", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Introduction to Geometry covers the fundamental concepts of shapes, sizes, relative positions of figures, and the properties of space. It serves as a foundation for more advanced studies in mathematics and related fields."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Limits and Continuity", "label": "Limits and Continuity", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Limits and Continuity are fundamental concepts in calculus that describe the behavior of functions as they approach specific points or values, helping to determine whether a function is continuous at those points."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Linear Equations", "label": "Linear Equations", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Linear equations are mathematical statements that express the relationship between two variables using a straight line on a graph, typically in the form y = mx + b. They are foundational in algebra and are used to model real-world situations involving constant rates of change."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Logical Operators", "label": "Logical Operators", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Logical operators are symbols or words used to connect two or more expressions in logic, enabling the formation of complex logical statements. Common logical operators include AND, OR, and NOT, which are fundamental in programming and mathematical logic."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Logical Thinking", "label": "Logical Thinking", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Logical thinking is the ability to reason systematically and make connections between concepts, enabling individuals to solve problems and make informed decisions. It involves analyzing information, identifying patterns, and drawing conclusions based on evidence."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Machine Learning", "label": "Machine Learning", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Machine Learning is a subset of artificial intelligence that focuses on the development of algorithms that allow computers to learn from and make predictions based on data. It involves the use of statistical techniques to enable machines to improve their performance on tasks through experience."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Material Properties", "label": "Material Properties", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Material properties refer to the characteristics of materials that define their behavior under various conditions, including mechanical, thermal, electrical, and chemical properties. Understanding these properties is essential for selecting appropriate materials for engineering and design applications."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Mathematical Symbols", "label": "Mathematical Symbols", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Mathematical symbols are standardized notations used to represent numbers, operations, relations, and functions in mathematics. They provide a concise way to express mathematical ideas and facilitate communication among mathematicians and students."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Mathematical Transformations", "label": "Mathematical Transformations", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Mathematical transformations are operations that alter the position, size, or shape of a function or geometric figure, often represented through functions like translation, rotation, reflection, and scaling. These transformations are essential in various fields such as geometry, algebra, and calculus."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Measurement Tools", "label": "Measurement Tools", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Measurement tools are instruments or methods used to quantify physical quantities, such as length, mass, volume, and time, enabling accurate data collection and analysis in various fields. They are essential for scientific experiments, engineering applications, and quality control processes."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Mole Concept", "label": "Mole Concept", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: The mole concept is a fundamental principle in chemistry that relates the amount of substance to its mass and the number of particles, allowing for quantitative analysis in chemical reactions. It provides a bridge between the atomic scale and macroscopic measurements."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Motion Principles", "label": "Motion Principles", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Motion Principles encompass the fundamental concepts and laws that describe the behavior of objects in motion, including the effects of forces, acceleration, and the relationship between distance and time."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Multiplication Skills", "label": "Multiplication Skills", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Multiplication skills involve the ability to efficiently and accurately perform multiplication operations, which are fundamental to various mathematical concepts and real-world applications. Mastery of these skills is essential for advancing in mathematics and problem-solving."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Numerical Skills", "label": "Numerical Skills", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Numerical skills refer to the ability to understand, interpret, and work with numbers effectively. These skills are essential for problem-solving in various academic and real-world contexts."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "One-to-One Correspondence", "label": "One-to-One Correspondence", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: One-to-one correspondence is a mathematical concept that describes a relationship between two sets where each element of one set is paired with exactly one element of the other set, and vice versa. This concept is fundamental in understanding functions, cardinality, and equivalence between sets."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Order of Operations", "label": "Order of Operations", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: The order of operations is a mathematical rule that dictates the sequence in which different operations should be performed to accurately solve expressions. It is commonly remembered by the acronym PEMDAS, which stands for Parentheses, Exponents, Multiplication and Division (from left to right), Addition and Subtraction (from left to right)."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Ordinal Numbers", "label": "Ordinal Numbers", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Ordinal numbers are numbers that represent the position or rank of an item in a sequence, such as first, second, or third. They are used to indicate order rather than quantity."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Pattern Recognition", "label": "Pattern Recognition", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Pattern recognition is the process of identifying and classifying patterns in data, often using algorithms and statistical techniques. It is widely applied in fields such as machine learning, computer vision, and artificial intelligence."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Place Value", "label": "Place Value", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Place value is a numerical system that assigns a value to a digit based on its position within a number, which is essential for understanding the magnitude of numbers in our base-10 system."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Problem Solving", "label": "Problem Solving", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Problem solving is the process of identifying a challenge or opportunity, analyzing it, and developing effective strategies to address it. This skill is essential across various disciplines and is fundamental to critical thinking and decision-making."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Problem-Solving Skills", "label": "Problem-Solving Skills", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Problem-solving skills refer to the ability to identify, analyze, and resolve challenges effectively and efficiently. These skills are essential for critical thinking and decision-making in various contexts."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Quantum Mechanics", "label": "Quantum Mechanics", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Quantum Mechanics is a fundamental branch of physics that deals with the behavior of matter and energy at the smallest scales, such as atoms and subatomic particles. It introduces concepts such as wave-particle duality, quantization, and the uncertainty principle."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Rational Numbers", "label": "Rational Numbers", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Rational numbers are numbers that can be expressed as the quotient or fraction of two integers, where the denominator is not zero. They include integers, fractions, and finite or repeating decimals."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Risk Assessment", "label": "Risk Assessment", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Risk Assessment is the systematic process of identifying, analyzing, and evaluating potential risks that could negatively impact an organization\u0027s operations or objectives. It involves assessing the likelihood and consequences of adverse events to inform decision-making and risk management strategies."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Set Theory", "label": "Set Theory", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Set Theory is a branch of mathematical logic that studies sets, which are collections of objects. It provides the foundational framework for various areas of mathematics, including functions, relations, and cardinality."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Single Variable Calculus", "label": "Single Variable Calculus", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Single Variable Calculus is the branch of mathematics that deals with the differentiation and integration of functions with a single variable. It focuses on understanding rates of change and the accumulation of quantities."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Statistical Analysis", "label": "Statistical Analysis", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Statistical Analysis involves the collection, examination, interpretation, presentation, and organization of data to uncover patterns and insights. It is essential for making informed decisions based on quantitative information."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Statistics", "label": "Statistics", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Statistics is the branch of mathematics that deals with collecting, analyzing, interpreting, presenting, and organizing data. It provides tools for making inferences and decisions based on data sets."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Symmetry Principles", "label": "Symmetry Principles", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Symmetry principles are fundamental concepts in various fields such as physics, mathematics, and art, which describe the invariance of systems under certain transformations. They play a crucial role in understanding conservation laws and the structure of physical theories."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Tool Safety", "label": "Tool Safety", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Tool safety encompasses the practices and precautions necessary to prevent accidents and injuries while using hand and power tools. It includes understanding proper usage, maintenance, and protective measures to ensure a safe working environment."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Understanding of Angles", "label": "Understanding of Angles", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Understanding of angles involves recognizing, measuring, and applying the properties of angles in various contexts, including geometry and real-world applications. This foundational concept is essential for further studies in mathematics and related fields."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Understanding of Integers", "label": "Understanding of Integers", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Understanding of integers involves recognizing and working with whole numbers, both positive and negative, including zero. This foundational concept is essential for more advanced mathematical operations and number theory."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Variables", "label": "Variables", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Variables are symbolic names that represent data values in programming and mathematics, allowing for the storage and manipulation of information. They serve as placeholders for data that can change during program execution or mathematical calculations."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Vector Basics", "label": "Vector Basics", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Vector Basics covers the fundamental concepts of vectors, including their representation, operations such as addition and scalar multiplication, and applications in various fields like physics and computer graphics."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Vector Calculus", "label": "Vector Calculus", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Vector Calculus is a branch of mathematics that deals with vector fields and the differentiation and integration of vector functions. It is essential for understanding physical phenomena in fields such as physics and engineering."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Vector Understanding", "label": "Vector Understanding", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Vector Understanding refers to the comprehension of vectors as mathematical entities that have both magnitude and direction, and their applications in various fields such as physics and engineering."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Visualization Skills", "label": "Visualization Skills", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Visualization skills refer to the ability to create mental images and interpret visual information effectively, which is essential for problem-solving and understanding complex concepts in various fields such as mathematics, science, and design."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Wave Theory", "label": "Wave Theory", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Wave Theory is a scientific framework that describes the behavior of waves, including their properties, interactions, and propagation in various media. It encompasses concepts from physics that explain phenomena such as sound, light, and water waves."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Waveform Analysis", "label": "Waveform Analysis", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Waveform Analysis involves the examination and interpretation of waveforms to extract meaningful information about signals in various fields such as engineering, physics, and audio processing. It encompasses techniques for analyzing frequency, amplitude, and phase characteristics of signals."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Work-Energy Principle", "label": "Work-Energy Principle", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: The Work-Energy Principle states that the work done on an object is equal to the change in its kinetic energy. This principle is fundamental in understanding the relationship between force, motion, and energy in physics."}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "blue", "from": "Battery Electric Vehicles", "to": "Electric Circuits", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Electric Vehicles", "to": "Energy Storage", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Electric Vehicles", "to": "Motor Technology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Electric Vehicles", "to": "Battery Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Chemistry", "to": "Basic Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Chemistry", "to": "Electrochemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Chemistry", "to": "Material Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Chemistry", "to": "Thermodynamics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electric Circuits", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electric Circuits", "to": "Ohm\u0027s Law", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electric Circuits", "to": "Circuit Components", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electric Circuits", "to": "Voltage and Current", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Storage", "to": "Basic Physics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Storage", "to": "Thermodynamics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Storage", "to": "Electrical Circuits", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Storage", "to": "Energy Conversion", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Storage", "to": "Material Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motor Technology", "to": "Electrical Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motor Technology", "to": "Mechanical Principles", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motor Technology", "to": "Control Systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motor Technology", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Chemistry", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Chemistry", "to": "Scientific Notation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Chemistry", "to": "Measurement Units", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Chemistry", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Mathematics", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Mathematics", "to": "Arithmetic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Mathematics", "to": "Basic Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Physics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Physics", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Physics", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Physics", "to": "Scientific Method", "width": 2}, {"arrows": "to", "color": "blue", "from": "Circuit Components", "to": "Basic Electricity", "width": 2}, {"arrows": "to", "color": "blue", "from": "Circuit Components", "to": "Ohm\u0027s Law", "width": 2}, {"arrows": "to", "color": "blue", "from": "Circuit Components", "to": "Circuit Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Circuit Components", "to": "Soldering Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Control Systems", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Control Systems", "to": "Differential Equations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Control Systems", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Control Systems", "to": "Signal Processing", "width": 2}, {"arrows": "to", "color": "blue", "from": "Control Systems", "to": "Systems Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Circuits", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Circuits", "to": "Ohm\u0027s Law", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Circuits", "to": "Circuit Components", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Circuits", "to": "Voltage and Current", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Fundamentals", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Fundamentals", "to": "Physics Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Fundamentals", "to": "Circuit Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Fundamentals", "to": "Ohm\u0027s Law", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrochemistry", "to": "General Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrochemistry", "to": "Basic Physics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrochemistry", "to": "Chemical Kinetics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrochemistry", "to": "Thermodynamics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Conversion", "to": "Basic Physics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Conversion", "to": "Thermodynamics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Conversion", "to": "Energy Types", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Conversion", "to": "Mathematics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Material Science", "to": "Basic Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Material Science", "to": "Solid Mechanics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Material Science", "to": "Thermodynamics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Material Science", "to": "Materials Properties", "width": 2}, {"arrows": "to", "color": "blue", "from": "Material Science", "to": "Crystal Structures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mechanical Principles", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mechanical Principles", "to": "Physics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mechanical Principles", "to": "Vector Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mechanical Principles", "to": "Static Equilibrium", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mechanical Principles", "to": "Kinematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ohm\u0027s Law", "to": "Basic Electricity", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ohm\u0027s Law", "to": "Circuit Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ohm\u0027s Law", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Thermodynamics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Thermodynamics", "to": "Physics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Thermodynamics", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Thermodynamics", "to": "Statistical Mechanics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Voltage and Current", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Voltage and Current", "to": "Ohm\u0027s Law", "width": 2}, {"arrows": "to", "color": "blue", "from": "Voltage and Current", "to": "Circuit Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Voltage and Current", "to": "Electric Charge", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Arithmetic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Number Properties", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra", "to": "Equation Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Arithmetic Operations", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Arithmetic Operations", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Arithmetic Operations", "to": "Understanding of Symbols", "width": 2}, {"arrows": "to", "color": "blue", "from": "Atomic Structure", "to": "Basic Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Atomic Structure", "to": "Atomic Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Atomic Structure", "to": "Periodic Table", "width": 2}, {"arrows": "to", "color": "blue", "from": "Atomic Structure", "to": "Electron Configuration", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Electricity", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Electricity", "to": "Scientific Notation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Electricity", "to": "Ohm\u0027s Law", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Electricity", "to": "Circuit Components", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Geometry", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Geometry", "to": "Algebra Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Geometry", "to": "Spatial Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Trigonometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Calculus", "to": "Limits", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Kinetics", "to": "Chemical Reactions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Kinetics", "to": "Stoichiometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Kinetics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Kinetics", "to": "Thermodynamics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Circuit Theory", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Circuit Theory", "to": "Ohm\u0027s Law", "width": 2}, {"arrows": "to", "color": "blue", "from": "Circuit Theory", "to": "AC/DC Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Circuit Theory", "to": "Complex Numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Circuit Theory", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Crystal Structures", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Crystal Structures", "to": "Chemical Bonding", "width": 2}, {"arrows": "to", "color": "blue", "from": "Crystal Structures", "to": "Unit Cells", "width": 2}, {"arrows": "to", "color": "blue", "from": "Crystal Structures", "to": "Symmetry Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Differential Equations", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Differential Equations", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Differential Equations", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Differential Equations", "to": "Graphing", "width": 2}, {"arrows": "to", "color": "blue", "from": "Differential Equations", "to": "Limits", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electric Charge", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electric Charge", "to": "Newton\u0027s Laws", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electric Charge", "to": "Electromagnetic Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electric Charge", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Types", "to": "Basic Physics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Types", "to": "Energy Conservation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Types", "to": "Thermodynamics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Types", "to": "Mechanical Energy", "width": 2}, {"arrows": "to", "color": "blue", "from": "General Chemistry", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "General Chemistry", "to": "Scientific Notation", "width": 2}, {"arrows": "to", "color": "blue", "from": "General Chemistry", "to": "Measurement Units", "width": 2}, {"arrows": "to", "color": "blue", "from": "General Chemistry", "to": "Atomic Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometry", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometry", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometry", "to": "Logical Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometry", "to": "Measurement Concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Kinematics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Kinematics", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Kinematics", "to": "Graph Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Kinematics", "to": "Physics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Algebra", "to": "Matrix Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Materials Properties", "to": "Basic Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Materials Properties", "to": "Physics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Materials Properties", "to": "Material Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Materials Properties", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics Fundamentals", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics Fundamentals", "to": "Arithmetic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics Fundamentals", "to": "Algebraic Expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics Fundamentals", "to": "Geometric Shapes", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Units", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Units", "to": "Unit Conversion", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Units", "to": "Measurement Concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Recognition", "to": "Counting Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Recognition", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Recognition", "to": "Symbol Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physics Basics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physics Basics", "to": "Scientific Method", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physics Basics", "to": "Measurement Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physics Fundamentals", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physics Fundamentals", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physics Fundamentals", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physics Fundamentals", "to": "Scientific Method", "width": 2}, {"arrows": "to", "color": "blue", "from": "Scientific Method", "to": "Observation Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Scientific Method", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Scientific Method", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Scientific Method", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Scientific Notation", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Scientific Notation", "to": "Exponents", "width": 2}, {"arrows": "to", "color": "blue", "from": "Scientific Notation", "to": "Decimal System", "width": 2}, {"arrows": "to", "color": "blue", "from": "Signal Processing", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Signal Processing", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Signal Processing", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Signal Processing", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Signal Processing", "to": "Programming Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Soldering Skills", "to": "Basic Electronics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Soldering Skills", "to": "Hand Tool Use", "width": 2}, {"arrows": "to", "color": "blue", "from": "Soldering Skills", "to": "Safety Practices", "width": 2}, {"arrows": "to", "color": "blue", "from": "Soldering Skills", "to": "Circuit Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Solid Mechanics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Solid Mechanics", "to": "Physics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Solid Mechanics", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Solid Mechanics", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Static Equilibrium", "to": "Force Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Static Equilibrium", "to": "Newton\u0027s Laws", "width": 2}, {"arrows": "to", "color": "blue", "from": "Static Equilibrium", "to": "Basic Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Static Equilibrium", "to": "Vector Addition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Mechanics", "to": "Thermodynamics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Mechanics", "to": "Classical Mechanics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Mechanics", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Mechanics", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Mechanics", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Systems Theory", "to": "Systems Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Systems Theory", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Systems Theory", "to": "Feedback Loops", "width": 2}, {"arrows": "to", "color": "blue", "from": "Systems Theory", "to": "Modeling Techniques", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Analysis", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Analysis", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Analysis", "to": "Multivariable Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Analysis", "to": "Differential Equations", "width": 2}, {"arrows": "to", "color": "blue", "from": "AC/DC Fundamentals", "to": "Basic Electricity", "width": 2}, {"arrows": "to", "color": "blue", "from": "AC/DC Fundamentals", "to": "Circuit Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "AC/DC Fundamentals", "to": "Ohm\u0027s Law", "width": 2}, {"arrows": "to", "color": "blue", "from": "AC/DC Fundamentals", "to": "Waveform Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra Basics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra Basics", "to": "Arithmetic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebra Basics", "to": "Number Properties", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic Expressions", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic Expressions", "to": "Variables", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic Expressions", "to": "Arithmetic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic Expressions", "to": "Order of Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Atomic Theory", "to": "Basic Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Atomic Theory", "to": "Basic Physics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Atomic Theory", "to": "Scientific Method", "width": 2}, {"arrows": "to", "color": "blue", "from": "Atomic Theory", "to": "Mathematics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Electronics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Electronics", "to": "Physics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Electronics", "to": "Circuit Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Bonding", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Bonding", "to": "Periodic Table", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Bonding", "to": "Chemical Reactions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Bonding", "to": "Electronegativity", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Reactions", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Reactions", "to": "Chemical Bonds", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Reactions", "to": "Stoichiometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Reactions", "to": "Balancing Equations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Classical Mechanics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Classical Mechanics", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Classical Mechanics", "to": "Trigonometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Classical Mechanics", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Classical Mechanics", "to": "Physics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Complex Numbers", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Complex Numbers", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Complex Numbers", "to": "Imaginary Numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Complex Numbers", "to": "Coordinate Systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting Skills", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting Skills", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting Skills", "to": "Ordinal Numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting Skills", "to": "One-to-One Correspondence", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Logical Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Analytical Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Argument Evaluation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Critical Thinking", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Data Visualization", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Analysis", "to": "Programming Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Decimal System", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Decimal System", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Decimal System", "to": "Place Value", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electromagnetic Theory", "to": "Classical Mechanics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electromagnetic Theory", "to": "Vector Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electromagnetic Theory", "to": "Differential Equations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electromagnetic Theory", "to": "Basic Electricity", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electromagnetic Theory", "to": "Wave Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electron Configuration", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electron Configuration", "to": "Quantum Mechanics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electron Configuration", "to": "Periodic Table", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electron Configuration", "to": "Chemical Bonding", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Conservation", "to": "Basic Physics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Conservation", "to": "Thermodynamics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Conservation", "to": "Energy Types", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Conservation", "to": "Environmental Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Equation Solving", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Equation Solving", "to": "Algebraic Expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Equation Solving", "to": "Inequalities", "width": 2}, {"arrows": "to", "color": "blue", "from": "Equation Solving", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Exponents", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Exponents", "to": "Multiplication Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Exponents", "to": "Order of Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Feedback Loops", "to": "Systems Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Feedback Loops", "to": "Dynamic Systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Feedback Loops", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Feedback Loops", "to": "Statistical Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Force Analysis", "to": "Newton\u0027s Laws", "width": 2}, {"arrows": "to", "color": "blue", "from": "Force Analysis", "to": "Vector Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Force Analysis", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Force Analysis", "to": "Static Equilibrium", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions", "to": "Graphing Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Functions", "to": "Logical Operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometric Shapes", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometric Shapes", "to": "Understanding of Angles", "width": 2}, {"arrows": "to", "color": "blue", "from": "Geometric Shapes", "to": "Introduction to Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graph Interpretation", "to": "Data Visualization", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graph Interpretation", "to": "Basic Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graph Interpretation", "to": "Graph Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graph Interpretation", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing", "to": "Coordinate Systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing", "to": "Algebraic Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing", "to": "Data Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Hand Tool Use", "to": "Tool Safety", "width": 2}, {"arrows": "to", "color": "blue", "from": "Hand Tool Use", "to": "Basic Measurements", "width": 2}, {"arrows": "to", "color": "blue", "from": "Hand Tool Use", "to": "Material Properties", "width": 2}, {"arrows": "to", "color": "blue", "from": "Hand Tool Use", "to": "Hand-Eye Coordination", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Graphing", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits", "to": "Inequalities", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Reasoning", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Reasoning", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Reasoning", "to": "Argument Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Reasoning", "to": "Logical Operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Logical Operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematics", "to": "Numerical Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Matrix Operations", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Matrix Operations", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Matrix Operations", "to": "Linear Equations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Matrix Operations", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Concepts", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Concepts", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Concepts", "to": "Measurement Units", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Concepts", "to": "Scientific Notation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Skills", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Skills", "to": "Unit Conversion", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Skills", "to": "Data Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Skills", "to": "Measurement Tools", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mechanical Energy", "to": "Kinematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mechanical Energy", "to": "Dynamics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mechanical Energy", "to": "Work-Energy Principle", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mechanical Energy", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Modeling Techniques", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Modeling Techniques", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Modeling Techniques", "to": "Programming Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Modeling Techniques", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Multivariable Functions", "to": "Single Variable Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Multivariable Functions", "to": "Basic Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Multivariable Functions", "to": "Coordinate Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Multivariable Functions", "to": "Limits and Continuity", "width": 2}, {"arrows": "to", "color": "blue", "from": "Newton\u0027s Laws", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Newton\u0027s Laws", "to": "Force Concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Newton\u0027s Laws", "to": "Motion Principles", "width": 2}, {"arrows": "to", "color": "blue", "from": "Newton\u0027s Laws", "to": "Vector Understanding", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Properties", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Properties", "to": "Arithmetic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Properties", "to": "Understanding of Integers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Properties", "to": "Rational Numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Observation Skills", "to": "Attention to Detail", "width": 2}, {"arrows": "to", "color": "blue", "from": "Observation Skills", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Observation Skills", "to": "Analytical Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Observation Skills", "to": "Communication Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Periodic Table", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Periodic Table", "to": "Chemical Bonds", "width": 2}, {"arrows": "to", "color": "blue", "from": "Periodic Table", "to": "Element Properties", "width": 2}, {"arrows": "to", "color": "blue", "from": "Periodic Table", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Probability Theory", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Basics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Basics", "to": "Logical Operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Basics", "to": "Problem-Solving Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Safety Practices", "to": "Risk Assessment", "width": 2}, {"arrows": "to", "color": "blue", "from": "Safety Practices", "to": "Emergency Procedures", "width": 2}, {"arrows": "to", "color": "blue", "from": "Safety Practices", "to": "First Aid Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Safety Practices", "to": "Health Regulations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Spatial Reasoning", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Spatial Reasoning", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Spatial Reasoning", "to": "Logical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Spatial Reasoning", "to": "Visualization Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Stoichiometry", "to": "Chemical Equations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Stoichiometry", "to": "Mole Concept", "width": 2}, {"arrows": "to", "color": "blue", "from": "Stoichiometry", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Stoichiometry", "to": "Balancing Reactions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symbol Recognition", "to": "Pattern Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symbol Recognition", "to": "Image Processing", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symbol Recognition", "to": "Machine Learning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symbol Recognition", "to": "Cognitive Psychology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symmetry Operations", "to": "Basic Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symmetry Operations", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symmetry Operations", "to": "Group Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symmetry Operations", "to": "Mathematical Transformations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Systems Thinking", "to": "Systems Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Systems Thinking", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Systems Thinking", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Systems Thinking", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Trigonometry", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Trigonometry", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Trigonometry", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Symbols", "to": "Basic Literacy", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Symbols", "to": "Mathematical Symbols", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Symbols", "to": "Logical Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Symbols", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Unit Cells", "to": "Crystallography", "width": 2}, {"arrows": "to", "color": "blue", "from": "Unit Cells", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Unit Cells", "to": "Symmetry Principles", "width": 2}, {"arrows": "to", "color": "blue", "from": "Unit Cells", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Unit Conversion", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Unit Conversion", "to": "Measurement Concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Unit Conversion", "to": "Dimensional Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Addition", "to": "Vector Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Addition", "to": "Coordinate Systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Addition", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic Functions", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Algebraic Functions", "to": "Algebraic Expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Analytical Skills", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Analytical Skills", "to": "Logical Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Analytical Skills", "to": "Data Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Analytical Skills", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Argument Analysis", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Argument Analysis", "to": "Logical Operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Argument Evaluation", "to": "Logical Operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Argument Evaluation", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Attention to Detail", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Attention to Detail", "to": "Observation Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Balancing Equations", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Balancing Reactions", "to": "Chemical Equations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Balancing Reactions", "to": "Stoichiometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Balancing Reactions", "to": "Basic Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Balancing Reactions", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Algebra", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Algebra", "to": "Arithmetic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Measurements", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Measurements", "to": "Unit Conversion", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Measurements", "to": "Measurement Tools", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Statistics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Statistics", "to": "Data Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Bonds", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Bonds", "to": "Periodic Table", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Bonds", "to": "Chemical Reactions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Bonds", "to": "Electronegativity", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Equations", "to": "Balancing Equations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Equations", "to": "Stoichiometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Equations", "to": "Basic Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Cognitive Psychology", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate Geometry", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate Geometry", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate Geometry", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate Geometry", "to": "Graphing Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate Systems", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate Systems", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Coordinate Systems", "to": "Graphing Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Crystallography", "to": "Basic Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Crystallography", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Crystallography", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Interpretation", "to": "Basic Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Interpretation", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Visualization", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Data Visualization", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Dimensional Analysis", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Dimensional Analysis", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Dynamic Systems", "to": "Differential Equations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Dynamic Systems", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Dynamic Systems", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Dynamic Systems", "to": "Basic Physics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Dynamics", "to": "Newton\u0027s Laws", "width": 2}, {"arrows": "to", "color": "blue", "from": "Dynamics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Dynamics", "to": "Vector Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Dynamics", "to": "Kinematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electronegativity", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electronegativity", "to": "Chemical Bonding", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electronegativity", "to": "Periodic Table", "width": 2}, {"arrows": "to", "color": "blue", "from": "Element Properties", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Element Properties", "to": "Periodic Table", "width": 2}, {"arrows": "to", "color": "blue", "from": "Element Properties", "to": "Chemical Bonds", "width": 2}, {"arrows": "to", "color": "blue", "from": "Element Properties", "to": "Basic Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Emergency Procedures", "to": "Risk Assessment", "width": 2}, {"arrows": "to", "color": "blue", "from": "Emergency Procedures", "to": "First Aid Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Environmental Science", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Force Concepts", "to": "Newton\u0027s Laws", "width": 2}, {"arrows": "to", "color": "blue", "from": "Force Concepts", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Force Concepts", "to": "Vector Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Force Concepts", "to": "Kinematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graph Theory", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graph Theory", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing Skills", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing Skills", "to": "Data Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing Skills", "to": "Coordinate Systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Graphing Skills", "to": "Algebra Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Group Theory", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Group Theory", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Image Processing", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Image Processing", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Image Processing", "to": "Programming Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Image Processing", "to": "Signal Processing", "width": 2}, {"arrows": "to", "color": "blue", "from": "Imaginary Numbers", "to": "Complex Numbers", "width": 2}, {"arrows": "to", "color": "blue", "from": "Imaginary Numbers", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Imaginary Numbers", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Inequalities", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Inequalities", "to": "Graphing Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to Geometry", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to Geometry", "to": "Algebra Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Introduction to Geometry", "to": "Logical Reasoning", "width": 2}, {"arrows": "to", "color": "blue", "from": "Limits and Continuity", "to": "Inequalities", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Equations", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Equations", "to": "Algebraic Expressions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Equations", "to": "Graphing Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Linear Equations", "to": "Arithmetic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Operators", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Operators", "to": "Programming Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Thinking", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Thinking", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Thinking", "to": "Problem Solving", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Thinking", "to": "Analytical Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Programming Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Machine Learning", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Material Properties", "to": "Basic Chemistry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Material Properties", "to": "Physics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Material Properties", "to": "Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Symbols", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Symbols", "to": "Logical Operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Symbols", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Transformations", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Transformations", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Transformations", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mathematical Transformations", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Tools", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Tools", "to": "Data Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Tools", "to": "Scientific Method", "width": 2}, {"arrows": "to", "color": "blue", "from": "Measurement Tools", "to": "Measurement Concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mole Concept", "to": "Atomic Structure", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mole Concept", "to": "Chemical Reactions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mole Concept", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Mole Concept", "to": "Stoichiometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motion Principles", "to": "Newton\u0027s Laws", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motion Principles", "to": "Kinematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motion Principles", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motion Principles", "to": "Vector Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Multiplication Skills", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Multiplication Skills", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Numerical Skills", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Numerical Skills", "to": "Arithmetic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Numerical Skills", "to": "Data Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Numerical Skills", "to": "Algebra Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "One-to-One Correspondence", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "One-to-One Correspondence", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "One-to-One Correspondence", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Order of Operations", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Order of Operations", "to": "Arithmetic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Ordinal Numbers", "to": "Set Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Pattern Recognition", "to": "Statistics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Pattern Recognition", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Pattern Recognition", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Pattern Recognition", "to": "Programming Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Place Value", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Place Value", "to": "Decimal System", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Analytical Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem Solving", "to": "Logical Operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem-Solving Skills", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem-Solving Skills", "to": "Analytical Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Problem-Solving Skills", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Quantum Mechanics", "to": "Classical Mechanics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Quantum Mechanics", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Quantum Mechanics", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Quantum Mechanics", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Rational Numbers", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Risk Assessment", "to": "Statistical Analysis", "width": 2}, {"arrows": "to", "color": "blue", "from": "Risk Assessment", "to": "Data Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Risk Assessment", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Logical Operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Set Theory", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Single Variable Calculus", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Single Variable Calculus", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Single Variable Calculus", "to": "Trigonometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Single Variable Calculus", "to": "Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Analysis", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Analysis", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistical Analysis", "to": "Data Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Data Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Statistics", "to": "Probability Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symmetry Principles", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symmetry Principles", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symmetry Principles", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Symmetry Principles", "to": "Group Theory", "width": 2}, {"arrows": "to", "color": "blue", "from": "Tool Safety", "to": "Risk Assessment", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Angles", "to": "Basic Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Understanding of Angles", "to": "Measurement Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Variables", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Variables", "to": "Programming Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Basics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Basics", "to": "Coordinate Systems", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Basics", "to": "Algebra Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Calculus", "to": "Calculus", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Calculus", "to": "Linear Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Calculus", "to": "Multivariable Functions", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Understanding", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Understanding", "to": "Algebra", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Understanding", "to": "Geometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Vector Understanding", "to": "Trigonometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Visualization Skills", "to": "Critical Thinking", "width": 2}, {"arrows": "to", "color": "blue", "from": "Visualization Skills", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Visualization Skills", "to": "Data Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Wave Theory", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Wave Theory", "to": "Physics Fundamentals", "width": 2}, {"arrows": "to", "color": "blue", "from": "Wave Theory", "to": "Trigonometry", "width": 2}, {"arrows": "to", "color": "blue", "from": "Wave Theory", "to": "Graphing Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Waveform Analysis", "to": "Signal Processing", "width": 2}, {"arrows": "to", "color": "blue", "from": "Waveform Analysis", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Waveform Analysis", "to": "Graph Interpretation", "width": 2}, {"arrows": "to", "color": "blue", "from": "Work-Energy Principle", "to": "Newton\u0027s Laws", "width": 2}, {"arrows": "to", "color": "blue", "from": "Work-Energy Principle", "to": "Kinematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Work-Energy Principle", "to": "Basic Mathematics", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": false}, "layout": {"hierarchical": {"enabled": true, "direction": "UD", "sortMethod": "directed"}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
</body>
</html>
        