
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Topic Tree Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #555;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .graph-container {
            position: relative;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .graph-container.expanded {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            z-index: 1000;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        .graph-container.expanded .graph-content {
            height: calc(100vh - 120px) !important;
        }
        /* Target all pyvis network elements for proper expansion */
        .graph-container.expanded .graph-content > div,
        .graph-container.expanded #mynetworkid,
        .graph-container.expanded .card-body,
        .graph-container.expanded #vis-network,
        .graph-container.expanded canvas {
            height: 100% !important;
            width: 100% !important;
        }
        /* Ensure pyvis container divs expand properly */
        .graph-content > div,
        #mynetworkid,
        .card-body,
        #vis-network {
            height: 100%;
            width: 100%;
        }
        /* Make sure canvas fills its container */
        canvas {
            display: block;
        }
        .graph-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 10;
            display: flex;
            gap: 5px;
        }
        .control-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        .control-btn:hover {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .graph-content {
            height: 500px;
            transition: height 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        /* Ensure pyvis elements fill the graph-content container */
        .graph-content > div {
            height: 100% !important;
            width: 100% !important;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f0f0;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }
        .overlay.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="overlay" id="overlay" onclick="toggleExpand()"></div>

    <div class="container">
        <h1>🌳 Topic Tree Visualization</h1>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">17</div>
                <div class="stat-label">Total Topics</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">6</div>
                <div class="stat-label">Depth Levels</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">17</div>
                <div class="stat-label">Topics with Prerequisites</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Interactive Graph</h2>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff6b6b;"></div>
                    <span>Depth 0 (Root)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #4ecdc4;"></div>
                    <span>Depth 1</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #45b7d1;"></div>
                    <span>Depth 2</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #96ceb4;"></div>
                    <span>Depth 3</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #feca57;"></div>
                    <span>Depth 4</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background-color: #ff9ff3;"></div>
                    <span>Depth 5</span>
                </div>
            </div>

            <div class="graph-container" id="graphContainer">
                <div class="graph-controls">
                    <button class="control-btn" onclick="toggleExpand()" id="expandBtn">
                        🔍 Expand
                    </button>
                    <button class="control-btn" onclick="resetGraph()">
                        🔄 Reset View
                    </button>
                </div>
                <div class="graph-content">
                    <div id="mynetwork" class="card-body"></div>
                </div>
            </div>
        </div>

        
        <div class="section">
            <h2>📋 Topic Details Table</h2>
            <table>
                <thead>
                    <tr>
                        <th>Depth</th>
                        <th>Term</th>
                        <th>Description</th>
                        <th>Prerequisites</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td style="background-color: #ff6b6b; color: white; font-weight: bold; text-align: center;">0</td>
                    <td><strong>Battery Electric Vehicles</strong></td>
                    <td>Battery Electric Vehicles (BEVs) are automobiles that are powered entirely by electricity stored in batteries, utilizing electric motors for propulsion. They are considered a key technology in reducing greenhouse gas emissions and reliance on fossil fuels.</td>
                    <td>Electricity Basics, Battery Technology, Motor Principles, Energy Storage, Sustainability Concepts</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Battery Technology</strong></td>
                    <td>Battery technology encompasses the study and development of energy storage systems, focusing on the chemical and physical principles that enable batteries to store and release electrical energy efficiently.</td>
                    <td>Electricity Basics, Basic Mathematics, Chemical Principles</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Electricity Basics</strong></td>
                    <td>Electricity Basics covers fundamental concepts related to electric charge, current, voltage, and resistance, providing a foundational understanding of how electrical systems operate.</td>
                    <td>Basic Mathematics, Physical Science, Electrical Circuits</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Energy Storage</strong></td>
                    <td>Energy storage refers to the capture of energy produced at one time for use at a later time, utilizing various technologies such as batteries, pumped hydro, and thermal storage. It plays a crucial role in balancing supply and demand in energy systems.</td>
                    <td>Electricity Basics, Battery Technology, Motor Principles</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Motor Principles</strong></td>
                    <td>Motor Principles encompass the fundamental concepts and mechanisms that govern the operation of electric motors, including the conversion of electrical energy into mechanical energy. Understanding these principles is essential for designing and analyzing motor systems in various applications.</td>
                    <td>Basic Mathematics, Electricity Basics, Battery Technology</td>
                </tr>
                
                <tr>
                    <td style="background-color: #4ecdc4; color: white; font-weight: bold; text-align: center;">1</td>
                    <td><strong>Sustainability Concepts</strong></td>
                    <td>Sustainability Concepts encompass the principles and practices aimed at meeting current needs without compromising the ability of future generations to meet theirs, focusing on environmental, social, and economic dimensions.</td>
                    <td>Basic Mathematics, Energy Storage, Electricity Basics, Motor Principles</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Basic Mathematics</strong></td>
                    <td>Basic Mathematics encompasses fundamental arithmetic operations, number theory, and introductory algebra concepts essential for everyday problem-solving and further mathematical studies.</td>
                    <td>Number Recognition, Basic Operations, Fractions Understanding, Decimals Understanding</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Chemical Principles</strong></td>
                    <td>Chemical Principles encompass the fundamental concepts and theories that govern the behavior of matter, including atomic structure, chemical bonding, and reactions. This topic serves as a foundation for understanding more complex chemical phenomena and applications.</td>
                    <td>Basic Mathematics, Physical Science, Electricity Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Electrical Circuits</strong></td>
                    <td>Electrical circuits are pathways through which electric current flows, consisting of components such as resistors, capacitors, and inductors, and are fundamental to understanding electrical engineering and electronics.</td>
                    <td>Basic Mathematics, Electricity Basics, Physical Science</td>
                </tr>
                
                <tr>
                    <td style="background-color: #45b7d1; color: white; font-weight: bold; text-align: center;">2</td>
                    <td><strong>Physical Science</strong></td>
                    <td>Physical Science is the study of non-living systems, encompassing fields such as physics, chemistry, and earth sciences to understand the fundamental principles governing the physical world.</td>
                    <td>Basic Mathematics, Electricity Basics, Energy Storage, Sustainability Concepts</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Basic Operations</strong></td>
                    <td>Basic operations refer to fundamental mathematical processes such as addition, subtraction, multiplication, and division that form the foundation for more complex calculations and problem-solving.</td>
                    <td>Basic Mathematics, Number Recognition</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Decimals Understanding</strong></td>
                    <td>Decimals Understanding involves grasping the concept of decimal numbers, their representation, and their operations in relation to whole numbers and fractions. It is essential for performing calculations and comparisons involving non-integer values.</td>
                    <td>Basic Mathematics, Fractions Understanding, Number Recognition</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Fractions Understanding</strong></td>
                    <td>Fractions Understanding involves comprehending the concept of parts of a whole, including how to represent, compare, and perform operations with fractions. Mastery of this topic is essential for more advanced mathematical concepts and applications.</td>
                    <td>Basic Mathematics, Number Recognition, Basic Operations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #96ceb4; color: white; font-weight: bold; text-align: center;">3</td>
                    <td><strong>Number Recognition</strong></td>
                    <td>Number recognition is the ability to identify and understand numerical symbols, which is a fundamental skill in early mathematics education. It serves as a building block for more advanced mathematical concepts and operations.</td>
                    <td>Basic Mathematics, Logical Operators, Counting Skills</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Counting Skills</strong></td>
                    <td>Counting skills involve the ability to recognize, understand, and manipulate numbers in order to perform basic counting tasks. These skills are foundational for further mathematical learning and everyday problem-solving.</td>
                    <td>Number Recognition, Basic Mathematics, Basic Operations</td>
                </tr>
                
                <tr>
                    <td style="background-color: #feca57; color: white; font-weight: bold; text-align: center;">4</td>
                    <td><strong>Logical Operators</strong></td>
                    <td>Logical operators are symbols or words used to connect two or more expressions in logic, enabling the construction of complex logical statements. They are fundamental in programming and mathematical logic, allowing for decision-making processes based on true or false evaluations.</td>
                    <td>Basic Mathematics, Programming Basics</td>
                </tr>
                
                <tr>
                    <td style="background-color: #ff9ff3; color: white; font-weight: bold; text-align: center;">5</td>
                    <td><strong>Programming Basics</strong></td>
                    <td>Programming Basics introduces fundamental concepts and techniques for writing simple programs, focusing on syntax, logic, and problem-solving skills. It serves as a foundation for further study in computer programming and software development.</td>
                    <td>Basic Mathematics, Logical Operators, IDE Experience</td>
                </tr>
                
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function toggleExpand() {
            const container = document.getElementById('graphContainer');
            const overlay = document.getElementById('overlay');
            const expandBtn = document.getElementById('expandBtn');

            if (container.classList.contains('expanded')) {
                container.classList.remove('expanded');
                overlay.classList.remove('active');
                expandBtn.innerHTML = '🔍 Expand';
                document.body.style.overflow = 'auto';

                // Force resize of all network elements after collapse
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            } else {
                container.classList.add('expanded');
                overlay.classList.add('active');
                expandBtn.innerHTML = '❌ Close';
                document.body.style.overflow = 'hidden';

                // Force resize of all network elements after expansion
                setTimeout(() => {
                    resizeNetworkElements();
                }, 300);
            }
        }

        function resizeNetworkElements() {
            // Force resize of all pyvis network elements
            const networkElements = [
                document.getElementById('mynetworkid'),
                document.querySelector('.card-body'),
                document.querySelector('#vis-network'),
                document.querySelector('canvas')
            ];

            networkElements.forEach(element => {
                if (element) {
                    // Trigger a reflow by temporarily changing display
                    const originalDisplay = element.style.display;
                    element.style.display = 'none';
                    element.offsetHeight; // Trigger reflow
                    element.style.display = originalDisplay;
                }
            });

            // Redraw and fit the network
            if (window.network) {
                if (window.network.redraw) {
                    window.network.redraw();
                }
                if (window.network.fit) {
                    window.network.fit();
                }
                // Force canvas resize if available
                if (window.network.canvas && window.network.canvas.setSize) {
                    const container = document.querySelector('.graph-content');
                    if (container) {
                        window.network.canvas.setSize(container.offsetWidth, container.offsetHeight);
                    }
                }
            }
        }

        function resetGraph() {
            // Reset the network view and ensure proper sizing
            resizeNetworkElements();
        }

        // Close expanded view with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const container = document.getElementById('graphContainer');
                if (container.classList.contains('expanded')) {
                    toggleExpand();
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            setTimeout(() => {
                resizeNetworkElements();
            }, 100);
        });

        // Initial resize after page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                resizeNetworkElements();
            }, 500);
        });
    </script>

    <script src="lib/bindings/utils.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>
<script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>
<script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"color": "#ff6b6b", "font": {"color": "black"}, "id": "Battery Electric Vehicles", "label": "Battery Electric Vehicles", "shape": "dot", "size": 45, "title": "Depth: 0\nDescription: Battery Electric Vehicles (BEVs) are automobiles that are powered entirely by electricity stored in batteries, utilizing electric motors for propulsion. They are considered a key technology in reducing greenhouse gas emissions and reliance on fossil fuels."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Battery Technology", "label": "Battery Technology", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Battery technology encompasses the study and development of energy storage systems, focusing on the chemical and physical principles that enable batteries to store and release electrical energy efficiently."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Electricity Basics", "label": "Electricity Basics", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Electricity Basics covers fundamental concepts related to electric charge, current, voltage, and resistance, providing a foundational understanding of how electrical systems operate."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Energy Storage", "label": "Energy Storage", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Energy storage refers to the capture of energy produced at one time for use at a later time, utilizing various technologies such as batteries, pumped hydro, and thermal storage. It plays a crucial role in balancing supply and demand in energy systems."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Motor Principles", "label": "Motor Principles", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Motor Principles encompass the fundamental concepts and mechanisms that govern the operation of electric motors, including the conversion of electrical energy into mechanical energy. Understanding these principles is essential for designing and analyzing motor systems in various applications."}, {"color": "#4ecdc4", "font": {"color": "black"}, "id": "Sustainability Concepts", "label": "Sustainability Concepts", "shape": "dot", "size": 40, "title": "Depth: 1\nDescription: Sustainability Concepts encompass the principles and practices aimed at meeting current needs without compromising the ability of future generations to meet theirs, focusing on environmental, social, and economic dimensions."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Basic Mathematics", "label": "Basic Mathematics", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Basic Mathematics encompasses fundamental arithmetic operations, number theory, and introductory algebra concepts essential for everyday problem-solving and further mathematical studies."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Chemical Principles", "label": "Chemical Principles", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Chemical Principles encompass the fundamental concepts and theories that govern the behavior of matter, including atomic structure, chemical bonding, and reactions. This topic serves as a foundation for understanding more complex chemical phenomena and applications."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Electrical Circuits", "label": "Electrical Circuits", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Electrical circuits are pathways through which electric current flows, consisting of components such as resistors, capacitors, and inductors, and are fundamental to understanding electrical engineering and electronics."}, {"color": "#45b7d1", "font": {"color": "black"}, "id": "Physical Science", "label": "Physical Science", "shape": "dot", "size": 35, "title": "Depth: 2\nDescription: Physical Science is the study of non-living systems, encompassing fields such as physics, chemistry, and earth sciences to understand the fundamental principles governing the physical world."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Basic Operations", "label": "Basic Operations", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Basic operations refer to fundamental mathematical processes such as addition, subtraction, multiplication, and division that form the foundation for more complex calculations and problem-solving."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Decimals Understanding", "label": "Decimals Understanding", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Decimals Understanding involves grasping the concept of decimal numbers, their representation, and their operations in relation to whole numbers and fractions. It is essential for performing calculations and comparisons involving non-integer values."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Fractions Understanding", "label": "Fractions Understanding", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Fractions Understanding involves comprehending the concept of parts of a whole, including how to represent, compare, and perform operations with fractions. Mastery of this topic is essential for more advanced mathematical concepts and applications."}, {"color": "#96ceb4", "font": {"color": "black"}, "id": "Number Recognition", "label": "Number Recognition", "shape": "dot", "size": 30, "title": "Depth: 3\nDescription: Number recognition is the ability to identify and understand numerical symbols, which is a fundamental skill in early mathematics education. It serves as a building block for more advanced mathematical concepts and operations."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Counting Skills", "label": "Counting Skills", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Counting skills involve the ability to recognize, understand, and manipulate numbers in order to perform basic counting tasks. These skills are foundational for further mathematical learning and everyday problem-solving."}, {"color": "#feca57", "font": {"color": "black"}, "id": "Logical Operators", "label": "Logical Operators", "shape": "dot", "size": 25, "title": "Depth: 4\nDescription: Logical operators are symbols or words used to connect two or more expressions in logic, enabling the construction of complex logical statements. They are fundamental in programming and mathematical logic, allowing for decision-making processes based on true or false evaluations."}, {"color": "#ff9ff3", "font": {"color": "black"}, "id": "Programming Basics", "label": "Programming Basics", "shape": "dot", "size": 20, "title": "Depth: 5\nDescription: Programming Basics introduces fundamental concepts and techniques for writing simple programs, focusing on syntax, logic, and problem-solving skills. It serves as a foundation for further study in computer programming and software development."}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "blue", "from": "Battery Electric Vehicles", "to": "Electricity Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Electric Vehicles", "to": "Battery Technology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Electric Vehicles", "to": "Motor Principles", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Electric Vehicles", "to": "Energy Storage", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Electric Vehicles", "to": "Sustainability Concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Technology", "to": "Electricity Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Technology", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Battery Technology", "to": "Chemical Principles", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electricity Basics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electricity Basics", "to": "Physical Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electricity Basics", "to": "Electrical Circuits", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Storage", "to": "Electricity Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Storage", "to": "Battery Technology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Energy Storage", "to": "Motor Principles", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motor Principles", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motor Principles", "to": "Electricity Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Motor Principles", "to": "Battery Technology", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sustainability Concepts", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sustainability Concepts", "to": "Energy Storage", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sustainability Concepts", "to": "Electricity Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Sustainability Concepts", "to": "Motor Principles", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Mathematics", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Mathematics", "to": "Basic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Mathematics", "to": "Fractions Understanding", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Mathematics", "to": "Decimals Understanding", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Principles", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Principles", "to": "Physical Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Chemical Principles", "to": "Electricity Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Circuits", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Circuits", "to": "Electricity Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Electrical Circuits", "to": "Physical Science", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physical Science", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physical Science", "to": "Electricity Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physical Science", "to": "Energy Storage", "width": 2}, {"arrows": "to", "color": "blue", "from": "Physical Science", "to": "Sustainability Concepts", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Operations", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Basic Operations", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Decimals Understanding", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Decimals Understanding", "to": "Fractions Understanding", "width": 2}, {"arrows": "to", "color": "blue", "from": "Decimals Understanding", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Fractions Understanding", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Fractions Understanding", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Fractions Understanding", "to": "Basic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Recognition", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Recognition", "to": "Logical Operators", "width": 2}, {"arrows": "to", "color": "blue", "from": "Number Recognition", "to": "Counting Skills", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting Skills", "to": "Number Recognition", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting Skills", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Counting Skills", "to": "Basic Operations", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Operators", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Logical Operators", "to": "Programming Basics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Basics", "to": "Basic Mathematics", "width": 2}, {"arrows": "to", "color": "blue", "from": "Programming Basics", "to": "Logical Operators", "width": 2}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 100}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
</body>
</html>
        